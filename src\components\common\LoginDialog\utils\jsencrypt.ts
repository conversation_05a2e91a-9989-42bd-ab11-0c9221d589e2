import JSEncrypt from 'jsencrypt'

// 从环境变量获取公钥
const publicKey = import.meta.env.VITE_APP_RSA_PUBLIC_KEY

// 加密
export function encrypt(txt: string): string {
  try {
    const encryptor = new JSEncrypt()
    // 移除可能的换行和空格
    const key = publicKey.replace(/[\n\r\s]/g, '')
    encryptor.setPublicKey(key)
    const encrypted = encryptor.encrypt(txt)
    if (!encrypted) {
      console.error('RSA加密失败，请检查公钥是否正确')
      throw new Error('加密失败')
    }
    return encrypted
  }
  catch (error) {
    console.error('RSA加密错误:', error)
    throw new Error('加密失败')
  }
}
