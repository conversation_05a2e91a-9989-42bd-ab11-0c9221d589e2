import { getResourceOssUsedDistSize } from "@/api/common"

export function useStorageUsage() {
  const usedSize = ref<number>(0)
  const usagePercentage = ref<number>(0)
  const totalSize = ref<number>(Infinity)

  // 获取已用存储空间大小
  const getStorageUsage = async () => {
    try {
      const response = await getResourceOssUsedDistSize<{
        code: number
        data: string
        msg: string
      }>({})
      if (response && response.code === 200 && response.data) {
        usedSize.value = response.data ? +response.data : 0
        // 如果总容量为无限，计算百分比为50%（显示效果）
        usagePercentage.value =
          totalSize.value === Infinity ? 30 : (usedSize.value / totalSize.value) * 100
      }
    } catch (error) {
      console.error('Failed to get storage usage', error)
    }
  }
  getStorageUsage()

  return {
    usedSize,
    totalSize,
    usagePercentage,
  }
}
