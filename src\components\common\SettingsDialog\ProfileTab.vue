<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/store'
import { putSystemUserProfile } from '@/api/system'
import UserAvatar from '@/views/settings/user/profile/userAvatar.vue'

const userStore = useUserStore()
const router = useRouter()

// 用户信息
const userInfo = computed(() => userStore.userInfo)

// 用户头像
const userAvatarRef = ref()

// 调用头像编辑器
function handleEditAvatar() {
  userAvatarRef.value?.editCropper()
}

// 生成头像显示字符
const avatarText = computed(() => {
  if (!userInfo.value?.nickName) return ''
  return userInfo.value.nickName.charAt(0)
})

// 根据姓名生成随机颜色
const avatarColor = computed(() => {
  if (!userInfo.value?.nickName) return '#4c5cec'

  // 简单哈希函数生成颜色
  let hash = 0
  for (let i = 0; i < userInfo.value.nickName.length; i++) {
    hash = userInfo.value.nickName.charCodeAt(i) + ((hash << 5) - hash)
  }

  // 固定一些好看的颜色
  const colors = ['#4c5cec']

  const index = Math.abs(hash) % colors.length
  return colors[index]
})

// 编辑姓名相关
const nameDialogVisible = ref(false)
const newName = ref('')

// 打开编辑姓名对话框
function openEditNameDialog() {
  newName.value = userInfo.value?.nickName || ''
  nameDialogVisible.value = true
}

// 提交更新用户姓名
async function submitUpdateName() {
  if (!newName.value.trim()) {
    ElMessage.warning('姓名不能为空')
    return
  }

  try {
    await putSystemUserProfile<any>({
      nickName: newName.value.trim(),
    })

    ElMessage.success('更新成功')
    // 更新用户信息
    await userStore.getInfo()
    nameDialogVisible.value = false
  } catch (error) {
    ElMessage.error('更新失败')
  }
}

// 处理退出登录
async function handleLogout() {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    await userStore.logout()
    ElMessage.success('退出成功')
    router.push('/login')
  } catch (error) {
    // 用户取消操作，不执行任何动作
  }
}
</script>

<template>
  <div>
    <div class="section-header">
      <div class="section-title">个人资料</div>
    </div>

    <div class="profile-item flex items-center">
      <div class="item-label">头像</div>
      <div class="avatar-container">
        <UserAvatar
          v-show="userInfo?.avatar"
          ref="userAvatarRef"
          @avatar-updated="userStore.getInfo()"
        />
        <div
          v-show="!userInfo?.avatar"
          class="avatar-circle"
          :style="{ backgroundColor: avatarColor }"
        >
          <template v-if="userInfo?.nickName">
            <span class="text-[14px]">{{ avatarText }}</span>
          </template>
          <img v-else src="" alt="" />
        </div>
        <div class="cursor-pointer ml-2" @click="handleEditAvatar">
          <i class="iconfont icon-bianji iconfont-c !text-[20px]"></i>
        </div>
      </div>
    </div>

    <div class="profile-item">
      <div class="item-label">姓名</div>
      <div class="item-value">
        {{ userInfo?.nickName }}
        <div class="cursor-pointer ml-2" @click="openEditNameDialog">
          <i class="iconfont icon-bianji iconfont-c !text-[20px]"></i>
        </div>
      </div>
    </div>

    <div class="action-buttons">
      <ElButton class="logout-button" type="danger" plain @click="handleLogout">退出登录</ElButton>
    </div>

    <!-- 企业资料部分 -->
    <div class="company-section">
      <div class="company-header">
        <div class="section-title">企业资料</div>
      </div>

      <div class="profile-item">
        <div class="item-label">所属企业</div>
        <!-- todo 后端更新了，但我没看到，算了吧先 -->
        <div class="item-value">{{ userInfo.tenantName }}</div>
      </div>

      <div class="profile-item">
        <div class="item-label">所属部门</div>
        <div class="item-value">{{ userInfo.deptName }}</div>
      </div>

      <div class="profile-item">
        <div class="item-label">企业角色</div>
        <div class="item-value">
          {{ userInfo.roles.map((role: any) => role.roleName).join(',') }}
        </div>
      </div>
    </div>

    <!-- 编辑姓名对话框 -->
    <ElDialog v-model="nameDialogVisible" title="编辑姓名" width="400px" append-to-body>
      <ElForm>
        <ElFormItem label="姓名">
          <ElInput v-model="newName" placeholder="请输入姓名" />
        </ElFormItem>
      </ElForm>
      <template #footer>
        <span class="dialog-footer">
          <ElButton @click="nameDialogVisible = false">取消</ElButton>
          <ElButton type="primary" @click="submitUpdateName">确定</ElButton>
        </span>
      </template>
    </ElDialog>
  </div>
</template>

<style scoped>
.avatar-container {
  display: flex;
  align-items: center;
}

.avatar-circle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #4c5cec;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.avatar-circle img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.action-buttons {
  margin: 20px 0;
}

.logout-button {
  width: 100%;
  color: #f56c6c;
  border-color: #f56c6c;
}

.company-section {
  margin-top: 40px;
}

.company-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16px;
  margin-bottom: 16px;
  border-bottom: 1px solid #eaeaea;
}
</style>
