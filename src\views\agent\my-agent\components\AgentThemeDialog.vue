<script setup lang="ts">
import { defineEmits, defineProps, onMounted, onUnmounted, ref, watch } from 'vue'
import { Check, Plus } from '@element-plus/icons-vue'
import type { UploadProps } from 'element-plus'
// 移除 Vue3EmojiPicker 导入
// import Vue3EmojiPicker from 'vue3-emoji-picker'
// import 'vue3-emoji-picker/css'
// 导入自定义组件
import EmojiPickerLocal from '@/components/EmojiPickerLocal.vue'
import { getBizAgentCategoryList } from '@/api/agent'

const props = defineProps<{
  visible: boolean
  initialData?: {
    name: string
    description: string
    emoji: string
    backgroundColor: string
    categoryIdList?: string[]
  }
}>()

const emit = defineEmits(['update:visible', 'save'])

const dialogVisible = ref(props.visible)

// 监听visible属性的变化
watch(
  () => props.visible,
  newVal => {
    dialogVisible.value = newVal
    // 当对话框打开时，使用 initialData 初始化表单
    if (newVal && props.initialData) {
      form.value = {
        ...form.value,
        name: props.initialData.name,
        description: props.initialData.description,
        emoji: props.initialData.emoji,
        backgroundColor: props.initialData.backgroundColor,
        categoryIdList: props.initialData.categoryIdList || [],
      }
    }
  },
)

// 监听dialogVisible的变化
watch(dialogVisible, newVal => {
  emit('update:visible', newVal)
})

const form = ref({
  name: props.initialData?.name || '',
  description: props.initialData?.description || '',
  emoji: props.initialData?.emoji || '',
  backgroundColor: props.initialData?.backgroundColor || '#FFEBD4',
  selectedAvatarIndex: -1,
  categoryIdList: props.initialData?.categoryIdList || [],
})

// 定义分类列表
const categoryList = ref<Agent.AgentCategoryVo[]>([])

// 新增：背景色列表
const backgroundColors = [
  '#FFEBD4',
  '#FDD9E2',
  '#FFF0D3',
  '#FFF9CE',
  '#EDFAC6',
  '#DDF0E1',
  '#E4FCF5',
  '#DDF8FF',
  '#D5E9FF',
  '#F2DDF3',
  '#F6DAE2',
  '#cccccc',
  '#000000',
  // '#ffffff',
]

const isWhiteColor = (color: string) => color.toLowerCase() === '#ffffff'

// 新增：处理背景色选择
const handleColorSelect = (color: string) => {
  form.value.backgroundColor = color
}

// 新增：处理生成头像选择
const handleAvatarSelect = (index: number) => {
  form.value.selectedAvatarIndex = index
}

// 新增：处理优化按钮点击
const handleOptimize = () => {
  // 这里可以添加生成头像的逻辑
  console.log('优化头像')
}

const handleClose = () => {
  emit('update:visible', false)
}

const handleSave = () => {
  // 表单验证
  if (!form.value.name.trim()) {
    ElMessage.warning('请输入助手名称')
    return
  }

  if (!form.value.description.trim()) {
    ElMessage.warning('请输入助手功能介绍')
    return
  }

  if (!form.value.emoji) {
    ElMessage.warning('请选择图标')
    return
  }

  if (!form.value.categoryIdList?.length) {
    ElMessage.warning('请选择助手分类')
    return
  }

  emit('save', {
    name: form.value.name,
    description: form.value.description,
    emoji: form.value.emoji,
    backgroundColor: form.value.backgroundColor,
    categoryIdList: form.value.categoryIdList,
  })
  handleClose()
}

const handleAvatarSuccess: UploadProps['onSuccess'] = response => {
  form.value.emoji = response.url
}

const beforeAvatarUpload: UploadProps['beforeUpload'] = file => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('上传头像图片只能是图片格式!')
  }
  if (!isLt2M) {
    ElMessage.error('上传头像图片大小不能超过 2MB!')
  }
  return isImage && isLt2M
}

const isPickerVisible = ref(false)

// 添加点击事件处理函数
const handleClickOutside = (event: MouseEvent) => {
  const picker = document.querySelector('.emoji-picker-container')
  const button = document.querySelector('.ex-el-button-gray')
  if (
    isPickerVisible.value &&
    picker &&
    !picker.contains(event.target as Node) &&
    !button?.contains(event.target as Node)
  ) {
    isPickerVisible.value = false
  }
}

// 获取分类列表
const getCategoryList = async () => {
  try {
    const res = await getBizAgentCategoryList()
    if (res.code === 200 && res.data) {
      categoryList.value = res.data as Agent.AgentCategoryVo[]
    } else {
      console.error('获取分类列表失败:', res.msg)
    }
  } catch (error) {
    console.error('获取分类列表失败:', error)
  }
}

// 添加和移除事件监听器
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  // 获取分类列表
  getCategoryList()
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// 新增：获取emoji本地路径函数
const getLocalEmojiPath = (emojiCode: string): string => {
  // 使用静态目录下的emoji图片
  return `/static/emoji/${emojiCode}.png`
}

// 修改handleEmojiSelect函数，使用本地路径
const handleEmojiSelect = (emoji: { i: string; n: string[]; r: string; t: string; u: string }) => {
  // 不再使用CDN路径，改为使用本地路径
  // const emojiUrl = `https://cdn.jsdelivr.net/npm/emoji-datasource-apple/img/apple/64/${emoji.u}.png`
  const emojiUrl = getLocalEmojiPath(emoji.u)
  form.value.emoji = emojiUrl
  isPickerVisible.value = false
}
</script>

<template>
  <el-dialog
    draggable
    :model-value="dialogVisible"
    title="助手设置"
    width="1000px"
    :close-on-click-modal="false"
    @update:model-value="dialogVisible = $event"
    @close="handleClose"
  >
    <el-form :model="form" label-width="120px" label-position="top">
      <el-form-item required label="助手名称">
        <el-input
          v-model="form.name"
          show-word-limit
          maxlength="200"
          placeholder="请输入助手名称"
        />
      </el-form-item>
      <el-form-item required label="助手分类">
        <el-select
          v-model="form.categoryIdList"
          multiple
          placeholder="请选择助手分类"
          style="width: 100%"
        >
          <el-option
            v-for="item in categoryList"
            :key="item.id"
            :label="item.name"
            :value="item.id!"
          />
        </el-select>
      </el-form-item>
      <el-form-item required label="助手功能介绍">
        <el-input
          v-model="form.description"
          show-word-limit
          maxlength="500"
          type="textarea"
          :rows="8"
          placeholder="请输入助手功能介绍"
        />
      </el-form-item>
      <el-form-item required label="图标">
        <div class="flex justify-center">
          <!-- img-emoji -->
          <div
            class="emoji-wrap !w-[88px] !h-[88px]"
            :style="{ backgroundColor: form.backgroundColor }"
          >
            <img v-if="form.emoji" :src="form.emoji" class="emoji" />
          </div>
          <div class="operation-box">
            <div class="flex ml-[24px]">
              <!-- 使用新的select-icon.svg -->
              <!-- 使用我们的自定义组件替代 Vue3EmojiPicker -->
              <el-button
                class="ex-el-button-gray ex-el-button-img-hover-white"
                type="primary"
                plain
                @click="isPickerVisible = true"
              >
                <img src="@/assets/agent/select-icon.svg" class="w-[20px] h-[20px] mr-[4px] icon" />
                选择图标
              </el-button>
              <!-- 使用本地组件替代 -->
              <EmojiPickerLocal
                :visible="isPickerVisible"
                @select="handleEmojiSelect"
                @close="isPickerVisible = false"
              />
              <!-- <div class="divider-vertical ml-[24px]"></div>
              <div class="optimize-button ml-[24px]" @click="handleOptimize">
                <img src="@/assets/agent/optimize.svg" alt="optimize" />
              </div>
              <div
                v-for="(_, index) in [1, 2]"
                :key="index"
                class="generated-emoji ml-[24px]"
                :class="{ selected: form.selectedAvatarIndex === index }"
                @click="handleAvatarSelect(index)"
              >
                <img src="https://iph.href.lu/300x300?fg=666666&bg=cccccc" alt="optimize" />
                <div v-if="form.selectedAvatarIndex === index" class="mask">
                  <el-icon class="check-icon"><Check /></el-icon>
                </div>
              </div> -->
            </div>
            <!-- 添加头像底色配置 -->
            <!-- 选择样式是在图片上放一个一个白色√ -->
            <!-- 勾加粗一点，并且白色底色上时候不明显，选择白色时候用黑色√ -->
            <!-- 目前选中白色没有对应效果 -->
            <!-- 点击色块选中能给img-emoji添加对应底色 -->
            <!-- resolved selected样式改一下，改为一个蓝色外边框 -->
            <div class="flex gap-[12px] ml-[24px] mt-[16px] items-center">
              <div
                v-for="(color, index) in backgroundColors"
                :key="index"
                class="color-block"
                :class="{ selected: form.backgroundColor === color }"
                @click="handleColorSelect(color)"
              >
                <div class="color-inner" :style="{ backgroundColor: color }"></div>
                <!-- <el-icon v-if="form.backgroundColor === color" class="check-icon">
                  <Check />
                </el-icon> -->
              </div>
              <el-color-picker v-model="form.backgroundColor" />
              <div class="text-[14px] text-[#B6BAC1]">底色配置</div>
            </div>
          </div>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button class="w-[120px]" @click="handleClose">取消</el-button>
        <el-button class="w-[120px]" type="primary" @click="handleSave">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style scoped lang="less">
.emoji-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 88px;
  height: 88px;
}

.emoji-uploader:hover {
  border-color: #409eff;
}

.emoji-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 88px;
  height: 88px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.operation-box {
  .optimize-button {
    cursor: pointer;
    width: 80px;
    height: 40px;
    background: #e9e9fe;
    border-radius: 4px;
    font-size: 14px;
    color: #4865e8;
    line-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 30px;
      height: 30px;
    }
  }

  .divider-vertical {
    width: 1px;
    height: 40px;
    background: #e9e9fe;
  }
  .generated-emoji {
    width: 40px;
    height: 40px;
    display: block;
    cursor: pointer;
    border-radius: 4px;
    overflow: hidden;
    transition: all 0.3s;
    position: relative;

    img {
      width: 40px;
      height: 40px;
    }

    .mask {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.3);
      display: flex;
      align-items: center;
      justify-content: center;

      .check-icon {
        color: white;
        font-size: 20px;
        font-weight: bold;
      }
    }
  }

  .color-block {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    .color-inner {
      width: 100%;
      height: 100%;
      border-radius: 50%;
    }
    &:hover {
      transform: scale(1.1);
    }

    &.selected {
      border: 2px solid #4865e8;
      padding: 2px;
    }

    .check-icon {
      font-size: 20px;
      font-weight: 900;
      color: white;

      &.is-white {
        color: #333333;
      }
    }
  }
}
</style>
