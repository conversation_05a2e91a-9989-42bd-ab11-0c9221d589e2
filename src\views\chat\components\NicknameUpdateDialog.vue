<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store'
import { putSystemUserProfile } from '@/api/system'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  nickname: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update:visible', 'updated'])

const dialogVisible = ref(false)
const newNickname = ref('')
const loading = ref(false)

// 监听visible属性变化
watch(
  () => props.visible,
  val => {
    dialogVisible.value = val
    if (val) {
      newNickname.value = ''
    }
  },
)

// 监听对话框关闭
watch(
  () => dialogVisible.value,
  val => {
    if (!val) {
      emit('update:visible', false)
    }
  },
)

// 提交修改昵称
async function submitUpdateNickname() {
  if (!newNickname.value.trim()) {
    ElMessage.warning('昵称不能为空')
    return
  }

  try {
    loading.value = true
    await putSystemUserProfile({
      nickName: newNickname.value.trim(),
    })

    ElMessage.success('昵称修改成功')
    // 更新用户信息
    const userStore = useUserStore()
    await userStore.getInfo()
    emit('updated')
    dialogVisible.value = false
  } catch (error) {
    ElMessage.error('昵称修改失败')
  } finally {
    loading.value = false
  }
}

// 取消修改
function cancelUpdate() {
  dialogVisible.value = false
}
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    width="400px"
    :close-on-click-modal="false"
    destroy-on-close
    :show-close="false"
  >
    <div class="nickname-dialog-content flex flex-col items-center">
      <p class="text-[24px] mt-[-12px]">欢迎来到 <span class="g-c">Easeidea Ai</span></p>
      <p class="text-[16px] text-[#969BA4] m-3">请告诉我您的姓名</p>
      <ElForm class="w-full">
        <ElFormItem>
          <ElInput
            v-model="newNickname"
            class="h-[64px] !text-[18px]"
            placeholder="请输入新昵称"
            maxlength="30"
            clearable
          />
        </ElFormItem>
      </ElForm>
    </div>
    <template #footer>
      <span class="dialog-footer mb-4">
        <ElButton
          class="w-full h-[40px]"
          type="primary"
          :loading="loading"
          @click="submitUpdateNickname"
          >保存</ElButton
        >
      </span>
    </template>
  </ElDialog>
</template>

<style scoped>
.nickname-dialog-content {
  padding: 10px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
