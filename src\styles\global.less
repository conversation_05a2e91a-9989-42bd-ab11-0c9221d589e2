@import "./variables.less";
@import "./element-ui.less";
@import "./ex-element-ui.less";
@import "./ex-naive-ui.less";

// 精简版字体优先加载（更小更快）
/* 配置阿里巴巴普惠体字体 - 精简版 */
// @font-face {
//   font-family: "Alibaba PuHuiTi";
//   // public\fonts\AlibabaPuHuiTi-3-55-Regular.woff2
//   src: url("../../public/fonts/AlibabaPuHuiTi-3-55-Regular.woff2") format("woff2");
//   font-weight: 400;
//   font-style: normal;
//   font-display: swap;
// }

// @font-face {
//   font-family: "Alibaba PuHuiTi";
//   src: url("../../public/fonts/AlibabaPuHuiTi-3-65-Medium.woff2") format("woff2");
//   font-weight: 500;
//   font-style: normal;
//   font-display: swap;
// }

// @font-face {
//   font-family: "Alibaba PuHuiTi";
//   src: url("../../public/fonts/AlibabaPuHuiTi-3-85-Bold.woff2") format("woff2");
//   font-weight: 700;
//   font-style: normal;
//   font-display: swap;
// }

// 完整版字体加载（在精简版后加载，提供更好的显示效果）
/* 配置阿里巴巴普惠体字体 - 完整版 */
@font-face {
  font-family: "Alibaba PuHuiTi";
  src: url("https://cdn.jsdelivr.net/gh/chinayin/fonts-alibaba-puhuiti-regular@latest/Alibaba-PuHuiTi-Regular.ttf")
    format("truetype");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Alibaba PuHuiTi Medium";
  src: url("https://cdn.jsdelivr.net/gh/chinayin/fonts-alibaba-puhuiti-medium@latest/Alibaba-PuHuiTi-Medium.ttf")
    format("truetype");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Alibaba PuHuiTi Bold";
  src: url("https://cdn.jsdelivr.net/gh/chinayin/fonts-alibaba-puhuiti-bold@latest/Alibaba-PuHuiTi-Bold.ttf")
    format("truetype");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

html,
body,
#app {
  height: 100%;
  overflow-x: hidden;
  color: #30343a;
  font-family:
    "Alibaba PuHuiTi",
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
}

body {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.g-family {
  font-family: "Alibaba PuHuiTi", sans-serif !important;
}
.g-family-medium {
  font-family: "Alibaba PuHuiTi Medium", sans-serif !important;
}
.g-family-bold {
  font-family: "Alibaba PuHuiTi Bold", sans-serif !important;
}

.g-c {
  color: @c;
}

.my-breadcrumb {
  padding: 16px 24px;
  font-size: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.emoji-wrap {
  flex: 0 0 auto;
  width: 72px;
  height: 72px;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  .emoji {
    width: 60% !important;
    height: 60% !important;
    display: block;
  }
}

.g-bg-active {
  background: @bg-active!important;
}

.g-bg-hover {
  &:hover {
    background-color: @bg-active;
  }
}

.ell {
  white-space: nowrap;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
}

.ell-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.ell-3 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.iconfont-c {
  color: @c;
}
.iconfont-hover-c {
  cursor: pointer;
  &:hover {
    color: @c;
  }
}

.model-logo {
  width: 28px;
  height: 28px;
  border-radius: 4px;
}

.model-logo-mini {
  width: 22px;
  height: 22px;
  border-radius: 4px;
}

.display-none {
  display: none;
}

// resolved 把loader效果改小到跟16px的字体符合
// todo 这个动效只有右边的圆点动，左边没动，为什么,是因为loader需要固定在left18px的位置吗
.loader-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
}
.loader {
  width: 12px;
  height: 36px;
  position: relative;
  animation: split 1s ease-in infinite alternate;
}
.loader::before,
.loader::after {
  content: "";
  position: absolute;
  height: 10px;
  width: 10px;
  border-radius: 50%;
  left: 0;
  top: 16px;
  transform: translateX(-2px);
  background: @c;
  opacity: 0.75;
  backdrop-filter: blur(20px);
}

.loader::after {
  left: auto;
  right: 0;
  background: rgba(204, 204, 204, 0.7);
  transform: translateX(2px);
}

@keyframes split {
  0%,
  25% {
    width: 12px;
  }
  100% {
    width: 36px;
  }
}
