<script setup lang="ts">
import { View } from '@element-plus/icons-vue'
import type { CheckboxValueType, TableInstance } from 'element-plus'
import { getBizKbItemList, postBizKbSearchTest } from '@/api/knowledge-base'
import { kbIdKey } from '@/utils/constants'
import type { AIModelVo } from '@/api'
import { getBizAiModelAvailableModels } from '@/api'
import { useSettingStore } from '@/store'

defineOptions({
  name: 'SearchTest',
})

const settingStore = useSettingStore()
const router = useRouter()
const modelName = ref('shanghai')
const tableRef = ref<TableInstance>()
const selectedCount = ref(0)
const isAllSelected = ref(false)
const isIndeterminate = ref(false)
const id = inject(kbIdKey) as string

const fileList = ref<KnowledgeBase.KnowledgeBaseItemVo[]>([])
const totalCount = ref(0)
getBizKbItemList<{ rows: KnowledgeBase.KnowledgeBaseItemVo[] }>({
  kbId: id,
  pageNum: 1,
  pageSize: 99999999,
}).then(res => {
  if (res.code === 200) {
    const tempList = res.rows || []
    fileList.value = tempList.filter(t => t.status === 2)
    totalCount.value = res.total
  }
})

const slectionList = ref<KnowledgeBase.KnowledgeBaseItemVo[]>([])
const handleSelectionChange = (selection: KnowledgeBase.KnowledgeBaseItemVo[]) => {
  slectionList.value = selection
  selectedCount.value = selection.length
  isAllSelected.value = selection.length === fileList.value.length
  isIndeterminate.value = selection.length > 0 && selection.length < fileList.value.length
}

const handleSelectAll = (val: CheckboxValueType) => {
  tableRef.value?.toggleAllSelection()
}

const searchText = ref('')
const maxResults = ref(3)
const minScore = ref(0.5)
const rerankModelId = ref<any>(settingStore?.defaultModel?.rerankModelId)
const rerankSwitch = ref(false)

interface SearchResult {
  text: string
  score: number
}
/**
 * 搜索结果
 */
const searchResultList = ref<SearchResult[]>([])
const searchLoading = ref(false)

function handleSearchTest() {
  // 如果没有输入文本，进行提示，不执行搜索
  if (!searchText.value) {
    ElMessage.warning('请输入检索文本')
    return
  }
  // 如果未选择文本，进行提示，不执行搜索
  if (!slectionList.value.length) {
    ElMessage.warning('请选择文件')
    return
  }
  // minScore: 0-1;kbIdList 和 kbItemIdList 只传一个
  searchResultList.value = []
  searchLoading.value = true
  postBizKbSearchTest<SearchResult[]>({
    maxResults: maxResults.value,
    minScore: minScore.value,
    query: searchText.value,
    rerankModelId: rerankSwitch.value ? rerankModelId.value : undefined,
    // kbIdList: [id],
    kbItemIdList: slectionList.value.map(item => item.id),
  })
    .then(res => {
      if (res.code === 200) {
        searchResultList.value = res.data || []
      }
    })
    .finally(() => {
      searchLoading.value = false
    })
}

/**
 * 跳转到预览页面
 */
function handleView(ossId: string | number) {
  router.push({
    name: 'filePreview',
    params: {
      ossId,
    },
  })
}

/**
 * 获取重排模型列表
 */
function useRerankModelList() {
  const rerankModelList = ref<AIModelVo[]>([])
  function getRerankModelList() {
    getBizAiModelAvailableModels({
      type: 'rerank',
    }).then(res => {
      if (res.code === 200) {
        const tempList = res.data || []
        rerankModelList.value = tempList.filter(t => t.status === 1)
      }
    })
  }
  getRerankModelList()
  return {
    rerankModelList,
  }
}
const { rerankModelList } = useRerankModelList()
</script>

<template>
  <div class="p-[20px] flex flex-col h-full">
    <div class="flex justify-between items-center mb-[24px]">
      <div>
        <img
          class="size-8 inline-block mr-2 align-middle"
          src="@/assets/knowledge/search-icon.png"
          alt=""
        />
        <span class="text-[18px] text-[#30343A] align-middle">检索测试</span>
      </div>
      <el-button type="primary" @click="handleSearchTest">测试</el-button>
    </div>
    <div class="search-text-config">
      <div>
        <div class="search-text-config-item">
          <div class="w-[143px]">
            <span class="search-text-config-label">最大召回数</span>
          </div>
          <div class="slider">
            <el-slider v-model="maxResults" :min="0" :max="10" />
          </div>
          <el-input-number
            v-model="maxResults"
            class="slider-input"
            :min="0"
            :max="10"
            controls-position="right"
          />
        </div>

        <div class="search-text-config-item">
          <div class="w-[143px]">
            <span class="search-text-config-label">最小匹配度</span>
          </div>
          <el-slider v-model="minScore" class="slider" :min="0.01" :max="0.99" :step="0.01" />
          <el-input-number
            v-model="minScore"
            class="slider-input"
            :min="0.01"
            :max="0.99"
            :step="0.01"
            controls-position="right"
          />
        </div>

        <div class="search-text-config-item">
          <div class="w-[143px]">
            <span class="search-text-config-label">结果重排</span>
          </div>
          <el-switch v-model="rerankSwitch" />
        </div>
        <!-- done  产品提的：开启结果重拍时候，才展示重排序模型，这里后面加个v-if -->
        <div v-if="rerankSwitch" class="search-text-config-item">
          <div class="w-[143px]">
            <span class="search-text-config-label">重排序模型</span>
          </div>
          <div class="flex-1">
            <el-select v-model="rerankModelId" placeholder="请选择文本向量模型">
              <template #prefix>
                <img class="size-[20px]" src="@/assets/knowledge/model-icon.png" alt="" />
              </template>
              <el-option
                v-for="item in rerankModelList"
                :key="item.id"
                :label="item.displayName || item.name"
                :value="item.id"
              />
            </el-select>
          </div>
        </div>
      </div>
      <div>
        <div class="mb-[12px] text-[16px] text-[#30343A]">测试文本</div>
        <textarea v-model="searchText" class="test-textarea" style="resize: none"></textarea>
      </div>
    </div>
    <div class="pt-4 flex-1 min-h-0 overflow-auto">
      <div class="file-list">
        <div class="file-header">
          <div class="selection-info">
            <el-checkbox
              v-model="isAllSelected"
              :indeterminate="isIndeterminate"
              @change="handleSelectAll"
            />
            <span>{{ selectedCount }}/{{ totalCount }} 选定的文件</span>
          </div>
          <!-- <div class="pagination">
            <el-icon class="page-btn" :class="{ disabled: currentPage === 1 }">
              <ArrowLeft @click="prevPage" />
            </el-icon>
            <span>{{ currentPage }}</span>
            <el-icon class="page-btn" :class="{ disabled: currentPage === totalPages }">
              <ArrowRight @click="nextPage" />
            </el-icon>
          </div> -->
        </div>

        <el-table
          ref="tableRef"
          :data="fileList"
          style="width: 100%"
          max-height="220"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="50" />

          <el-table-column>
            <template #default="{ row }">
              <FileTypePic :suffix="row.fileSuffix" />
              <span class="ml-1 align-middle">{{ row.name }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="blockSize" width="120" align="center" />

          <el-table-column width="80" align="center">
            <template #default="{ row }">
              <el-icon class="cursor-pointer" @click="handleView(row.ossId)"><View /></el-icon>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="mt-16px">
        <div v-for="(sr, i) in searchResultList" :key="i" class="search-card">
          <div class="card-header">
            <el-space>
              <!-- <span>
                <span>混合相似度：</span>
                <span class="text-[#4865E8]">47.04</span>
              </span> -->

              <!-- <span>
                <span>关键词相似度：</span>
                <span class="text-[#4865E8]">27.01</span>
              </span> -->

              <span>
                <span>向量相似度：</span>
                <span class="text-[#4865E8]">{{ sr.score.toFixed(4) }}</span>
              </span>
            </el-space>
          </div>

          <div class="py-[16px]">
            {{ sr.text }}
          </div>
        </div>
        <div v-if="searchResultList.length === 0">
          <el-empty v-loading="searchLoading" description="暂无测试结果" />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.search-text-config {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  gap: 2.5rem;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(218, 221, 232, 0.5);

  &-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
  }

  &-label {
    color: #30343a;
    font-size: 14px;
    vertical-align: middle;
  }

  .slider {
    // width: calc(100% - 120px - 24px);
    flex: 1;
  }

  .slider-input {
    width: 120px;
    height: 44px;
    background: #ffffff;
    margin-left: 24px;

    :deep(.el-input__inner) {
      text-align: left;
    }
    :deep(.el-input-number__decrease),
    :deep(.el-input-number__increase) {
      --el-input-number-controls-height: 20px;
      --el-border-radius-base: 8px;
      background: #ffffff;
    }
  }
}
.test-textarea {
  width: 100%;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid rgba(218, 221, 232, 0.8);
  height: 180px;
  padding: 12px;
  outline: none;
}

.slider:deep(.el-slider__button) {
  width: 12px;
  height: 12px;
}

.file-list {
  width: 100%;
  border-radius: 4px;
  overflow: hidden;
}

.file-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  color: #30343a;
  background: rgba(218, 221, 232, 0.5);
}
.selection-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-btn {
  cursor: pointer;
}

.page-btn.disabled {
  color: #c0c4cc;
  cursor: not-allowed;
}

:deep(.el-checkbox > span) {
  background-color: #fff;
}

:deep(.el-table) {
  th.el-table__cell {
    display: none;
  }
  tr th {
    // background: rgba(218, 221, 232, 0.5) !important;
    color: #30343a;
  }
  .el-table__body {
    background: #ffffff;
  }
  .el-table__row {
    background: #ffffff;
  }
}

.search-card {
  margin-top: 16px;
  padding: 0 16px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid rgba(218, 221, 232, 0.8);
  color: #30343a;
}
.card-header {
  padding: 16px 0;
  border-bottom: 1px solid rgba(218, 221, 232, 0.5);
}
</style>
