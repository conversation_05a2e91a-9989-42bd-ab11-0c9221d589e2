import { deleteReq, get, post } from '@/utils/request'
import '@/typings/agent.d.ts'
import '@/typings/api.d.ts'
// /biz/agent/adminAgentStatusCount
export function getBizAgentAdminAgentStatusCount<T>(data: Agent.AgentCategoryVo) {
  return get<T>({
    url: '/biz/agent/adminAgentStatusCount',
    data,
  })
}

/** AI助手 */
// "/biz/agent/updateAgentCategory": {
// "post": {
// "summary": "修改助手类型",
export function postBizAgentUpdateAgentCategory<T>(data: Agent.AgentCategoryVo) {
  return post<T>({
    url: '/biz/agent/updateAgentCategory',
    data,
  })
}

// "/biz/agent/turnToPrivate/{id}": {
// "post": {
// "summary": "助手转为私有",
export function postBizAgentTurnToPrivateId<T>(data: { id: number }) {
  return post<T>({
    url: `/biz/agent/turnToPrivate/${data.id}`,
  })
}

// "/biz/agent/removeAgent/{id}": {
// "post": {
// "summary": "删除助手",
export function postBizAgentRemoveAgentId<T>(data: { id: string }) {
  return post<T>({
    url: `/biz/agent/removeAgent/${data.id}`,
  })
}

// "/biz/agent/addOrUpdateCategory": {
// "post": {
// "summary": "新增或者修改类型",
export function postBizAgentAddOrUpdateCategory<T>(data: Agent.AgentCategoryVo) {
  return post<T>({
    url: '/biz/agent/addOrUpdateCategory',
    data,
  })
}

// 提示词优化
// /biz/agent/prompt
export function postBizAgentPrompt<T>(data: { userMessage: string }) {
  return post<T>({
    url: '/biz/agent/prompt',
    data,
  })
}

// "/biz/agent/duplicate": {
// "post": {
// "summary": "复制",
export function postBizAgentDuplicate<T>(data: { id: number }) {
  return post<T>({
    url: `/biz/agent/duplicate?id=${data.id}`,
  })
}

// "/biz/agent/auditAgent": {
// "post": {
// "summary": "审核助手",
export function postBizAgentAuditAgent<T>(data: Agent.AgentEventRecordVo) {
  return post<T>({
    url: '/biz/agent/auditAgent',
    data,
  })
}

// "/biz/agent/addOrUpdateAgent": {
// "post": {
// "summary": "新增或修改助手",
export function postBizAgentAddOrUpdateAgent<T>(data: Agent.AgentVo) {
  return post<T>({
    keepUndefined: true,
    url: '/biz/agent/addOrUpdateAgent',
    data,
  })
}

// "/biz/agent/myAgentList": {
// "get": {
// "summary": "我的助手",
export function getBizAgentMyAgentList<T>(data: Partial<Agent.AgentVo>) {
  return get<T>({
    url: '/biz/agent/myAgentList',
    data,
  })
}

// "/biz/agent/detail/{id}": {
// "get": {
// "summary": "助手详情",
export function getBizAgentDetailId<T>(data: { id: string }) {
  return get<T>({
    url: `/biz/agent/detail/${data.id}`,
  })
}

// "/biz/agent/agentStoreList": {
// "get": {
// "summary": "企业助手",
// 改成箭头函数
export const getBizAgentAgentStoreList = <T>(data: Agent.AgentStoreListRequest) => {
  return get<T>({
    url: '/biz/agent/agentStoreList',
    data,
  })
}

// "/biz/AgentCategory/list": {
// "get": {
// "summary": "助手分类",
// export const getBizAgentCategoryList = <T = Agent.AgentCategoryVo[]>(
export const getBizAgentCategoryList = <T>(data?: Partial<Agent.AgentCategoryVo>) => {
  return get<T>({
    url: '/biz/AgentCategory/list',
    data,
    method: 'get',
  })
}

// "/biz/agent/agentEventRecordList/{id}": {
// "get": {
// "summary": "助手事件记录",
export function getBizAgentAgentEventRecordListId<T>(data: { id: string }) {
  return get<T>({
    url: `/biz/agent/agentEventRecordList/${data.id}`,
  })
}

// "/biz/agent/agentCategoryList": {
// "get": {
// "summary": "助手分类",
export function getBizAgentAgentCategoryList<T>(data?: Partial<Agent.AgentCategoryVo>) {
  return get<T>({
    url: '/biz/agent/agentCategoryList',
    data,
  })
}

// "/biz/agent/adminAgentList": {
// "get": {
// "summary": "管理员助手界面
export function getBizAgentAdminAgentList<T>(data: Partial<Agent.AgentVo>) {
  return get<T>({
    url: '/biz/agent/adminAgentList',
    data,
  })
}

/** AI助手分类 */
// "/biz/AgentCategory/remove/{id}": {
// "post": {
// "summary": "删除分类",
export function postBizAgentCategoryRemoveId<T>(data: { id: string }) {
  return post<T>({
    url: `/biz/AgentCategory/remove/${data.id}`,
  })
}

// "/biz/AgentCategory/addOrUpdate": {
// "post": {
// "summary": "新增或者修改类型",
export function postBizAgentCategoryAddOrUpdate<T>(data: Agent.AgentCategoryVo) {
  return post<T>({
    url: '/biz/AgentCategory/addOrUpdate',
    data,
  })
}

// 依据对外发布Id获取临时token
// /auth/getToken/{outwardId}
// get
export function getAuthGetTokenOutwardId<T>(data: { outwardId: string, password?: string }) {
  return post<T>({
    url: `/auth/getToken/${data.outwardId}`,
    data: {
      password: data.password,
    },
  })
}
