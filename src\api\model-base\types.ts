// 平台数据类型
export interface Platform {
	apiKey: string
	apiSecret: string
	baseUrl: string
	createTime: string
	description: string
	id: string
	isEnabled: boolean
	modelsListUrl: string
	platformCode: string
	platformName: string
	sortOrder: number
	tenantId: string
	icon: string
	ossId: string
	isDefault: boolean | null
	defaultBaseUrl?: string
}


export interface PlatformInfo {
	/**
	 * API密钥
	 */
	apiKey?: string;
	/**
	 * API Secret
	 */
	apiSecret?: string;
	/**
	 * API基础地址
	 */
	baseUrl?: string;
	/**
	 * 平台描述
	 */
	description?: string;
	/**
	 * 平台图标
	 */
	icon?: string;
	/**
	 * 平台ID
	 */
	id?: string | number;
	/**
	 * 是否启用（0-禁用 1-启用）
	 */
	isEnabled?: boolean;
	/**
	 * 获取模型列表的API地址
	 */
	modelsListUrl?: string;
	/**
	 * 平台代码（openai、deepseek等）
	 */
	platformCode?: string;
	/**
	 * 平台名称
	 */
	platformName?: string;
	/**
	 * 排序
	 */
	sortOrder?: number;
}

export interface SortParam {
	sortList: SortInfo[]
}
interface SortInfo {
	id: string
	sortOrder: number
}