<script setup lang="ts">
import { computed, ref } from 'vue'
import emojiList from '@/data/emoji-list.json'

// 定义组件props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
})

// 定义事件
const emit = defineEmits(['select', 'close'])

// 选项卡状态
const activeTab = ref(0)

// 表情分类
const tabs = [
  { label: '表情', category: 'smileys' },
  { label: '人物', category: 'people' },
  { label: '动物', category: 'animals' },
  { label: '食物', category: 'food' },
  { label: '物品', category: 'objects' },
  { label: '符号', category: 'symbols' },
]

// 根据当前选中的分类，获取对应的表情列表
const currentEmojis = computed(() => {
  const category = tabs[activeTab.value].category
  return emojiList[category] || []
})

// 获取表情图片的路径
const getEmojiPath = (code: string): string => {
  return `/static/emoji/${code}.png`
}

// 选择表情的处理函数
const selectEmoji = (emoji: { name: string; code: string }) => {
  emit('select', {
    i: emoji.name,
    n: [emoji.name],
    u: emoji.code,
    r: emoji.code,
    t: 'neutral',
  })
  emit('close')
}
</script>

<template>
  <div class="emoji-picker-local">
    <div v-if="visible" class="emoji-picker-container">
      <div class="emoji-tabs">
        <div
          v-for="(tab, index) in tabs"
          :key="index"
          class="emoji-tab"
          :class="{ active: activeTab === index }"
          @click="activeTab = index"
        >
          {{ tab.label }}
        </div>
      </div>
      <div class="emoji-content">
        <div class="emoji-grid">
          <div
            v-for="(emoji, index) in currentEmojis"
            :key="index"
            class="emoji-item"
            @click="selectEmoji(emoji)"
          >
            <img :src="getEmojiPath(emoji.code)" :alt="emoji.name" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.emoji-picker-local {
  position: relative;
}

.emoji-picker-container {
  position: absolute;
  left: 10px;
  bottom: 0px;
  width: 320px;
  height: 360px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  z-index: 9999;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.emoji-tabs {
  display: flex;
  border-bottom: 1px solid #eee;
  height: 40px;
}

.emoji-tab {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  transition: all 0.3s;
}

.emoji-tab.active {
  color: #4865e8;
  border-bottom: 2px solid #4865e8;
}

.emoji-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 5px;
}

.emoji-item {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  transition: all 0.2s;
}

.emoji-item:hover {
  background-color: #f5f5f5;
}

.emoji-item img {
  width: 24px;
  height: 24px;
}
</style>
