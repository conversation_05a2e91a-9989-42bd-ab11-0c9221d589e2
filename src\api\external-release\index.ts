import { get, post, deleteReq } from '@/utils/request';
import type {
  ConversationVo,
  OutwardQueryParam,
  OutwardSaveParam,
  OutwardUseStat,
  OutwardVo,
} from './type';

/** 新增外部接口 */
export function saveOutward(data: OutwardSaveParam) {
  return post({ url: '/biz/outward/save', data });
}

/** 编辑外部接口 */
export function updateOutward(data: OutwardSaveParam) {
  return post({ url: '/biz/outward/update', data });
}

/** 同步外部接口配置 */
export function syncOutward(id: string) {
  return post({ url: `/biz/outward/sync/${id}` });
}

/** 启用外部接口 */
export function enableOutward(id: string) {
  return post({ url: `/biz/outward/enable/${id}` });
}

/** 禁用外部接口 */
export function disableOutward(id: string) {
  return post({ url: `/biz/outward/disable/${id}` });
}

/** 获取接口使用统计 */
export function getOutwardUseStat(id: string, queryDate: { startDay: string; endDay: string }) {
  return get<OutwardUseStat[]>({ url: `/biz/outward/useStat/${id}`, data: queryDate });
}

/** 分页查询外部接口 */
export function queryOutward(params: OutwardQueryParam) {
  return get<API.ResponseRows<OutwardVo>>({ url: '/biz/outward/query', data: params });
}

/** 获取接口详情 */
export function getOutwardDetail(id: string) {
  return get<OutwardVo>({ url: `/biz/outward/detail/${id}` });
}

/** 删除外部接口 */
export function deleteOutward(id: string | number) {
  return deleteReq<API.Response>({ url: `/biz/outward/delete/${id}` });
}

/**
 * 对外发布详情
 */
export function getOutwardOpenDetail(id: string) {
  return get<OutwardVo>({ url: `/open/outward/detail/${id}` });
}

// /**
//  * 创建会话
//  */
// export function createOutwardApply(id: string) {
//   return post<ConversationVo>({ url: `/biz/outward/apply?id=${id}` });
// }