/**
 * 下载emoji图片到本地
 * 这个脚本用于下载emoji表情包到本地，避免使用CDN
 * 使用方法：node scripts/download-emojis.js
 */

const fs = require('fs')
const path = require('path')
const https = require('https')
const { promisify } = require('util')

// 目标目录
const EMOJI_DIR = path.resolve(__dirname, '../public/static/emoji')
const EMOJI_LIST_FILE = path.resolve(__dirname, '../src/data/emoji-list.json')

// 确保目录存在
if (!fs.existsSync(EMOJI_DIR)) {
  fs.mkdirSync(EMOJI_DIR, { recursive: true })
}

// 读取表情列表
const emojiList = JSON.parse(fs.readFileSync(EMOJI_LIST_FILE, 'utf8'))

// 获取所有表情代码
const allEmojis = Object.values(emojiList)
  .flat()
  .map(emoji => emoji.code)

// CDN 源列表
const CDN_SOURCES = [
  'https://cdn.jsdelivr.net/npm/emoji-datasource-apple/img/apple/64/',
  'https://unpkg.com/emoji-datasource-apple/img/apple/64/',
  'https://fastly.jsdelivr.net/npm/emoji-datasource-apple/img/apple/64/',
]

/**
 * 下载文件
 * @param {string} url 文件URL
 * @param {string} dest 目标路径
 * @param {number} retries 重试次数
 * @param {number} sourceIndex CDN源索引
 * @returns {Promise}
 */
function downloadFile(url, dest, retries = 2, sourceIndex = 0) {
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(dest)

    const request = https.get(url, response => {
      if (response.statusCode !== 200) {
        file.close()
        fs.unlink(dest, () => {})

        // 尝试其他CDN源
        if (sourceIndex < CDN_SOURCES.length - 1) {
          const newUrl = url.replace(CDN_SOURCES[sourceIndex], CDN_SOURCES[sourceIndex + 1])
          resolve(downloadFile(newUrl, dest, retries, sourceIndex + 1))
          return
        }

        // 重试
        if (retries > 0) {
          console.log(`重试下载: ${path.basename(dest)}`)
          resolve(downloadFile(url, dest, retries - 1, 0))
          return
        }

        reject(new Error(`下载失败，状态码: ${response.statusCode}`))
        return
      }

      response.pipe(file)

      file.on('finish', () => {
        file.close(() => {
          console.log(`下载完成: ${dest}`)
          resolve()
        })
      })
    })

    request.on('error', err => {
      file.close()
      fs.unlink(dest, () => {})

      // 尝试其他CDN源
      if (sourceIndex < CDN_SOURCES.length - 1) {
        const newUrl = url.replace(CDN_SOURCES[sourceIndex], CDN_SOURCES[sourceIndex + 1])
        resolve(downloadFile(newUrl, dest, retries, sourceIndex + 1))
        return
      }

      // 重试
      if (retries > 0) {
        console.log(`重试下载: ${path.basename(dest)}`)
        setTimeout(() => {
          resolve(downloadFile(url, dest, retries - 1, 0))
        }, 1000) // 延迟1秒再重试
        return
      }

      reject(err)
    })

    request.setTimeout(10000, () => {
      request.abort()
      file.close()
      fs.unlink(dest, () => {})

      // 尝试其他CDN源
      if (sourceIndex < CDN_SOURCES.length - 1) {
        const newUrl = url.replace(CDN_SOURCES[sourceIndex], CDN_SOURCES[sourceIndex + 1])
        resolve(downloadFile(newUrl, dest, retries, sourceIndex + 1))
        return
      }

      // 重试
      if (retries > 0) {
        console.log(`重试下载: ${path.basename(dest)} (超时)`)
        setTimeout(() => {
          resolve(downloadFile(url, dest, retries - 1, 0))
        }, 1000) // 延迟1秒再重试
        return
      }

      reject(new Error('下载超时'))
    })
  })
}

/**
 * 创建占位图片
 * @param {string} dest 目标路径
 */
function createPlaceholderImage(dest) {
  // 创建一个简单的1x1像素透明PNG图片
  const placeholderData = Buffer.from(
    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=',
    'base64',
  )
  fs.writeFileSync(dest, placeholderData)
  console.log(`创建占位图片: ${dest}`)
}

/**
 * 检查文件是否存在并且非空
 * @param {string} filePath
 * @returns {boolean}
 */
function isFileValid(filePath) {
  try {
    return fs.existsSync(filePath) && fs.statSync(filePath).size > 0
  } catch (error) {
    return false
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('开始下载emoji图片...')
  console.log(`目标目录: ${EMOJI_DIR}`)
  console.log(`总表情数: ${allEmojis.length}`)

  const failedEmojis = []

  // 并行下载，但限制并发数
  const concurrencyLimit = 5
  const chunks = []

  // 将emoji列表分成多个小块
  for (let i = 0; i < allEmojis.length; i += concurrencyLimit) {
    chunks.push(allEmojis.slice(i, i + concurrencyLimit))
  }

  // 按块处理下载
  for (const chunk of chunks) {
    await Promise.all(
      chunk.map(async code => {
        const dest = path.join(EMOJI_DIR, `${code}.png`)

        // 检查文件是否已经存在且有效
        if (isFileValid(dest)) {
          console.log(`文件已存在，跳过: ${dest}`)
          return
        }

        try {
          const url = `${CDN_SOURCES[0]}${code}.png`
          await downloadFile(url, dest)
        } catch (error) {
          console.error(`下载失败: ${code}`, error.message)
          failedEmojis.push(code)

          // 创建占位图片，确保至少有一个文件可用
          createPlaceholderImage(dest)
        }
      }),
    )
  }

  if (failedEmojis.length > 0) {
    console.error('以下emoji下载失败，但已创建占位图片:')
    console.error(failedEmojis.join(', '))
  } else {
    console.log('所有emoji下载完成！')
  }
}

main().catch(console.error)
