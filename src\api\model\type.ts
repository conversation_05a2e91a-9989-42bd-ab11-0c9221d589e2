export interface ModelParams {
	/**
	 * 模型请求地址
	 */
	baseUrl?: string;
	/**
	 * 模型能力描述(Json格式)
	 */
	capabilities?: string[];
	/**
	 * 显示名称
	 */
	displayName?: string;
	id?: string;
	/**
	 * 输入token单价
	 */
	inputTokenUnitPrice?: number;
	/**
	 * 是否启用 0-未启用 1-启用
	 */
	isEnabled?: boolean;
	/**
	 * 最大输入token数
	 */
	maxInputTokens?: number;
	/**
	 * 最大输出token数
	 */
	maxOutputTokens?: number;
	/**
	 * 最大token数
	 */
	maxTokens?: number;
	/**
	 * 多模态能力
	 */
	multimodalFlag?: boolean;
	/**
	 * 模型名称
	 */
	name?: string;
	/**
	 * 输出token单价
	 */
	outputTokenUnitPrice?: number;
	/**
	 * 平台 openai
	 */
	platform?: string;
	/**
	 * 关联的平台ID
	 */
	platformId?: string | number;
	/**
	 * 设置参数
	 */
	setting?: string;
	/**
	 * 状态 0-未检测 1-通过 2-失败
	 */
	status?: number;
	/**
	 * 输入token单价
	 */
	tokenUnitPrice?: number;
	/**
	 * 模型类型 text,image,embedding,rerank
	 */
	type?: string;
}
