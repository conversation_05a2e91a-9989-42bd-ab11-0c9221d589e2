const { chromium } = require('playwright')

;(async () => {
  // 启动浏览器
  console.log('启动浏览器...')
  const browser = await chromium.launch({
    headless: false, // 设置为非无头模式，以便可以看到浏览器界面
    devtools: true, // 自动打开开发者工具
  })

  // 创建新页面
  console.log('创建新页面...')
  const page = await browser.newPage()

  // 收集所有控制台消息
  const consoleMessages = []

  // 监听控制台事件
  page.on('console', message => {
    const type = message.type()
    const text = message.text()
    const location = message.location()

    consoleMessages.push({
      type,
      text,
      location: location ? `${location.url}:${location.lineNumber}` : 'unknown',
    })

    // 在我们自己的控制台输出浏览器的控制台消息
    console.log(`浏览器控制台[${type}]: ${text}`)
  })

  // 监听页面错误
  page.on('pageerror', error => {
    console.error(`页面错误: ${error.message}`)
    consoleMessages.push({
      type: 'error',
      text: error.message,
      location: 'pageerror',
    })
  })

  // 导航到百度
  console.log('正在打开百度页面...')
  await page.goto('https://www.baidu.com')

  console.log('已成功打开百度页面！')

  // 在搜索框中输入"今天天气"
  console.log('在搜索框中输入"今天天气"')
  await page.fill('#kw', '今天天气')

  // 点击搜索按钮
  console.log('点击搜索按钮')
  await page.click('#su')

  // 等待搜索结果加载
  console.log('等待搜索结果加载...')
  await page.waitForSelector('#content_left')

  console.log('搜索完成，显示天气信息')

  // 主动执行JavaScript获取更多页面信息
  await page.evaluate(() => {
    // 在页面控制台生成一些日志
    console.log('Playwright测试: 正在检查页面')
    console.warn('Playwright测试: 这是一条警告消息')
    console.error('Playwright测试: 这是一条错误消息')

    // 尝试访问页面上的一些DOM元素
    const searchBox = document.querySelector('#kw')
    if (searchBox) {
      console.log('搜索框当前值:', searchBox.value)
    }

    const results = document.querySelectorAll('.result')
    console.log(`找到 ${results.length} 条搜索结果`)

    // 页面性能数据
    if (window.performance) {
      console.info(
        '页面加载性能:',
        JSON.stringify({
          loadTime: performance.timing.loadEventEnd - performance.timing.navigationStart,
          domReady: performance.timing.domComplete - performance.timing.domLoading,
          networkLatency: performance.timing.responseEnd - performance.timing.requestStart,
        }),
      )
    }
  })

  // 等待一段时间以收集更多控制台消息
  console.log('等待3秒收集控制台消息...')
  await page.waitForTimeout(3000)

  // 输出收集到的所有控制台消息
  console.log('\n收集到的控制台消息汇总:')
  console.table(consoleMessages)

  // 截图保存搜索结果
  await page.screenshot({ path: 'baidu-weather-search.png', fullPage: true })
  console.log('已保存搜索结果截图到 baidu-weather-search.png')

  // 等待用户手动关闭浏览器
  console.log('\n浏览器将保持打开状态，您可以在开发者工具中查看更多控制台消息')
  console.log('按 Ctrl+C 终止程序')

  // 这里不调用browser.close()，以便用户可以继续查看页面和控制台
})().catch(err => {
  console.error('发生错误:', err)
  process.exit(1)
})
