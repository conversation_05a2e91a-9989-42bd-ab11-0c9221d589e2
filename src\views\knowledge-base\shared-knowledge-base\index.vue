<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, Sort } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import Pagination from '@/components/common/Pagination/index.vue'
import { getBizKbShareList } from '@/api/knowledge-base'
import { formatFileSize } from '@/utils/formatter'
import '@/typings/knowledge-base.d.ts'
import { useKnowledgeBaseStore } from '@/store/modules/knowledge-base'
const knowledgeBaseStore = useKnowledgeBaseStore()
const loading = ref(false)
const tableData = ref<KnowledgeBase.KnowledgeBaseVo[]>([])
const dialogVisible = ref(false)
const dialogTitle = ref('新增知识')
const formRef = ref<FormInstance>()
const queryFormRef = ref<FormInstance>()
const total = ref(0)

// 查询参数
const queryParams = ref<KnowledgeBase.KbListParams>({
  pageNum: 1,
  pageSize: 10,
  name: '',
})

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1
  getKnowledgeList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  queryParams.value.pageNum = 1
  handleQuery()
}

const handleAdd = () => {
  dialogTitle.value = '新增知识'
  dialogVisible.value = true
}
const router = useRouter()
const handleLookUp = (kbId: number | string, sharePermissionLevel: string) => {
  knowledgeBaseStore.setSharePermissionLevel(kbId.toString(), sharePermissionLevel)
  router.push({
    name: 'share-dataset',
    params: {
      kbId,
    },
  })
}

const getKnowledgeList = () => {
  loading.value = true
  getBizKbShareList<{
    rows: KnowledgeBase.KnowledgeBaseVo[]
    total?: number
  }>({
    pageNum: queryParams.value.pageNum,
    pageSize: queryParams.value.pageSize,
    name: queryParams.value.name,
  })
    .then((res: any) => {
      tableData.value = res.rows
      total.value = res.total
      loading.value = false
    })
    .catch(() => {
      loading.value = false
    })
}

onMounted(() => {
  getKnowledgeList()
})
</script>

<template>
  <div class="my-container">
    <div class="flex justify-between">
      <!-- <div class="flex">
        <el-button class="ex-el-button-gray" type="primary" plain @click="handleAdd()">
          <i class="iconfont iconfont-c icon-xinzeng mr-2"></i>
          新增
        </el-button>
      </div> -->
      <div class="flex justify-end">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="text-right">
          <el-form-item label="" prop="title" class="!mr-0">
            <el-input
              v-model="queryParams.name"
              style="width: 400px"
              placeholder="请输入你需要搜索的内容"
              clearable
              @clear="handleQuery"
              @keyup.enter="handleQuery"
            >
              <template #suffix>
                <el-icon>
                  <Search />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>
        </el-form>
        <!-- <el-button class="ml-3" type="primary" plain @click="handleQuery"> 高级搜索 </el-button> -->
      </div>
    </div>

    <el-table v-loading="loading" :data="tableData" style="width: 100%">
      <el-table-column type="index" label="序号" width="60" />
      <el-table-column prop="name" label="知识" min-width="200" show-overflow-tooltip>
        <template #default="{ row }">
          <div
            class="knowledge-name-in-table"
            @click="handleLookUp(row.id, row.sharePermissionLevel)"
          >
            <img class="inline-block w-[22px] h-[22px]" src="@/assets/word.png" alt="" />
            <div class="knowledge-name">
              {{ row.name }}
              <el-tooltip
                v-if="!row.modelExist"
                effect="dark"
                content="向量模型被删除，请重新设置向量模型。"
                placement="bottom-start"
              >
                <img class="has-error" src="@/assets/chat/error.svg" alt="" />
              </el-tooltip>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="type" label="类型" width="120">
        <template #default="{ row }">
          <span v-if="row.type === 1">文本</span>
          <span v-if="row.type === 2">图像</span>
        </template>
      </el-table-column>
      <el-table-column prop="size" label="文件大小" width="120">
        <template #default="{ row }">
          {{ formatFileSize(row.size) }}
        </template>
      </el-table-column>
      <el-table-column prop="userName" label="拥有者" width="120" />
      <el-table-column prop="sharePermissionLevel" label="权限" width="120">
        <template #default="{ row }">
          <span v-if="row.sharePermissionLevel === '1'">仅查看</span>
          <span v-if="row.sharePermissionLevel === '2'">可编辑</span>
        </template>
      </el-table-column>
      <el-table-column prop="editTime" label="编辑时间" width="180" />
      <el-table-column label="操作" width="180" fixed="right">
        <template #default="{ row }">
          <el-button link type="primary" @click="handleLookUp(row.id, row.sharePermissionLevel)">
            查看
          </el-button>
          <!-- <el-button link type="danger" @click="handleDelete(row)"> 删除 </el-button> <!-- 暂不支持删除 -->
        </template>
      </el-table-column>
    </el-table>

    <Pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getKnowledgeList"
    />
  </div>
</template>

<style scoped lang="less">
.my-container {
  padding: 20px;
}

:deep(.el-table) {
  --el-table-header-bg-color: #f7f8fa;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.knowledge-name-in-table {
  display: flex;
  align-items: center;
  cursor: pointer;

  .knowledge-name {
    max-width: 100%;
    margin-left: 4px;
    overflow: hidden;
    white-space: nowrap;
    min-width: 0;
    text-overflow: ellipsis;

    position: relative;
    padding-right: 26px;
  }

  .has-error {
    width: 17px;
    height: 17px;
    position: absolute;
    right: 0;
    top: 2px;
  }
}
</style>
