<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store'
import {
  getConfirmPhone,
  postConfirmPwd,
  putSystemUserProfileUpdatePhone,
  putSystemUserProfileUpdatePwd,
} from '@/api/system'
import { getSmsCode } from '@/components/common/LoginDialog/utils/api'
import SlideCaptcha from '@/components/common/SlideCaptcha/index.vue'
import { useCaptcha } from '@/hooks/useCaptcha'

// resolved 发送验证码调用getSmsCode接口，发送前不能输入验证码，发送后才能输入

const userStore = useUserStore()
const { checkNeedCaptcha } = useCaptcha()

// 用户信息
const userInfo = computed(() => userStore.userInfo)

// 密码显示控制
const showPassword = ref(false)
const passwordValue = ref('123456')
const displayPassword = computed(() => {
  return showPassword.value ? passwordValue.value : '******'
})

// 切换密码显示状态
function togglePasswordVisibility() {
  showPassword.value = !showPassword.value
}

/** 修改手机号相关 */
const phoneDialogVisible = ref(false)
const phoneVerifyStep = ref(1) // 1: 验证身份 2: 绑定新手机号
const activeVerifyTab = ref('phone')
const currentPhone = computed(() => userInfo.value?.phonenumber || '')

// 验证表单
const verifyForm = ref({
  phone: '',
  code: '',
  password: '',
})

// 新手机表单
const newPhoneForm = ref({
  phone: '',
  code: '',
})

// 表单验证规则
const verifyFormRules = {
  phone: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
  code: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
}

const newPhoneFormRules = {
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' },
  ],
  code: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
}

// 发送验证码倒计时
const countdown = ref(0)
const newPhoneCountdown = ref(0)
const smsButtonText = computed(() =>
  countdown.value > 0 ? `${countdown.value}秒后重试` : '获取验证码',
)
const newPhoneSmsButtonText = computed(() =>
  newPhoneCountdown.value > 0 ? `${newPhoneCountdown.value}秒后重试` : '获取验证码',
)

// 验证码输入框禁用状态
const codeInputDisabled = ref(true)
const newPhoneCodeInputDisabled = ref(true)

// 表单ref
const verifyFormRef = ref()
const newPhoneFormRef = ref()

// 滑块验证相关
const captchaVisible = ref(false)
const captchaData = ref<any>(null)
const captchaSuccessContext = ref<'oldPhone' | 'newPhone' | 'passwordPhone' | null>(null)

// 显示滑块验证
const showCaptchaDialog = () => {
  captchaVisible.value = true
}

// 打开修改手机号对话框
function openEditPhoneDialog() {
  phoneDialogVisible.value = true
  phoneVerifyStep.value = 1
  activeVerifyTab.value = 'phone'
  verifyForm.value = {
    phone: currentPhone.value,
    code: '',
    password: '',
  }
  newPhoneForm.value = {
    phone: '',
    code: '',
  }
}

// 发送验证码
async function sendVerifyCode() {
  console.log('sendVerifyCode')
  if (countdown.value > 0) return
  if (!verifyForm.value.phone) {
    ElMessage.warning('请输入手机号')
    return
  }

  // 检查是否需要滑块验证
  const needCaptcha = await checkNeedCaptcha(verifyForm.value.phone, '')
  if (needCaptcha && !captchaData.value) {
    // 需要滑块验证但还没有验证，显示滑块
    captchaSuccessContext.value = 'oldPhone'
    showCaptchaDialog()
    return
  }

  await performSendVerifyCode()
}

// 执行发送验证码的具体逻辑
async function performSendVerifyCode() {
  try {
    // 发送验证码的API调用
    await getSmsCode()
    ElMessage.success('验证码已发送')

    // 启用验证码输入框
    codeInputDisabled.value = false

    // 启动倒计时
    countdown.value = 60
    const timer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)
  } catch (error) {
    ElMessage.error(error || '发送验证码失败')
  }
}

// 发送新手机号验证码
async function sendNewPhoneCode() {
  console.log('sendNewPhoneCode', { phonenumber: newPhoneForm.value.phone })
  if (newPhoneCountdown.value > 0) return
  if (!newPhoneForm.value.phone) {
    ElMessage.warning('请输入手机号')
    return
  }

  if (!/^1[3-9]\d{9}$/.test(newPhoneForm.value.phone)) {
    ElMessage.warning('手机号格式不正确')
    return
  }

  if (newPhoneForm.value.phone === currentPhone.value) {
    ElMessage.warning('新手机号不能与当前手机号相同')
    return
  }

  // 检查是否需要滑块验证
  const needCaptcha = await checkNeedCaptcha(newPhoneForm.value.phone, '')
  if (needCaptcha && !captchaData.value) {
    // 需要滑块验证但还没有验证，显示滑块
    captchaSuccessContext.value = 'newPhone'
    showCaptchaDialog()
    return
  }

  await performSendNewPhoneCode()
}

// 执行发送新手机号验证码的具体逻辑
async function performSendNewPhoneCode() {
  try {
    // 发送验证码的API调用
    await getSmsCode({ phonenumber: newPhoneForm.value.phone })
    ElMessage.success('验证码已发送')

    // 启用验证码输入框
    newPhoneCodeInputDisabled.value = false

    // 启动倒计时
    newPhoneCountdown.value = 60
    const timer = setInterval(() => {
      newPhoneCountdown.value--
      if (newPhoneCountdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)
  } catch (error) {
    ElMessage.error(error || '发送验证码失败')
  }
}

// 验证身份（改手机号）
async function verifyIdentity() {
  await verifyFormRef.value.validate(async (valid: boolean) => {
    if (!valid) return
    try {
      if (activeVerifyTab.value === 'phone') {
        // 通过手机验证码验证身份
        // 调用 getConfirmPhone API验证手机验证码
        await getConfirmPhone<any>({
          // phonenumber: verifyForm.value.phone,
          smsCode: verifyForm.value.code,
        })
      } else {
        // 通过密码验证身份
        await postConfirmPwd<any>({
          password: verifyForm.value.password,
        })
      }

      // 验证成功，进入下一步
      phoneVerifyStep.value = 2
    } catch (error) {
      // ElMessage.error('验证失败，请检查输入')
    }
  })
}

// 绑定新手机号
async function bindNewPhone() {
  await newPhoneFormRef.value.validate(async (valid: boolean) => {
    if (!valid) return

    try {
      await getConfirmPhone<any>({
        phonenumber: newPhoneForm.value.phone,
        smsCode: newPhoneForm.value.code,
      })
      // 调用绑定新手机号的API
      await putSystemUserProfileUpdatePhone<any>({
        phonenumber: newPhoneForm.value.phone,
      })

      ElMessage.success('手机号更新成功')
      // 更新用户信息
      await userStore.getInfo()
      phoneDialogVisible.value = false
    } catch (error) {
      // ElMessage.error('更新手机号失败')
    }
  })
}

watch(
  () => phoneDialogVisible.value,
  newVal => {
    if (!newVal) {
      // 重置表单
      phoneVerifyStep.value = 1
      // 重置验证码输入框状态
      codeInputDisabled.value = true
      newPhoneCodeInputDisabled.value = true
    }
  },
)

/** 修改密码相关 */
const passwordDialogVisible = ref(false)
const passwordVerifyStep = ref(1) // 1: 验证身份 2: 设置新密码
const activePasswordVerifyTab = ref('phone')

// 密码验证表单
const passwordVerifyForm = ref({
  phone: '',
  code: '',
  password: '',
})

// 新密码表单
const newPasswordForm = ref({
  newPassword: '',
  confirmPassword: '',
})

// 密码表单验证规则
const passwordVerifyFormRules = {
  phone: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
  code: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
}

const newPasswordFormRules = {
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6个字符', trigger: 'blur' },
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (value !== newPasswordForm.value.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
}

// 密码验证码倒计时
const passwordCountdown = ref(0)
const passwordSmsButtonText = computed(() =>
  passwordCountdown.value > 0 ? `${passwordCountdown.value}秒后重试` : '获取验证码',
)

// 密码验证码输入框禁用状态
const passwordCodeInputDisabled = ref(true)

// 密码表单ref
const passwordVerifyFormRef = ref()
const newPasswordFormRef = ref()

// 打开修改密码对话框
function openEditPasswordDialog() {
  passwordDialogVisible.value = true
  passwordVerifyStep.value = 1
  activePasswordVerifyTab.value = 'phone'
  passwordVerifyForm.value = {
    phone: currentPhone.value,
    code: '',
    password: '',
  }
  newPasswordForm.value = {
    newPassword: '',
    confirmPassword: '',
  }
}

// 发送密码验证码
async function sendPasswordVerifyCode() {
  console.log('sendPasswordVerifyCode')
  if (passwordCountdown.value > 0) return
  if (!passwordVerifyForm.value.phone) {
    ElMessage.warning('请输入手机号')
    return
  }

  // 检查是否需要滑块验证
  const needCaptcha = await checkNeedCaptcha(passwordVerifyForm.value.phone, '')
  if (needCaptcha && !captchaData.value) {
    // 需要滑块验证但还没有验证，显示滑块
    captchaSuccessContext.value = 'passwordPhone'
    showCaptchaDialog()
    return
  }

  await performSendPasswordVerifyCode()
}

// 执行发送密码验证码的具体逻辑
async function performSendPasswordVerifyCode() {
  try {
    // 发送验证码的API调用
    await getSmsCode()
    ElMessage.success('验证码已发送')

    // 启用验证码输入框
    passwordCodeInputDisabled.value = false

    // 启动倒计时
    passwordCountdown.value = 60
    const timer = setInterval(() => {
      passwordCountdown.value--
      if (passwordCountdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)
  } catch (error) {
    ElMessage.error(error || '发送验证码失败')
  }
}

// 验证密码身份（改密码）
async function verifyPasswordIdentity() {
  const validateFields =
    activePasswordVerifyTab.value === 'phone' ? ['phone', 'code'] : ['password']

  await passwordVerifyFormRef.value.validateField(validateFields, async (valid: boolean) => {
    if (!valid) return

    try {
      if (activePasswordVerifyTab.value === 'phone') {
        // 通过手机验证码验证身份
        await getConfirmPhone<any>({
          // phonenumber: passwordVerifyForm.value.phone,
          smsCode: passwordVerifyForm.value.code,
        })
      } else {
        // 通过密码验证身份
        await postConfirmPwd<any>({
          password: passwordVerifyForm.value.password,
        })
      }

      // 验证成功，进入下一步
      passwordVerifyStep.value = 2
    } catch (error) {
      // ElMessage.error('验证失败，请检查输入')
    }
  })
}

// 更新密码
async function updatePassword() {
  await newPasswordFormRef.value.validate(async (valid: boolean) => {
    if (!valid) return

    try {
      // 调用更新密码的API
      // resolved 调整下入参   password: string   confirmPassword: string
      await putSystemUserProfileUpdatePwd<any>({
        password: newPasswordForm.value.newPassword,
        confirmPassword: newPasswordForm.value.confirmPassword,
      })

      ElMessage.success('密码更新成功')
      passwordDialogVisible.value = false
    } catch (error) {
      // ElMessage.error('更新密码失败')
    }
  })
}

// 滑块验证成功后的处理
const handleCaptchaSuccess = (data: any) => {
  captchaData.value = data
  captchaVisible.value = false

  // 根据上下文执行对应的发送短信操作，跳过验证检查
  if (captchaSuccessContext.value === 'oldPhone') {
    performSendVerifyCode()
  } else if (captchaSuccessContext.value === 'newPhone') {
    performSendNewPhoneCode()
  } else if (captchaSuccessContext.value === 'passwordPhone') {
    performSendPasswordVerifyCode()
  }

  // 重置上下文
  captchaSuccessContext.value = null
}

// 滑块验证关闭后的处理
const handleCaptchaClose = () => {
  captchaVisible.value = false
  captchaSuccessContext.value = null
}

watch(
  () => phoneDialogVisible.value,
  newVal => {
    if (!newVal) {
      // 重置表单
      phoneVerifyStep.value = 1
      // 重置验证码输入框状态
      codeInputDisabled.value = true
      newPhoneCodeInputDisabled.value = true
      // 重置滑块验证状态
      captchaData.value = null
    }
  },
)

watch(
  () => passwordDialogVisible.value,
  newVal => {
    if (!newVal) {
      // 重置表单
      passwordVerifyStep.value = 1
      // 重置验证码输入框状态
      passwordCodeInputDisabled.value = true
      // 重置滑块验证状态
      captchaData.value = null
    }
  },
)
</script>

<template>
  <div>
    <div class="section-header">
      <div class="section-title">安全</div>
    </div>
    <!-- 关联手机号 -->
    <div class="profile-item">
      <div class="item-label">关联手机号</div>
      <div class="item-value">
        {{ userInfo.phonenumber || '--' }}
        <!-- 点击弹框"修改手机号"，进行"手机验证"tab（手机号+验证码）或者"密码验证"tab（密码），再下一步是绑定新手机号（新手机号+验证码） -->
        <div class="cursor-pointer ml-2" @click="openEditPhoneDialog">
          <i class="iconfont icon-bianji iconfont-c !text-[20px]"></i>
        </div>
      </div>
    </div>
    <!-- 密码 -->
    <div class="profile-item">
      <div class="item-label flex items-center">
        密码
        <div class="cursor-pointer ml-2" @click="togglePasswordVisibility">
          <!-- <i v-if="showPassword" class="iconfont icon-yincang"></i>
          <i v-else class="iconfont icon-xianshi"></i> -->
        </div>
      </div>
      <div class="item-value">
        {{ displayPassword }}
        <!-- resolved: 点击弹框"修改密码"，进行"手机验证"tab（手机号+验证码）或者"密码验证"tab（密码），再下一步是设置新密码（新密码+确认新密码） -->
        <div class="cursor-pointer ml-2" @click="openEditPasswordDialog">
          <i class="iconfont icon-bianji iconfont-c !text-[20px]"></i>
        </div>
      </div>
    </div>

    <!-- 修改手机号对话框 -->
    <ElDialog
      v-model="phoneDialogVisible"
      :title="phoneVerifyStep === 1 ? '修改手机号' : '绑定新手机号'"
      width="450px"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
    >
      <!-- 步骤1: 验证身份 -->
      <div v-if="phoneVerifyStep === 1">
        <ElTabs v-model="activeVerifyTab" class="verify-tabs">
          <ElTabPane label="手机验证" name="phone">
            <ElForm
              v-if="activeVerifyTab === 'phone'"
              ref="verifyFormRef"
              :model="verifyForm"
              :rules="verifyFormRules"
              label-position="top"
            >
              <ElFormItem label="手机号" prop="phone">
                <ElInput v-model="verifyForm.phone" disabled placeholder="当前绑定的手机号" />
              </ElFormItem>
              <ElFormItem label="验证码" prop="code">
                <div class="code-input-container">
                  <ElInput
                    v-model="verifyForm.code"
                    placeholder="请输入验证码"
                    :disabled="codeInputDisabled"
                  />
                  <ElButton
                    type="primary"
                    class="send-code-btn"
                    :disabled="countdown > 0"
                    @click="sendVerifyCode"
                  >
                    {{ smsButtonText }}
                  </ElButton>
                </div>
              </ElFormItem>
            </ElForm>
          </ElTabPane>
          <ElTabPane label="密码验证" name="password">
            <ElForm
              v-if="activeVerifyTab === 'password'"
              ref="verifyFormRef"
              :model="verifyForm"
              :rules="verifyFormRules"
              label-position="top"
            >
              <ElFormItem label="密码" prop="password">
                <ElInput v-model="verifyForm.password" type="password" placeholder="请输入密码" />
              </ElFormItem>
            </ElForm>
          </ElTabPane>
        </ElTabs>
      </div>

      <!-- 步骤2: 绑定新手机号 -->
      <div v-else-if="phoneVerifyStep === 2">
        <ElForm
          ref="newPhoneFormRef"
          :model="newPhoneForm"
          :rules="newPhoneFormRules"
          label-position="top"
        >
          <ElFormItem label="手机号" prop="phone">
            <ElInput v-model="newPhoneForm.phone" placeholder="请输入新的手机号" />
          </ElFormItem>
          <ElFormItem label="验证码" prop="code">
            <div class="code-input-container">
              <ElInput
                v-model="newPhoneForm.code"
                placeholder="请输入验证码"
                :disabled="newPhoneCodeInputDisabled"
              />
              <ElButton
                type="primary"
                class="send-code-btn"
                :disabled="newPhoneCountdown > 0"
                @click="sendNewPhoneCode"
              >
                {{ newPhoneSmsButtonText }}
              </ElButton>
            </div>
          </ElFormItem>
        </ElForm>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <ElButton @click="phoneDialogVisible = false">取消</ElButton>
          <ElButton v-if="phoneVerifyStep === 1" type="primary" @click="verifyIdentity">
            下一步
          </ElButton>
          <ElButton v-else type="primary" @click="bindNewPhone"> 确定 </ElButton>
        </span>
      </template>
    </ElDialog>

    <!-- 修改密码对话框 -->
    <ElDialog
      v-model="passwordDialogVisible"
      :title="passwordVerifyStep === 1 ? '修改密码' : '设置新密码'"
      width="450px"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
    >
      <!-- 步骤1: 验证身份 -->
      <div v-if="passwordVerifyStep === 1">
        <ElTabs v-model="activePasswordVerifyTab" class="verify-tabs">
          <ElTabPane label="手机验证" name="phone">
            <ElForm
              v-if="activePasswordVerifyTab === 'phone'"
              ref="passwordVerifyFormRef"
              :model="passwordVerifyForm"
              :rules="passwordVerifyFormRules"
              label-position="top"
            >
              <ElFormItem label="手机号" prop="phone">
                <ElInput
                  v-model="passwordVerifyForm.phone"
                  disabled
                  placeholder="当前绑定的手机号"
                />
              </ElFormItem>
              <ElFormItem label="验证码" prop="code">
                <div class="code-input-container">
                  <ElInput
                    v-model="passwordVerifyForm.code"
                    placeholder="请输入验证码"
                    :disabled="passwordCodeInputDisabled"
                  />
                  <ElButton
                    type="primary"
                    class="send-code-btn"
                    :disabled="passwordCountdown > 0"
                    @click="sendPasswordVerifyCode"
                  >
                    {{ passwordSmsButtonText }}
                  </ElButton>
                </div>
              </ElFormItem>
            </ElForm>
          </ElTabPane>
          <ElTabPane label="密码验证" name="password">
            <ElForm
              v-if="activePasswordVerifyTab === 'password'"
              ref="passwordVerifyFormRef"
              :model="passwordVerifyForm"
              :rules="passwordVerifyFormRules"
              label-position="top"
            >
              <ElFormItem label="密码" prop="password">
                <ElInput
                  v-model="passwordVerifyForm.password"
                  type="password"
                  placeholder="请输入当前密码"
                />
              </ElFormItem>
            </ElForm>
          </ElTabPane>
        </ElTabs>
      </div>

      <!-- 步骤2: 设置新密码 -->
      <div v-else-if="passwordVerifyStep === 2">
        <ElForm
          ref="newPasswordFormRef"
          :model="newPasswordForm"
          :rules="newPasswordFormRules"
          label-position="top"
        >
          <ElFormItem label="新密码" prop="newPassword">
            <ElInput
              v-model="newPasswordForm.newPassword"
              type="password"
              placeholder="请输入新密码"
            />
          </ElFormItem>
          <ElFormItem label="确认新密码" prop="confirmPassword">
            <ElInput
              v-model="newPasswordForm.confirmPassword"
              type="password"
              placeholder="请确认新密码"
            />
          </ElFormItem>
        </ElForm>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <ElButton @click="passwordDialogVisible = false">取消</ElButton>
          <ElButton v-if="passwordVerifyStep === 1" type="primary" @click="verifyPasswordIdentity">
            下一步
          </ElButton>
          <ElButton v-else type="primary" @click="updatePassword"> 确定 </ElButton>
        </span>
      </template>
    </ElDialog>

    <!-- 滑块验证组件 -->
    <SlideCaptcha
      v-model:visible="captchaVisible"
      @success="handleCaptchaSuccess"
      @close="handleCaptchaClose"
    />
  </div>
</template>

<style scoped>
/* 验证码样式 */
.code-input-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.send-code-btn {
  white-space: nowrap;
  flex-shrink: 0;
}

.verify-tabs {
  width: 100%;
}

:deep(.verify-tabs .el-tabs__content) {
  padding: 20px 0;
}
</style>
