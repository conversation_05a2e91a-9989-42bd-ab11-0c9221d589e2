<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { ElButton } from 'element-plus'
import { useRouter } from 'vue-router'

const router = useRouter()

// 返回上一页
const goBack = () => {
  router.back()
}

onMounted(() => {
  // 设置页面标题
  document.title = '用户协议与隐私政策'
})
</script>

<template>
  <div class="terms-container">
    <div class="terms-header">
      <h1 class="terms-title">用户协议与隐私政策</h1>
      <div class="back-button">
        <ElButton type="primary" plain @click="goBack">返回</ElButton>
      </div>
    </div>

    <div class="terms-content">
      <div class="terms-section">
        <h2>用户协议</h2>
        <div class="section-content">
          <h3>1. 服务条款</h3>
          <p>
            欢迎使用我们的服务。本用户协议（"协议"）是您与我们之间关于您使用我们提供的网站、产品和服务的法律协议。使用我们的服务即表示您同意本协议的所有条款。
          </p>

          <h3>2. 账号注册与安全</h3>
          <p>
            您在注册账号时须提供真实、准确、完整的个人资料，并及时更新相关信息。您有责任保护账号安全，包括但不限于妥善保管用户名和密码。如因您未能保管好自己的账号和密码而导致的任何损失，我们将不承担责任。
          </p>

          <h3>3. 用户行为规范</h3>
          <p>
            您在使用我们的服务时，必须遵守中华人民共和国相关法律法规，不得利用我们的服务从事违法违规活动。您不得利用我们的服务制作、上传、发布、传播含有以下内容的信息：
          </p>
          <ul>
            <li>反对宪法确定的基本原则的</li>
            <li>危害国家安全，泄露国家秘密的</li>
            <li>侮辱、诽谤他人，侵害他人合法权益的</li>
            <li>含有淫秽、色情、赌博、暴力、恐怖或者教唆犯罪内容的</li>
            <li>其他违反法律法规及社会公德的内容</li>
          </ul>

          <h3>4. 知识产权</h3>
          <p>
            我们尊重知识产权并希望用户也尊重知识产权。您应确保您上传、发布的内容不侵犯任何第三方的知识产权。对于侵犯他人知识产权的内容，我们有权予以删除。
          </p>

          <h3>5. 协议修改</h3>
          <p>
            我们保留随时修改本协议的权利。修改后的协议一经公布即代替原协议，无需另行通知。如您不同意相关修改，请立即停止使用我们的服务；如您继续使用我们的服务，则视为您接受修改后的协议。
          </p>
        </div>
      </div>

      <div class="terms-section">
        <h2>隐私政策</h2>
        <div class="section-content">
          <h3>1. 信息收集</h3>
          <p>
            我们可能收集您的个人信息，包括但不限于：您提供的个人资料（如姓名、联系方式）、设备信息、位置信息、日志信息以及您使用我们服务时产生的其他信息。我们承诺依法收集、使用这些信息。
          </p>

          <h3>2. 信息使用</h3>
          <p>我们可能将收集的信息用于以下用途：</p>
          <ul>
            <li>向您提供服务</li>
            <li>改进和优化我们的服务</li>
            <li>确保服务的安全</li>
            <li>向您推送通知、活动和服务信息</li>
            <li>用于与服务相关的其他用途</li>
          </ul>

          <h3>3. 信息共享</h3>
          <p>除以下情况外，我们不会与任何第三方共享您的个人信息：</p>
          <ul>
            <li>获得您的明确同意</li>
            <li>为满足法律法规要求或遵守法律程序</li>
            <li>为保护我们、我们的用户或公众的权利、财产或安全</li>
            <li>与我们的关联公司共享，他们将遵守同样的隐私保护义务</li>
          </ul>

          <h3>4. 信息安全</h3>
          <p>
            我们致力于保护您的个人信息安全。我们使用各种安全技术和程序，以防信息的丢失、不当使用、未经授权访问或披露。
          </p>

          <h3>5. Cookie和同类技术</h3>
          <p>
            我们使用Cookie和同类技术来收集和存储您的信息，以便为您提供更加个性化的服务体验。您可以通过浏览器设置管理Cookie，但这可能影响您使用我们的某些服务。
          </p>
        </div>
      </div>

      <div class="terms-section">
        <h2>联系我们</h2>
        <p>
          如您对本用户协议与隐私政策有任何疑问，或需要就隐私问题进行投诉，请通过以下方式联系我们：
        </p>
        <p>邮箱：<EMAIL></p>
        <p>电话：400-XXX-XXXX</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.terms-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 30px 20px;
  background-color: #fff;
  min-height: 100vh;
}

.terms-header {
  position: relative;
  margin-bottom: 30px;
  padding-bottom: 15px;
  font-weight: bold;
}

.back-button {
  position: absolute;
  right: 0;
  top: 0px;
}

.terms-title {
  text-align: center;
  font-size: 24px;
  color: #333;
  margin: 0;
}

.terms-content {
  color: #333;
  line-height: 1.6;
}

.terms-section {
  margin-bottom: 40px;
}

.terms-section h2 {
  font-size: 20px;
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eaeaea;
}

.section-content {
  padding: 0 10px;
}

.section-content h3 {
  font-size: 18px;
  color: #333;
  margin: 20px 0 10px;
}

.section-content p,
.section-content ul {
  margin-bottom: 15px;
  color: #666;
}

.section-content ul {
  padding-left: 20px;
}

.section-content li {
  margin-bottom: 5px;
}

@media (max-width: 768px) {
  .terms-container {
    padding: 20px 15px;
  }

  .terms-title {
    font-size: 20px;
  }

  .terms-section h2 {
    font-size: 18px;
  }

  .section-content h3 {
    font-size: 16px;
  }
}
</style>
