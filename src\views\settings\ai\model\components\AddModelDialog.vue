<script setup lang="ts">
import { modelTypeList } from '../constant/index'
import type { FormInstance, FormRules } from 'element-plus'
import { postBizAiModelAdd } from '@/api/model/index'
import { ModelParams } from '@/api/model/type'
import { capabilitieList } from '../constant/index'

defineOptions({
  name: 'AddModelDialog',
})

const emits = defineEmits(['updateModelList'])

const props = defineProps<{
  platformId?: string | number
}>()

const dialogVisible = defineModel({ default: false })
const ruleFormRef = ref<FormInstance>()
const submitLoading = ref(false)
const form = reactive<{
  type: 'text' | 'image' | 'embedding' | 'rerank'
  /**
   * 模型Id
   */
  name: string
  /**
   * 模型名称
   */
  displayName: string
  capabilities: string[]
}>({
  type: 'text',
  name: '',
  displayName: '',
  capabilities: [],
})
interface RuleForm {
  type: string
  name: string
  capabilities: string[]
}
const rules = reactive<FormRules<RuleForm>>({
  type: [{ required: true, message: '请选择模型类型', trigger: 'change' }],
  name: [{ required: true, message: '请填写模型ID', trigger: 'blur' }],
  capabilities: [{ required: false, message: '请选择模型特征', trigger: 'change' }],
})

/**
 * 重置表单数据
 */
function resetForm() {
  if (ruleFormRef.value) {
    ruleFormRef.value.resetFields()
  }
}

function handleBeforeClose(done: () => void) {
  if (!submitLoading.value) {
    done()
  }
}

function handleClose() {
  resetForm()
}

function handleSubmit() {
  if (ruleFormRef.value) {
    ruleFormRef.value.validate(valid => {
      if (valid) {
        const params: ModelParams = {
          type: form.type,
          name: form.name,
          displayName: form.displayName || form.name,
          platformId: props.platformId,
          capabilities: [],
        }

        if (form.type === 'text') {
          params.capabilities = form.capabilities
        }

        // console.log(params, '提交参数')
        submitLoading.value = true

        postBizAiModelAdd(params)
          .then(res => {
            if (res.code === 200) {
              ElMessage.success(res.msg)
              emits('updateModelList')
              dialogVisible.value = false
            } else {
              ElMessage.error(res.msg || '添加失败')
            }
          })
          .finally(() => {
            submitLoading.value = false
          })
      }
    })
  }
}
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    width="600"
    :close-on-click-modal="false"
    :before-close="handleBeforeClose"
    @close="handleClose"
  >
    <template #header>
      <span class="text-[24px] g-family-medium">添加模型</span>
    </template>
    <el-form
      ref="ruleFormRef"
      :model="form"
      :rules="rules"
      label-width="auto"
      hide-required-asterisk
      label-position="top"
    >
      <el-form-item prop="type">
        <template #label>
          <div class="g-family-medium text-[16px]">模型类型</div>
        </template>
        <el-radio-group class="model-radio" v-model="form.type">
          <el-radio v-for="model in modelTypeList" size="large" border :value="model.value"
            >{{ model.label }}
            <img
              v-if="model.value === form.type"
              class="check-icon"
              src="@/assets/settings/checked.svg"
              alt=""
            />
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="name">
        <template #label>
          <div class="g-family-medium text-[16px]">模型ID</div>
        </template>
        <el-input v-model="form.name" :maxlength="50" />
      </el-form-item>
      <el-form-item prop="displayName">
        <template #label>
          <div class="g-family-medium text-[16px]">模型名称</div>
        </template>
        <el-input v-model="form.displayName" :maxlength="50" />
      </el-form-item>

      <el-form-item prop="capabilities" v-if="form.type === 'text'">
        <template #label>
          <div class="g-family-medium text-[16px]">模型特性</div>
        </template>
        <el-checkbox-group v-model="form.capabilities">
          <el-checkbox v-for="c in capabilitieList" :value="c.value">{{ c.label }}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button :disabled="submitLoading" @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
          {{ submitLoading ? ' 保存中...' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="less" scoped>
.model-radio {
  :deep(.el-radio.el-radio--large) {
    border-radius: 8px;
  }
  :deep(.el-radio__input) {
    display: none;
  }
}

.check-icon {
  position: absolute;
  right: 0;
  bottom: 0;
}
</style>
