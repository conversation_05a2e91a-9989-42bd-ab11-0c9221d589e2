<script setup name="User" lang="ts">
import { to } from 'await-to-js'
import type { ComponentInternalInstance } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type {
  DialogInstance,
  ElMessageBoxOptions,
  FormInstance,
  MessageBoxData,
  TreeInstance,
  UploadFile,
  UploadInstance,
} from 'element-plus'
import { Delete, Edit, More, Search, Sort } from '@element-plus/icons-vue'

// 组件导入
import DeptDialog from './components/DeptDialog.vue'
import InviteDialog from './components/InviteDialog.vue'
import Pagination from '@/components/common/Pagination/index.vue'
import SvgIcon from '@/components/common/SvgIcon/index.vue'

import api from '@/api/system/user'
import type { UserForm, UserQuery, UserVO } from '@/api/system/user/types'
import type { DeptForm, DeptVO } from '@/api/system/dept/types'
import type { RoleVO } from '@/api/system/role/types'
import type { PostVO } from '@/api/system/post/types'
import { delDept, listDept, treeselect, updateDept } from '@/api/system/dept'
import { optionselect } from '@/api/system/post'
import { useDict } from '@/utils/dict'
// zzz-add
import { addDateRange } from '@/utils/ruoyi'
import { getConfigKey } from '@/api/system/config'
// import { download } from '@/utils/request';
import { handleTree } from '@/utils/ruoyi'
import { postSystemUserChangeDept } from '@/api/system'

const modal = {
  msgSuccess(content: any) {
    ElMessage.success(content)
  },
  confirm(content: any): Promise<MessageBoxData> {
    return ElMessageBox.confirm(content, '系统提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
  },
}

interface ImportOption {
  open: boolean
  title: string
  isUploading: boolean
  updateSupport: number
  headers?: Record<string, string>
  url: string
}

interface DialogOption {
  visible: boolean
  title: string
}

interface FieldOption {
  key: number
  label: string
  visible: boolean
  children: any[]
}

interface PageData<T, Q> {
  form: T
  queryParams: Q
  rules: Record<string, any[]>
}

type DateModelType = string | Date | number

const router = useRouter()
const { proxy } = getCurrentInstance() as ComponentInternalInstance
const { sys_normal_disable, sys_user_sex } = toRefs<any>(
  useDict('sys_normal_disable', 'sys_user_sex'),
)
const userList = ref<UserVO[]>()
const loading = ref(true)
const showSearch = ref(true)
const ids = ref<Array<number | string>>([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const dateRange = ref<[DateModelType, DateModelType]>(['', ''])
const deptName = ref('')
const deptOptions = ref<DeptVO[]>([])
const initPassword = ref<string>('')
const postOptions = ref<PostVO[]>([])
const roleOptions = ref<RoleVO[]>([])
// resolved 未分配归属部门成员数量
const unabsorbedCount = ref(0)
/** * 用户导入参数 */
const upload = reactive<ImportOption>({
  // 是否显示弹出层（用户导入）
  open: false,
  // 弹出层标题（用户导入）
  title: '',
  // 是否禁用上传
  isUploading: false,
  // 是否更新已经存在的用户数据
  updateSupport: 0,
  // 设置上传的请求头部
  // headers: globalHeaders(), // temp
  // 上传的地址
  url: `${import.meta.env.VITE_APP_BASE_API}/system/user/importData`,
})
// 列显隐信息
const columns = ref<FieldOption[]>([
  { key: 0, label: '用户编号', visible: true, children: [] },
  { key: 1, label: '用户名称', visible: true, children: [] },
  { key: 2, label: '姓名', visible: true, children: [] },
  { key: 3, label: '部门', visible: true, children: [] },
  { key: 4, label: '手机号码', visible: true, children: [] },
  { key: 5, label: '状态', visible: true, children: [] },
  { key: 6, label: '创建时间', visible: true, children: [] },
])

const deptTreeRef = ref<TreeInstance>()
const queryFormRef = ref<FormInstance>()
const userFormRef = ref<FormInstance>()
const uploadRef = ref<UploadInstance>()
const formDialogRef = ref<DialogInstance>()

const dialog = reactive<DialogOption>({
  visible: false,
  title: '',
})

const initFormData: UserForm = {
  userId: undefined,
  deptId: undefined,
  nickName: '',
  password: '',
  phonenumber: undefined,
  email: undefined,
  sex: undefined,
  status: '0',
  remark: '',
  postIds: [],
  roleIds: [], // 保持数组格式，但实际使用时只会有一个值
}

const initData: PageData<UserForm, UserQuery> = {
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    nickName: '',
    phonenumber: '',
    status: '',
    deptId: '',
    roleId: '',
    unabsorbed: false,
  },
  rules: {
    nickName: [{ required: true, message: '姓名不能为空', trigger: 'blur' }],
    password: [
      { required: true, message: '用户密码不能为空', trigger: 'blur' },
      {
        min: 5,
        max: 20,
        message: '用户密码长度必须介于 5 和 20 之间',
        trigger: 'blur',
      },
      { pattern: /^[^<>"'|\\]+$/, message: '不能包含非法字符：< > " \' \\\ |', trigger: 'blur' },
    ],
    email: [
      {
        type: 'email',
        message: '请输入正确的邮箱地址',
        trigger: ['blur', 'change'],
      },
    ],
    phonenumber: [
      { required: true, message: '手机号码不能为空', trigger: 'blur' },
      {
        pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
        message: '请输入正确的手机号码',
        trigger: 'blur',
      },
    ],
    roleIds: [{ required: true, message: '用户角色不能为空', trigger: 'blur' }],
  },
}
const data = reactive<PageData<UserForm, UserQuery>>(initData)

const { queryParams, form, rules } = toRefs<PageData<UserForm, UserQuery>>(data)

// resolved 编辑模式下手机号重新填写状态
const isEditingPhoneNumber = ref(false)

/** 通过条件过滤节点  */
const filterNode = (value: string, data: any) => {
  if (!value) return true
  return data.label.includes(value)
}
/** 根据名称筛选部门树 */
watchEffect(
  () => {
    deptTreeRef.value?.filter(deptName.value)
  },
  {
    flush: 'post', // watchEffect会在DOM挂载或者更新之前就会触发，此属性控制在DOM元素更新后运行
  },
)

/** 查询部门下拉树结构 */
const getTreeSelect = async () => {
  const res = await listDept()
  res.data.forEach((item: any) => {
    item.id = item.deptId
    item.label = item.deptName
  })
  if (res.data) {
    // 使用handleTree处理数据，转换为树形结构
    deptOptions.value = handleTree<DeptVO>(res.data, 'id', 'parentId', 'children')
  }
  console.log('getTreeSelect-deptOptions', JSON.parse(JSON.stringify(deptOptions.value)))
  // resolved 设置默认选中第一个部门，把根节点id设置为0
  if (deptOptions.value && deptOptions.value.length > 0) {
    const firstDept = deptOptions.value[0]
    // 把根节点id设置为0
    firstDept.id = 0
    deptTreeRef.value?.setCurrentKey(firstDept.id)
    queryParams.value.deptId = firstDept.id
    handleQuery()
  }
}

/** 搜索按钮操作 */
// resolved 如果选中的部门是根节点，就getList时候就不要传deptId和unabsorbed，实际操作，可以在getTreeSelect把根节点id设置为0，然后getList时候判断deptId是否为0，如果为0，就不传deptId和unabsorbed
const handleQuery = (unabsorbed?: boolean) => {
  queryParams.value.pageNum = 1
  if (unabsorbed) {
    queryParams.value.unabsorbed = true
  } else {
    queryParams.value.unabsorbed = false
  }
  getList()
}

/** 节点单击事件 */
const handleNodeClick = (data: DeptVO) => {
  queryParams.value.deptId = data.id
  handleQuery()
}

/** 获取未分配归属部门成员数量 */
const getUnabsorbedCount = async () => {
  try {
    const res = await api.listUserKeyword({
      pageNum: 1,
      pageSize: 1, // 只需要获取总数，不需要详细数据
      unabsorbed: true,
    })
    unabsorbedCount.value = (res as any).total || 0
  } catch (error) {
    console.error('获取未分配归属部门成员数量失败:', error)
    unabsorbedCount.value = 0
  }
}

/** 查询用户列表 */
const getList = async () => {
  loading.value = true
  // resolved 如果deptId为0（根节点），就不传deptId和unabsorbed
  const params = { ...queryParams.value }
  if (params.deptId === 0) {
    delete params.deptId
    delete params.unabsorbed
  }
  const res = await api.listUserKeyword(params)
  loading.value = false
  userList.value = (res as any).rows
  total.value = (res as any).total
}

/** 移除按钮操作 */
const handleDelete = async (row?: UserVO) => {
  const userIds = row?.userId || ids.value
  // resolved 提示语从用户编号改为姓名
  const names =
    row?.nickName ||
    userList.value
      ?.filter(item => ids.value.includes(item.userId))
      .map(item => item.nickName)
      .join('、') ||
    '选中'
  const [err] = await to(modal.confirm(`是否确认移除"${names}"用户？`) as any)
  if (!err) {
    await api.delUser(userIds)
    await getList()
    await getUnabsorbedCount() // resolved 更新未分配归属部门成员数量
    modal.msgSuccess('移除成功')
  }
}

/** 用户状态修改  */
const handleStatusChange = async (row: UserVO) => {
  const text = row.status === '0' ? '启用' : '停用'
  try {
    await modal.confirm(`确认要"${text}""${row.nickName}"用户吗?`)
    await api.changeUserStatus(row.userId, row.status)
    modal.msgSuccess(`${text}成功`)
  } catch (err) {
    row.status = row.status === '0' ? '1' : '0'
  }
}
/** 跳转角色分配 */
const handleAuthRole = (row: UserVO) => {
  const userId = row.userId
  router.push(`/system/user-auth/role/${userId}`)
}

/** 选择条数  */
const handleSelectionChange = (selection: UserVO[]) => {
  ids.value = selection.map(item => item.userId)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

/** 导入按钮操作 */
const handleImport = () => {
  upload.title = '用户导入'
  upload.open = true
}
/** 导出按钮操作 */
const handleExport = () => {
  // download(
  //   'system/user/export',
  //   {
  //     ...queryParams.value,
  //   },
  //   `user_${new Date().getTime()}.xlsx`,
  // )
}
/** 下载模板操作 */
const importTemplate = () => {
  // download('system/user/importTemplate', {}, `user_template_${new Date().getTime()}.xlsx`)
}

/** 文件上传中处理 */
const handleFileUploadProgress = () => {
  upload.isUploading = true
}
/** 文件上传成功处理 */
const handleFileSuccess = (response: any, file: UploadFile) => {
  upload.open = false
  upload.isUploading = false
  uploadRef.value?.handleRemove(file)
  ElMessageBox.alert(
    `<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>${response.msg}</div>`,
    '导入结果',
    {
      dangerouslyUseHTMLString: true,
    },
  )
  getList()
}

/** 提交上传文件 */
function submitFileForm() {
  uploadRef.value?.submit()
}

/** 初始化部门数据 */
const initTreeData = async () => {
  // 判断部门的数据是否存在，存在不获取，不存在则获取
  if (deptOptions.value === undefined) {
    const { data } = await treeselect()
    deptOptions.value = data
  }
}

/** 重置操作表单 */
const reset = () => {
  form.value = { ...initFormData }
  userFormRef.value?.resetFields()
  // resolved 重置手机号重新填写状态
  isEditingPhoneNumber.value = false
}
/** 取消按钮 */
const cancel = () => {
  dialog.visible = false
  reset()
}

/** 新增按钮操作 */
const handleAdd = async () => {
  reset()
  const { data } = await api.getUser()
  dialog.visible = true
  dialog.title = '新增用户'
  await initTreeData()
  postOptions.value = data.posts
  roleOptions.value = data.roles
  form.value.password = initPassword.value.toString()
  // resolved 重置手机号重新填写状态
  isEditingPhoneNumber.value = false
}

/** 修改按钮操作 */
const handleUpdate = async (row?: UserForm) => {
  reset()
  const userId = row?.userId || ids.value[0]
  const { data } = await api.getUser(userId)
  dialog.visible = true
  dialog.title = '修改用户'
  await initTreeData()
  Object.assign(form.value, data.user)
  postOptions.value = data.posts
  roleOptions.value = data.roles
  form.value.postIds = data.postIds
  form.value.roleIds = data.roleIds && data.roleIds.length > 0 ? [data.roleIds[0]] : [] // 只取第一个角色ID
  form.value.password = ''
  // resolved 重置手机号重新填写状态
  isEditingPhoneNumber.value = false
}

/** 提交按钮 */
const submitForm = () => {
  userFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      // 确保roleIds是数组格式，即使只选择了一个值
      if (form.value.roleIds && !Array.isArray(form.value.roleIds)) {
        form.value.roleIds = [form.value.roleIds]
      }

      // resolved 创建提交数据的副本
      const submitData = { ...form.value }

      // resolved 在编辑模式下，如果没有点击重新填写按钮，则不传递手机号字段
      if (dialog.title === '修改用户' && !isEditingPhoneNumber.value) {
        delete submitData.phonenumber
      }

      form.value.userId ? await api.updateUser(submitData) : await api.addUser(submitData)
      modal.msgSuccess('操作成功')
      dialog.visible = false
      await getList()
      await getUnabsorbedCount() // resolved 更新未分配归属部门成员数量
    }
  })
}

/**
 * 关闭用户弹窗
 */
const closeDialog = () => {
  dialog.visible = false
  resetForm()
}

/**
 * 重置表单
 */
const resetForm = () => {
  userFormRef.value?.resetFields()
  userFormRef.value?.clearValidate()

  form.value.id = undefined
  form.value.status = '1'
  // resolved 重置手机号重新填写状态
  isEditingPhoneNumber.value = false
}
onMounted(() => {
  getTreeSelect() // 初始化部门数据
  getList() // 初始化列表数据
  getUnabsorbedCount() // resolved 初始化未分配归属部门成员数量
  getConfigKey('sys.user.initPassword').then((response: { data: string }) => {
    initPassword.value = response.data
  })
})

async function handleDeptChange(value: number | string) {
  const response = await optionselect(value)
  postOptions.value = response.data
  form.value.postIds = []
}

// 新增功能变量
const isSortMode = ref(false)
const deptBackup = ref<DeptVO[]>([])
const sortedDept = ref<DeptVO[]>([])
const deptDialog = ref({
  visible: false,
  title: '添加部门',
})

// 批量设置部门相关变量
const batchDeptDialog = ref({
  visible: false,
  title: '批量设置部门',
})
const selectedDeptId = ref<string | number>('')

// 当前悬停的节点ID
const currentHoverNodeId = ref<number | string | null>(null)

// 处理下拉菜单可见性变化
const handleDropdownVisibleChange = (visible: boolean, id: number | string) => {
  currentHoverNodeId.value = visible ? id : null
}

// 编辑部门对话框
const editDeptDialogVisible = ref(false)
const editingDept = ref<DeptVO | null>(null)

// 编辑部门
const handleEditDept = (data: DeptVO) => {
  console.log('handleEditDept-data', data)
  editingDept.value = data
  deptDialog.value.visible = true
  deptDialog.value.title = '编辑部门'
}

// 删除部门
const handleDeleteDept = async (data: DeptVO) => {
  try {
    // 检查部门下是否有用户
    // 实际业务中应该调用接口检查

    await ElMessageBox.confirm('确定要删除该部门吗？删除后将无法恢复', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    if (!data.id) {
      ElMessage.error('部门ID不能为空')
      return
    }

    // 调用删除部门API
    await delDept(data.id)
    ElMessage.success('删除成功')
    // 重新加载部门数据
    getTreeSelect()
  } catch (error) {
    if (error instanceof Error) {
      console.error('删除部门失败:', error)
      ElMessage.error(error.message || '删除失败')
    }
  }
}

// 添加部门
const handleAddDepartment = () => {
  deptDialog.value.visible = true
  deptDialog.value.title = '添加部门'
  editingDept.value = undefined
}

// 处理部门添加成功
const handleDeptAddSuccess = () => {
  // 刷新部门树
  getTreeSelect()
}

// 开启部门排序模式
const handleEnableDepartmentSort = () => {
  isSortMode.value = true
  ElMessage.info('已开启部门排序模式，可拖拽部门进行排序')

  // 备份当前部门数据
  deptBackup.value = JSON.parse(JSON.stringify(deptOptions.value))
  sortedDept.value = JSON.parse(JSON.stringify(deptOptions.value))
}

// 处理部门拖拽结束事件
const handleDeptDragEnd = (draggingNode: any, dropNode: any, dropType: string, ev: any) => {
  // 只在排序模式下处理拖拽事件
  if (!isSortMode.value) return

  // 更新排序后的部门列表
  sortedDept.value = JSON.parse(JSON.stringify(deptOptions.value))
}

// 取消部门排序
const handleCancelDeptSort = () => {
  isSortMode.value = false
  ElMessage.info('已取消部门排序')

  // 恢复原始部门数据
  if (deptBackup.value.length > 0) {
    deptOptions.value = JSON.parse(JSON.stringify(deptBackup.value))
  }

  getTreeSelect()
}

// 递归收集所有部门（包括子部门）
const collectAllDepts = (depts: DeptVO[]) => {
  const result: DeptVO[] = []

  const collect = (items: DeptVO[], parentId: string | number | null = null) => {
    if (!items || items.length === 0) return

    items.forEach((item, index) => {
      // 创建当前部门的副本，并添加到结果数组
      const dept = { ...item }
      // 设置排序号
      dept.orderNum = index + 1
      // 存储父ID（如果有）
      if (parentId !== null) {
        dept.parentId = parentId
      }
      result.push(dept)

      // 递归处理子部门
      if (dept.children && dept.children.length > 0) {
        collect(dept.children, dept.id)
      }
    })
  }

  collect(depts)
  return result
}

// 保存部门排序
const handleSaveDeptSort = async () => {
  try {
    // resolved: 实现保存部门排序的接口调用逻辑 updateDept
    if (sortedDept.value && sortedDept.value.length > 0) {
      // 收集所有部门（包括子部门）
      const allDepts = collectAllDepts(sortedDept.value)

      if (allDepts.length === 0) {
        ElMessage.warning('无可排序的部门数据')
        return
      }

      // 创建一个更新操作的promises数组
      const updatePromises = allDepts.map(dept => {
        // 创建更新用的部门表单数据
        const deptForm: DeptForm = {
          parentId: dept.parentId,
          deptName: dept.deptName,
          deptId: dept.deptId,
          orderNum: dept.orderNum, // 使用在collectAllDepts中计算的排序号
        }
        // 调用更新部门API
        return updateDept(deptForm)
      })

      // 等待所有更新操作完成
      await Promise.all(updatePromises)

      ElMessage.success('部门排序保存成功')
      isSortMode.value = false
      // 重新加载部门数据
      await getTreeSelect()
    } else {
      ElMessage.warning('无可排序的部门数据')
    }
  } catch (error) {
    console.error('保存部门排序失败:', error)
    ElMessage.error('保存部门排序失败')
  }
}

// 控制拖拽时的放置规则，只允许同级排序
const allowDrop = (draggingNode: any, dropNode: any, type: string) => {
  return true
}

/** 查看按钮操作 */
const handleView = async (row: UserForm) => {
  reset()
  const userId = row.userId
  const { data } = await api.getUser(userId)
  dialog.visible = true
  dialog.title = '查看用户'
  await initTreeData()
  Object.assign(form.value, data.user)
  postOptions.value = data.posts
  roleOptions.value = data.roles
  form.value.postIds = data.postIds
  form.value.roleIds = data.roleIds

  // resolved: 用在el-form设置disabled的方式去只读
  // 不需要使用setTimeout和DOM操作
}

// 批量设置部门
const handleBatchSetDept = () => {
  if (ids.value.length === 0) {
    ElMessage.warning('请先选择需要设置部门的用户')
    return
  }

  batchDeptDialog.value.visible = true
  selectedDeptId.value = ''
}

// 邀请成员对话框
const inviteDialog = ref({
  visible: false,
})

// 打开邀请成员对话框
const handleInviteMembers = () => {
  inviteDialog.value.visible = true
}

// resolved 重新填写手机号
const handleRewritePhoneNumber = () => {
  isEditingPhoneNumber.value = true
  form.value.phonenumber = ''
}

// 提交批量设置部门
const submitBatchSetDept = async () => {
  if (!selectedDeptId.value) {
    ElMessage.warning('请选择部门')
    return
  }

  try {
    await postSystemUserChangeDept({
      userIds: ids.value.map(id => id.toString()),
      deptId: selectedDeptId.value.toString(),
    })
    ElMessage.success('批量设置部门成功')
    batchDeptDialog.value.visible = false
    selectedDeptId.value = ''
    await getList()
    await getUnabsorbedCount() // resolved 更新未分配归属部门成员数量
  } catch (error) {
    console.error('批量设置部门失败:', error)
  }
}

// resolved 点击未分配归属部门成员
const handleUnassignedDept = () => {
  queryParams.value.deptId = undefined
  deptTreeRef.value?.setCurrentKey(undefined)
  handleQuery(true)
}
</script>

<template>
  <div class="h-full">
    <el-row class="h-full">
      <!-- 部门树 -->
      <el-col :span="4" class="!p-[20px] h-full">
        <div class="flex flex-col justify-between h-full">
          <div>
            <div class="flex justify-between items-center">
              <div class="category-tit">部门</div>
              <el-dropdown trigger="click">
                <SvgIcon
                  class="text-lg cursor-pointer hover:text-primary el-dropdown-link !w-[18px] !h-[18px]"
                  icon="icon-park:setting-config"
                />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="handleAddDepartment">
                      <el-icon><Edit /></el-icon>
                      添加部门
                    </el-dropdown-item>
                    <el-dropdown-item @click="handleEnableDepartmentSort">
                      <el-icon><Sort /></el-icon>
                      部门排序
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
            <div v-if="isSortMode" class="sort-mode-controls mt-2">
              <el-alert
                title="排序模式已开启，请拖动部门进行排序"
                type="info"
                :closable="false"
                show-icon
              />
              <div class="flex justify-end mt-2">
                <el-button @click="handleCancelDeptSort">取消</el-button>
                <el-button type="primary" @click="handleSaveDeptSort">保存排序</el-button>
              </div>
            </div>
            <div class="my-tree mr-[-20px] pr-[20px] overflow-y-auto max-h-[calc(100vh-200px)]">
              <!-- 使用视窗高度替代百分比，确保高度计算的准确性 -->
              <!-- resolved 部门太多超出可视高度怎么处理 -->
              <el-tree
                ref="deptTreeRef"
                style="background: transparent"
                class="mt-2"
                node-key="id"
                :data="deptOptions"
                :props="{ label: 'label', children: 'children' }"
                :expand-on-click-node="false"
                :filter-node-method="filterNode"
                highlight-current
                default-expand-all
                :draggable="isSortMode"
                :allow-drop="allowDrop"
                @node-click="handleNodeClick"
                @node-drag-end="handleDeptDragEnd"
              >
                <template #default="{ node, data }">
                  <div class="tree-node-content">
                    <div class="flex items-center">
                      <el-icon v-if="isSortMode && data.id" class="drag-icon mr-1"
                        ><Sort
                      /></el-icon>
                      <span>{{ data.label }}</span>
                    </div>
                    <div
                      v-if="data.id"
                      class="tree-node-actions"
                      :class="{ show: currentHoverNodeId === data.id || node.isCurrent }"
                      @click.stop
                    >
                      <el-dropdown
                        trigger="click"
                        @visible-change="
                          (visible: boolean) => handleDropdownVisibleChange(visible, data.id)
                        "
                      >
                        <div class="content-right-btn el-dropdown-link">
                          <el-icon><More /></el-icon>
                        </div>
                        <!-- resolved 调用接口updateDept、delDept -->
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item @click="handleEditDept(data)">
                              <el-icon><Edit /></el-icon>
                              编辑部门
                            </el-dropdown-item>
                            <el-dropdown-item @click="handleDeleteDept(data)">
                              <el-icon style="color: #f25b37"><Delete /></el-icon>
                              <span style="color: #f25b37">删除部门</span>
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </div>
                  </div>
                </template>
              </el-tree>
            </div>
          </div>
          <!-- resolved 点击未分配归属部门成员，queryParams.value.deptId = undefined，且deptTreeRef不选中任何项 -->
          <!-- resolved 请求一次接口，获取total作为未分配归属部门成员数量 -->
          <el-button class="w-full" type="info" plain @click="handleUnassignedDept"
            >未分配归属部门（{{ unabsorbedCount }}）</el-button
          >
        </div>
      </el-col>
      <el-col :span="20" class="border-l h-full !p-[20px]">
        <div>
          <transition>
            <!-- :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave" -->
            <div v-show="showSearch">
              <el-card class="my-card" shadow="hover" style="background: transparent" />
            </div>
          </transition>

          <el-card class="my-card" shadow="hover" style="background: transparent">
            <template #header>
              <div class="flex justify-between">
                <div class="flex">
                  <el-dropdown class="mt-[1px]">
                    <el-button class="ex-el-button-gray" type="primary" plain>
                      <i class="iconfont iconfont-c icon-piliang mr-2"></i>
                      批量操作
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <!-- resolved 应该判断是否已经勾选用户 -->
                        <el-dropdown-item :disabled="multiple" @click="handleDelete()">
                          批量移除成员
                        </el-dropdown-item>
                        <el-dropdown-item :disabled="multiple" @click="handleBatchSetDept">
                          批量设置部门
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                  <el-button
                    v-has-permi="['system:user:add']"
                    class="ml-3 ex-el-button-gray"
                    type="primary"
                    plain
                    @click="handleAdd()"
                  >
                    <i class="iconfont iconfont-c icon-xinzeng mr-2"></i>
                    新增
                  </el-button>
                  <el-button
                    v-has-permi="['system:user:add']"
                    class="ml-3 ex-el-button-gray"
                    type="primary"
                    plain
                    @click="handleInviteMembers"
                  >
                    <i class="iconfont iconfont-c icon-lianjie mr-2"></i>
                    邀请链接
                  </el-button>
                </div>
                <div class="flex justify-end">
                  <el-form
                    ref="queryFormRef"
                    :model="queryParams"
                    :inline="true"
                    class="text-right"
                  >
                    <el-form-item label="" prop="nickName" class="!mr-0">
                      <el-input
                        v-model="queryParams.keyword"
                        style="width: 400px"
                        placeholder="请输入你需要搜索的内容"
                        clearable
                        @clear="handleQuery"
                        @keyup.enter="handleQuery"
                      >
                        <template #suffix>
                          <el-icon>
                            <Search />
                          </el-icon>
                        </template>
                      </el-input>
                    </el-form-item>
                  </el-form>
                  <!-- <el-button class="ml-3" type="primary" plain @click="handleQuery">高级搜索</el-button> -->
                </div>
              </div>
            </template>

            <el-table
              v-loading="loading"
              :data="userList"
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="50" align="center" />
              <el-table-column type="index" label="序号" width="60" align="center" />
              <el-table-column
                key="nickName"
                label="姓名"
                align="center"
                prop="nickName"
                :show-overflow-tooltip="true"
              />
              <el-table-column
                key="phonenumber"
                label="手机号码"
                align="center"
                prop="phonenumber"
                width="120"
              >
                <template #default="scope">
                  <span>{{ scope.row.phonenumber || '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column
                key="deptName"
                label="部门"
                align="center"
                prop="deptName"
                :show-overflow-tooltip="true"
              >
                <template #default="scope">
                  <span>{{ scope.row.deptName || '未分配' }}</span>
                </template>
              </el-table-column>
              <el-table-column key="role" label="角色" align="center" prop="role" width="100">
                <template #default="scope">
                  <span>{{ scope.row.roles?.[0]?.roleName || '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="加入时间" align="center" prop="joinDate" width="160">
                <template #default="scope">
                  <span>{{ scope.row.joinDate || '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column
                key="joinDate"
                label="激活状态"
                align="center"
                prop="joinDate"
                width="80"
              >
                <template #default="scope">
                  <span>{{
                    scope.row.activeState === 0 || scope.row.activeState === '0'
                      ? '已激活'
                      : '未激活'
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column key="status" label="启用" align="center">
                <template #default="scope">
                  <el-switch
                    v-model="scope.row.status"
                    :disabled="scope.row.activeState !== 0 && scope.row.activeState !== '0'"
                    active-value="0"
                    inactive-value="1"
                    @change="handleStatusChange(scope.row)"
                  />
                </template>
              </el-table-column>

              <el-table-column
                label="操作"
                fixed="right"
                width="180"
                class-name="small-padding fixed-width"
              >
                <template #default="scope">
                  <span>
                    <!-- resolved: 写一下查看的逻辑 -->
                    <el-button link type="primary" @click="handleView(scope.row)">查看</el-button>
                  </span>
                  <span v-if="scope.row.userId !== 1" v-hasPermi="['system:user:edit']">
                    <el-button link type="primary" @click="handleUpdate(scope.row)">编辑</el-button>
                  </span>
                  <span v-if="scope.row.userId !== 1" v-hasPermi="['system:user:remove']">
                    <el-button link type="danger" @click="handleDelete(scope.row)"
                      >移除成员</el-button
                    >
                  </span>
                </template>
              </el-table-column>
            </el-table>
            <Pagination
              v-show="total > 0"
              v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize"
              :total="total"
              @pagination="getList"
            />
          </el-card>
        </div>
      </el-col>
    </el-row>

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog
      ref="formDialogRef"
      v-model="dialog.visible"
      draggable
      :title="dialog.title"
      width="400px"
      append-to-body
      @close="closeDialog"
    >
      <el-form
        ref="userFormRef"
        :model="form"
        :rules="dialog.title === '查看用户' ? {} : rules"
        :disabled="dialog.title === '查看用户'"
        label-width="80px"
        label-position="top"
      >
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="姓名" prop="nickName">
              <el-input v-model="form.nickName" placeholder="请输入姓名" maxlength="30" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <!-- resolved  补充按钮【重新填写】，点击后再去对用户输入的手机号进行格式校验，以及必填校验。如果不点击该按钮，手机号字段（phonenumber），就不传 -->
            <el-form-item
              label="手机号码"
              :prop="dialog.title === '修改用户' && !isEditingPhoneNumber ? '' : 'phonenumber'"
            >
              <el-input
                v-model="form.phonenumber"
                :placeholder="dialog.title === '查看用户' ? '' : '请输入手机号码'"
                :disabled="
                  dialog.title === '查看用户' ||
                  (dialog.title === '修改用户' && !isEditingPhoneNumber)
                "
                maxlength="11"
              >
                <template v-if="dialog.title === '修改用户' && !isEditingPhoneNumber" #suffix>
                  <el-button type="primary" link @click="handleRewritePhoneNumber">
                    重新填写
                  </el-button>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="归属部门" prop="deptId">
              <el-tree-select
                v-model="form.deptId"
                :data="deptOptions"
                :props="{ value: 'id', label: 'label', children: 'children' }"
                value-key="id"
                placeholder="请选择归属部门"
                check-strictly
                @change="handleDeptChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="角色" prop="roleIds">
              <!-- resolved: 处理一下，目前roleIds数据是数组格式，但原型要求此处是单选 -->
              <el-select v-model="form.roleIds[0]" filterable placeholder="请选择角色" clearable>
                <el-option
                  v-for="item in roleOptions"
                  :key="item.roleId"
                  :label="item.roleName"
                  :value="item.roleId"
                  :disabled="item.status === '1'"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel()"> 取消 </el-button>
          <el-button v-if="dialog.title !== '查看用户'" type="primary" @click="submitForm">
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog v-model="upload.open" draggable :title="upload.title" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="`${upload.url}?updateSupport=${upload.updateSupport}`"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload">
          <i-ep-upload-filled />
        </el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="text-center el-upload__tip">
            <div class="el-upload__tip">
              <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的用户数据
            </div>
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link
              type="primary"
              :underline="false"
              style="font-size: 12px; vertical-align: baseline"
              @click="importTemplate"
            >
              下载模板
            </el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm"> 确 定 </el-button>
          <el-button @click="upload.open = false"> 取 消 </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 部门对话框 -->
    <DeptDialog
      v-model:visible="deptDialog.visible"
      :title="deptDialog.title"
      :editing-dept="editingDept"
      @success="handleDeptAddSuccess"
    />

    <!-- 批量设置部门对话框 -->
    <el-dialog
      v-model="batchDeptDialog.visible"
      draggable
      :title="batchDeptDialog.title"
      width="400px"
      append-to-body
      @close="batchDeptDialog.visible = false"
    >
      <el-form label-width="80px" label-position="top">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="归属部门">
              <el-tree-select
                v-model="selectedDeptId"
                :data="deptOptions"
                :props="{ value: 'id', label: 'label', children: 'children' }"
                value-key="id"
                placeholder="请选择归属部门"
                check-strictly
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchDeptDialog.visible = false"> 取消 </el-button>
          <el-button type="primary" @click="submitBatchSetDept"> 保存 </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 邀请成员对话框 -->
    <InviteDialog v-model:visible="inviteDialog.visible" />
  </div>
</template>

<style lang="less" scoped>
.my-card {
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
  :deep(.el-card__header) {
    padding: 0 !important;
  }
  :deep(.el-card__body) {
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
  }
}

.category-tit {
  width: 32px;
  height: 22px;
  font-size: 14px;
  color: #646a73;
  line-height: 22px;
  text-align: left;
  font-style: normal;
}

.sort-mode-controls {
  margin-top: 10px;
}

.el-dropdown-link {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  &:hover {
    background: rgba(230, 234, 244, 0.6);
    border-radius: 4px;
  }
}
.el-button:focus-visible {
  outline: none; // 生效
}

.tree-node-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 8px;
  width: 100%;

  .drag-icon {
    color: #909399;
    cursor: move;
    &:hover {
      color: #4d5bec;
    }
  }

  .tree-node-actions {
    display: none;
    &.show {
      display: block;
    }
  }

  &:hover {
    .tree-node-actions {
      display: block;
    }
  }
}

.content-right-btn {
  height: 32px;
  width: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  cursor: pointer;
}

// 其实和全局滚动条样式一样
.my-tree {
  // 美化滚动条
  &::-webkit-scrollbar {
    width: 5px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}
</style>
