import axios, { type AxiosResponse } from 'axios'
import ms from '@/utils/message'
import { encrypt as rsaEncrypt } from '@/components/common/LoginDialog/utils/jsencrypt'
import {
  encryptBase64,
  encryptWithAes,
  generateAesKey,
} from '@/components/common/LoginDialog/utils/crypto'
import {
  getCurrentToken,
  getRequestAgentContext,
  setRequestAgentContext,
} from '@/utils/token-manager'

const service = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API,
})

service.interceptors.request.use(
  config => {
    const token = getCurrentToken()
    // console.log('getCurrentToken', token)
    if (token && !config.url?.includes('/auth/getToken/')) {
      config.headers.Authorization = `Bearer ${token}`
    }
    config.headers.Clientid = import.meta.env.VITE_APP_CLIENT_ID

    // 是否需要加密
    const isEncrypt = config.headers?.isEncrypt === true || config.headers?.isEncrypt === 'true'
    if (isEncrypt && (config.method === 'post' || config.method === 'put')) {
      // 生成AES密钥
      const aesKey = generateAesKey()
      config.headers['Encrypt-Key'] = rsaEncrypt(encryptBase64(aesKey))
      config.data =
        typeof config.data === 'object'
          ? encryptWithAes(JSON.stringify(config.data), aesKey)
          : encryptWithAes(config.data, aesKey)
    }

    return config
  },
  error => {
    return Promise.reject(error.response)
  },
)

service.interceptors.response.use(
  (response: AxiosResponse): AxiosResponse => {
    if (response.status === 200) {
      // console.log('response.data.code', response.data.code)
      if (response.data.code === 200) {
        return response
      } else {
        let message = '请求失败'
        // 检查是否为 getBizAiModelCheckModelStatus 接口且 code 为 500
        const isCheckModelStatusWith500 =
          response.config.url?.includes('/biz/aiModel/checkModelStatus') &&
          response.data.code === 500

        switch (response.data.code) {
          case 401:
            message = '未授权，请重新登录'
            ms.warning(message)
            break
          case 403:
            message = '拒绝访问'
            ms.error(message)
            break
          case 404:
            message = '请求错误，未找到该资源'
            ms.error(message)
            break
          case 500:
            message = response.data?.msg || '服务器内部错误'
            // 仅当不是 checkModelStatus 接口时才显示错误
            if (!isCheckModelStatusWith500) ms.error(message)
            break
          default:
            message = response.data?.msg || '请求失败'
        }

        // 对于checkModelStatus接口当code为500时特殊处理
        if (isCheckModelStatusWith500) {
          return response
        }

        throw new Error(response.data.msg || '请求失败')
      }
    }
    throw new Error(response.status.toString())
  },
  error => {
    let message = '请求失败'
    const status = error.response?.status
    switch (status) {
      case 400:
        message = '请求格式有误，请检查输入'
        break
      case 401:
        message = '登录已过期，请重新登录'
        break
      case 403:
        message = '暂无权限，请联系管理员'
        break
      case 404:
        message = '请求的资源不存在'
        break
      case 500:
        message = '服务器开小差了，请稍后重试'
        break
      case 502:
        message = '网络连接异常，请检查网络'
        break
      case 503:
        message = '服务正在维护，请稍后再试'
        break
      case 504:
        message = '网络超时，请稍后重试'
        break
      default:
        if (error.code === 'ECONNABORTED') {
          message = '请求超时，请检查网络'
        } else if (!error.response) {
          message = '网络连接失败，请检查网络'
        } else {
          message = error.response?.data?.message || '操作失败，请稍后重试'
        }
    }
    ms.error(message)
    // ms.error(error.message || '请求失败')
    return Promise.reject(error)
  },
)

export const globalHeaders = () => {
  const token = getCurrentToken()
  return {
    Authorization: `Bearer ${token}`,
    clientid: import.meta.env.VITE_APP_CLIENT_ID,
  }
}

// 重新导出token管理函数，保持向后兼容
export { setRequestAgentContext, getRequestAgentContext }

export default service
