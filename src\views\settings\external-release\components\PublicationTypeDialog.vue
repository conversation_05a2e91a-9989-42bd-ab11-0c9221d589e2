<script setup lang="ts">
import { OutwardType } from '@/api/external-release/type'
import webTypeIcon from '@/assets/settings/external-release/web-type-icon.png'
import wechatBotTypeIcon from '@/assets/settings/external-release/wechat-bot-type-icon.png'
import embedTypeIcon from '@/assets/settings/external-release/embed-type-icon.png'
import apiTypeIcon from '@/assets/settings/external-release/api-type-icon.png'

defineOptions({
  name: 'PublicationTypeDialog',
})

const emits = defineEmits(['selection-change'])

const dialogVisible = defineModel({ default: false })

function handleClose() {
  dialogVisible.value = false
}

/**
 * 发布类型列表
 */
const publicationTypeList = [
  {
    icon: webTypeIcon,
    name: '网页对话',
    key: OutwardType.Web,
    publish: true,
  },
  {
    icon: wechatBotTypeIcon,
    name: '企微机器人',
    key: OutwardType.Wechat,
    publish: true,
  },
  {
    icon: embedTypeIcon,
    name: '嵌入悬浮框',
    key: OutwardType.Floating,
    publish: false,
  },
  {
    icon: apiTypeIcon,
    name: 'API',
    key: OutwardType.API,
    publish: false,
  },
]

/**
 * 选择发布类型
 */
const handleSelectPublicationType = (type: OutwardType) => {
  handleClose()
  emits('selection-change', type)
}
</script>

<template>
  <el-dialog v-model="dialogVisible" width="1000" @close="handleClose">
    <template #header>
      <span class="text-[24px] g-family-medium">选择发布类型</span>
    </template>
    <div class="flex justify-between items-center w-[80%] mx-auto my-[40px]">
      <div v-for="item in publicationTypeList" class="type-item" :class="{ publish: item.publish }">
        <img
          class="type-icon"
          :src="item.icon"
          alt=""
          @click="item.publish && handleSelectPublicationType(item.key)"
        />
        <div class="type-name g-family-medium">{{ item.name }}</div>
      </div>
    </div>
  </el-dialog>
</template>

<style lang="less" scoped>
.type-item {
  text-align: center;
  width: 140px;
  .type-icon {
    width: 140px;
    height: 140px;
    transition: all 0.3s ease-in-out;
    border-radius: 16px;
  }
  .type-name {
    margin-top: 26px;
    font-size: 16px;
    cursor: default;
  }

  .publish&:hover {
    .type-icon {
      border: 2px solid #4865e8;
      transform: scale(1.07);
    }
    .type-name {
      color: #4865e8;
    }
  }
}
</style>
