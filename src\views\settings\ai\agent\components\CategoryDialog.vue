<script setup lang="ts">
import { ref, watch } from 'vue'
import type { FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { postBizAgentCategoryAddOrUpdate } from '@/api/agent'
import type { AgentCategoryVo } from '@/typings/agent'

const props = defineProps<{
  visible: boolean
  title: string
  category?: AgentCategoryVo | null
}>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

const categoryForm = ref({
  name: '',
  orderNum: 0,
})

const categoryFormRef = ref<any>(null)

// 表单验证规则
const categoryFormRules: FormRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' },
  ],
}

// 监听visible变化，当打开对话框时初始化表单数据
const dialogVisible = ref(props.visible)
watch(
  () => props.visible,
  newVal => {
    dialogVisible.value = newVal
    if (newVal && props.category) {
      categoryForm.value.name = props.category.name || ''
      categoryForm.value.orderNum = props.category.orderNum || 0
    } else {
      categoryForm.value.name = ''
      categoryForm.value.orderNum = 0
    }
  },
)

// 监听dialogVisible变化，同步到父组件
watch(
  () => dialogVisible.value,
  newVal => {
    emit('update:visible', newVal)
  },
)

// 提交表单
const handleSubmit = async () => {
  try {
    await categoryFormRef.value.validate()
    const formData = {
      ...categoryForm.value,
      ...(props.category?.id ? { id: props.category.id } : {}),
    }

    const res = await postBizAgentCategoryAddOrUpdate<API.Response<any>>(formData)

    if (res.code === 200) {
      ElMessage.success(props.category ? '更新成功' : '添加成功')
      dialogVisible.value = false
      emit('success')
    } else {
      ElMessage.error(res.msg || (props.category ? '更新失败' : '添加失败'))
    }
  } catch (error) {
    if (error instanceof Error) {
      console.error(props.category ? '更新分类失败:' : '添加分类失败:', error)
      ElMessage.error(error.message || (props.category ? '更新失败' : '添加失败'))
    }
  }
}
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    draggable
    :title="title"
    width="400px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form
      ref="categoryFormRef"
      :model="categoryForm"
      :rules="categoryFormRules"
      label-width="80px"
      label-position="top"
    >
      <el-form-item label="分类名称" prop="name">
        <el-input v-model="categoryForm.name" placeholder="请输入分类名称" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>
