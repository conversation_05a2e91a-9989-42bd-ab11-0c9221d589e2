<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useChatStore } from '@/store'
import { postBizConversationApplyAgent, postBizConversationCreateConversation } from '@/api'
import MarkdownEditor from '@/components/MarkdownEditor.vue'

const props = withDefaults(
  defineProps<{
    modelValue: boolean
    agent: Agent.AgentVo | null
    showEdit?: boolean
  }>(),
  {
    showEdit: true,
  },
)
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  edit: [agent: Agent.AgentVo | null]
  startChat: []
}>()
const router = useRouter()
const chatStore = useChatStore()

const prompt = ref('')
const agentPrompt = ref('')

// 当agent改变时，更新agentPrompt
watch(
  () => props.agent,
  newAgent => {
    if (newAgent) {
      agentPrompt.value = newAgent.prompt || ''
    }
  },
  { immediate: true },
)

const handleClose = () => {
  emit('update:modelValue', false)
}

const handleEditAgent = () => {
  emit('edit', props.agent)
}

const handleStartChat = async () => {
  // resolved 调用的接口改为postBizConversationApplyAgent
  try {
    if (!props.agent?.id) {
      ElMessage.error('助手ID不存在')
      return
    }

    // 应用助手创建对话
    const { data } = await postBizConversationApplyAgent<{
      // resolved 这里是定义返回类型，不是入参类型
      id: string
      name: string
      subtitle: string | null
      modelId: number
      maxContextCount: number
      systemMessage: string
      lastQuestion: string | null
      lastChatTime: string
      lastChatId: string | null
    }>({
      id: props.agent.id,
    })

    if (data?.id) {
      // 添加历史记录
      chatStore.addHistory({
        title: props.agent?.name || '新对话',
        conversationId: data.id,
      })

      // 如果有系统消息，则添加到对话中
      // if (data.systemMessage) {
      //   chatStore.addChatByConversationId(data.id, {
      //     dateTime: new Date().toLocaleString(),
      //     text: data.systemMessage, // 系统消息内容放在text字段
      //     inversion: false, // 不是用户发送的消息
      //     error: false,
      //     loading: false,
      //     conversationOptions: null,
      //   })
      // }

      // 关闭对话框
      emit('update:modelValue', false)

      // 跳转到聊天页面
      router.push({ name: 'Chat', params: { conversationId: data.id } })
    }
  } catch (error) {
    console.error('创建对话失败:', error)
  }
}
</script>

<template>
  <el-dialog
    draggable
    :model-value="modelValue"
    title="助手详情"
    width="1000px"
    :before-close="handleClose"
    @update:model-value="(val: boolean) => emit('update:modelValue', val)"
  >
    <div v-if="agent" class="dialog-content">
      <!-- resolved 接入对应数据，头像的实现参考src\views\agent\my-agent\components\AgentThemeDialog.vue -->
      <div class="agent-info">
        <div class="emoji-wrap" :style="{ backgroundColor: agent.backgroundColor }">
          <img v-if="agent.emoji" :src="agent.emoji" class="emoji" />
        </div>
        <div class="info">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="title">{{ agent.name }}</h3>
              <p class="create-time">创建者｜2024-09-02 08:45:07</p>
            </div>
            <div v-if="showEdit" class="flex items-center">
              <template v-if="agent.status === 1">
                <el-icon size="16" color="#1EBF60">
                  <SuccessFilled />
                </el-icon>
                <span class="ml-1">已发布</span>
              </template>
              <template v-else-if="agent.status === 2">
                <el-icon size="16" color="#F25B37">
                  <CircleCloseFilled />
                </el-icon>
                <span class="ml-1">审核失败</span>
              </template>
              <template v-else-if="agent.status === 0">
                <el-icon size="16" color="#FF9A27">
                  <Clock />
                </el-icon>
                <span class="ml-1">审核中</span>
              </template>
              <template v-else>
                <el-icon size="16" color="#4c5cec">
                  <Lock />
                </el-icon>
                <span class="ml-1">私人助手</span>
              </template>
            </div>
          </div>
          <p class="description">{{ agent.description }}</p>
        </div>
      </div>

      <div class="prompt-section">
        <MarkdownEditor v-model:model-value="agentPrompt" disabled />
        <!-- <el-input
          v-model="agentPrompt"
          readonly
          type="textarea"
          :rows="10"
          placeholder="请输入提示词，帮助AI更好地理解您的需求..."
          class="prompt-input"
          input-style="border-radius: 8px"
        /> -->
      </div>
      <!-- 审核失败原因 -->
      <div v-if="agent.status === 2" class="fail-reason">
        <el-icon size="16" color="#F25B37">
          <WarningFilled />
        </el-icon>
        <span
          >审核失败：失败原因说明失败原因说明失败原因说明失败原因说明失败原因说明失败原因说明失败原因说明失败原因说明失败原因说明失败原因说明失败原因说明</span
        >
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <!-- resolved 补全编辑功能 -->
        <div>
          <el-button
            v-if="showEdit"
            class="w-[120px]"
            type="primary"
            plain
            @click="handleEditAgent"
          >
            编辑
          </el-button>
        </div>
        <el-button
          :disabled="!agent?.status || ![-1, 1].includes(agent.status)"
          class="w-[120px]"
          type="primary"
          @click="handleStartChat"
        >
          开始对话
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="less" scoped>
.dialog-content {
  // padding: 20px;
  border-top: 1px solid #dadde8;
  padding-top: 16px;

  .agent-info {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;

    .dialog-avatar {
      width: 72px;
      height: 72px;
      border-radius: 8px;
    }

    .info {
      flex: 1;

      .title {
        font-size: 18px;
        color: #30343a;
        line-height: 25px;
        text-align: left;
        font-style: normal;
        margin-bottom: 8px;
      }
      .create-time {
        font-size: 14px;
        color: #969ba4;
        line-height: 20px;
        text-align: left;
        font-style: normal;
      }

      .description {
        margin-top: 8px;
        font-size: 14px;
        color: #666;
        line-height: 1.5;
        // 限制两行
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }
  }

  .prompt-section {
    height: 300px;
    .prompt-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 12px;
    }

    .prompt-input {
      width: 100%;

      :deep(.el-textarea__inner) {
        border-color: #e4e7ed;

        &:focus {
          border-color: #4c5cec;
        }
      }
    }
  }
}
.fail-reason {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-top: 12px;
  font-size: 14px;
  color: #f25b37;

  .el-icon {
    margin-top: 3px;
  }

  span {
    line-height: 1.5;
  }
}
.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
