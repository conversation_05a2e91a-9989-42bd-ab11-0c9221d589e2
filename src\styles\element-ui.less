@import "./variables.less";
:root {
  --el-font-family:
    "Alibaba PuHuiTi", -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue",
    <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol",
    "Noto Color Emoji";

  --el-fill-color-blank: transparent !important; // !
  --el-color-primary: #4c5cec !important; // !
  --el-color-primary-light-9: transparent !important; // ！
  --el-button-hover-link-text-color: #4c5cec;
  // --el-color-primary-light-5: #4c5cec !important; // ！
  // --el-color-primary-light-5: #DADDE8!important; // 边框色不改这个，能生效但是也影响部分文字颜色
  --el-color-primary-light-7: #4c5cec !important; // ！
  --el-color-primary-light-8: #dadde8 !important; // ！

  --el-button-border-color: #dadde8;

  // --el-button-active-bg-color
  .el-button--primary.is-plain {
    // border-color: #dadde8; // 边框色改这里
    border-color: #4865e8; // 边框色改这里
  }
  .el-button--primary.is-plain.is-disabled {
    color: #4c5cec;
  }
}

// 强制为Element Plus组件应用阿里巴巴普惠体
.el-button,
.el-input,
.el-select,
.el-dialog,
.el-dropdown,
.el-menu,
.el-table,
.el-pagination,
.el-message,
.el-message-box,
.el-notification,
.el-popover,
.el-tooltip {
  font-family: "Alibaba PuHuiTi", sans-serif !important;
}

.my-tree {
  .el-tree-node > .el-tree-node__content {
    border-radius: 8px;
  }
}

.el-tree-node__content {
  &:hover {
    background-color: @bg-hover !important;
  }
}

.el-tree-node > .el-tree-node__content {
  // border-radius: 8px;
  height: 40px;
  .content-right-btn {
    &:hover {
      background-color: rgb(229, 232, 240);
    }
  }
}
// 添加部门树选中节点的样式
.el-tree-node.is-current > .el-tree-node__content {
  background-color: @bg-active !important;
  .content-right-btn {
    &:hover {
      background-color: rgb(216, 221, 243) !important;
    }
  }
}

.el-select {
  .el-input__wrapper {
    min-height: 40px;
  }
  .el-select__wrapper {
    min-height: 40px;
    border-radius: 8px;
  }
}
.el-select {
  --el-select-input-focus-border-color: var(--el-color-primary);
}

.el-button {
  height: 40px !important;
  min-width: 96px;
  border-radius: 8px !important;
  cursor: pointer !important;
  // font-size: 14px !important;
  &:hover {
    .iconfont {
      color: #fff;
    }
  }
}

.el-color-dropdown__btns {
  .el-button {
    min-width: 0px !important;
  }
}

.el-button.is-link {
  height: auto !important;
  min-width: 0;
}

.el-input {
  min-height: 40px !important;
  .el-input__wrapper {
    min-height: 40px !important;
    border-radius: 8px;
  }
  .el-input__inner {
    min-height: 38px !important;
  }
}

.el-table {
  tr th {
    background: rgb(232, 236, 241) !important;
    color: #30343a;
  }
  .el-table__body {
    background: #f7f8fa;
  }
  .el-table__row {
    background: #f7f8fa;
  }

  .el-table__body tr.hover-row > td.el-table__cell {
    background-color: #e6eaf4;
  }
}
// 有效 但是怕影响范围太大，估计这么写也没问题
// .el-table {
//   tr th{
//     background: #F7F8FA!important;
//   }
//   .el-table__body{
//     background: #F7F8FA;
//   }
//   .el-table__row{
//     background: #F7F8FA;
//   }
// }

.el-dialog {
  border-radius: 16px !important;
}

.el-textarea {
  border-radius: 8px !important;
}

.el-dropdown-link {
  &:focus {
    outline: none; /* 去掉聚焦时的边框 */
  }
}

.el-dialog__headerbtn {
  padding-top: 17px !important;
  padding-right: 2px !important;
  .el-dialog__close {
    font-size: 24px !important;
    font-weight: bold;
  }
}
