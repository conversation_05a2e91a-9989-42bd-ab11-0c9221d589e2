apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/proxy-connect-timeout: '5'
  name: chatgpt-web
spec:
  rules:
    - host: chatgpt.example.com
      http:
        paths:
          - backend:
              service:
                name: chatgpt-web
                port:
                  number: 3002
            path: /
            pathType: ImplementationSpecific
  tls:
    - secretName: chatgpt-web-tls
