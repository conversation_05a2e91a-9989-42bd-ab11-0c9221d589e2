import type { Ref } from 'vue'
import { computed, nextTick, ref } from 'vue'
import { ElMessageBox } from 'element-plus'
import { useChat } from './useChat'
import { useUserStore } from '@/store/modules/user'
import { useChatStore } from '@/store'
import { t } from '@/locales'
import {
  clearBizGuestContext,
  getBizChatContentRecordList,
  postBizChatRemove,
  postBizChatUpdate,
  postBizConversationClearContext,
  postBizConversationCloseConversation,
  updateBizGuestChat,
} from '@/api'
import ms from '@/utils/message'
import { streamResponseManager } from '@/utils/streamResponseManager'
import { usePublicChat } from './usePublicChat'
// import { setStreamResponseMode } from '@/views/chat/components/Message/markdown-log'

export function useConversationActions(
  conversationId: string,
  scrollToBottomIfAtBottom: () => void,
  scrollToBottom?: () => void,
) {
  const { updateChat, updateChatSome, getChatByConversationIdAndIndex } = useChat()
  const chatStore = useChatStore()
  const { isPublicChat } = usePublicChat()
  const postConversationClearContext = isPublicChat ? clearBizGuestContext : postBizConversationClearContext
  const postChatUpdate = isPublicChat ? updateBizGuestChat : postBizChatUpdate

  // 使用streamResponseManager获取当前会话的loading状态
  // 直接返回内部的ref，这样Vue可以直接追踪到变化
  const loading = streamResponseManager.getLoadingRef(conversationId)

  // 编辑消息
  async function handleEdit(text: string, index: number, currentModel: string) {
    let message = chatStore.getChatByConversationId(conversationId)[index]
    if (!message?.conversationOptions?.id) {
      // 重新获取聊天记录
      // 注意: 这里应该调用fetchChatRecord，但由于该函数在useChatRecords中，我们暂时不处理这里的逻辑
      // 可以在组件中将fetchChatRecord传入，或者重构这个函数
      message = chatStore.getChatByConversationId(conversationId)[index]
    }
    // todo 我发出的消息的接口只通过sse返回ai回复的内容，不返回我发出的消息的id，所以这里需要处理
    if (!message?.conversationOptions?.id) {
      ms.error('无法编辑该消息')
      return
    }

    try {
      await postChatUpdate({
        id: message.conversationOptions.id,
        chatId: message.conversationOptions.chatId,
        conversationId,
        modelId: chatStore.modelId,
        role: message.inversion ? 'user' : 'assistant',
        content: text,
      })

      updateChat(conversationId, index, {
        dateTime: new Date().toLocaleString(),
        text,
        inversion: message.inversion,
        error: false,
        loading: false,
        conversationOptions: message.conversationOptions,
      })

      // 编辑完成后滚动到对应位置
      scrollToBottomIfAtBottom()

      ms.success('编辑成功')
    } catch (error: any) {
      ms.error('编辑失败')
    }
  }

  // 删除消息
  function handleDelete(index: number) {
    // resolved 这个dialog改成eldialog
    ElMessageBox.confirm(t('chat.deleteMessageConfirm'), t('chat.deleteMessage'), {
      confirmButtonText: t('common.yes'),
      cancelButtonText: t('common.no'),
      type: 'warning',
    })
      .then(async () => {
        try {
          let message = chatStore.getChatByConversationId(conversationId)[index]
          if (!message?.conversationOptions?.id) {
            // 重新获取聊天记录
            // 同样，这里应该调用fetchChatRecord
            message = chatStore.getChatByConversationId(conversationId)[index]
          }
          if (message?.conversationOptions?.id) {
            await postBizChatRemove({
              id: message.conversationOptions.id,
            })
          }
          chatStore.deleteChatByConversationId(conversationId, index)
          ms.success(t('common.deleteSuccess'))
        } catch (e: any) {
          // ms.error(t('common.deleteFailed'))
          console.error(e)
        }
      })
      .catch(() => {
        // 用户取消删除，不做任何操作
      })
  }

  // 清空上下文
  function handleClear(
    showNewContextValue: boolean,
    latestChatIdValue: string | undefined,
  ): Promise<{ latestChatId: undefined; showNewContext: boolean } | null> {
    return new Promise(resolve => {
      ElMessageBox.confirm('是否清空对话上下文', '提示', {
        confirmButtonText: t('common.yes'),
        cancelButtonText: t('common.no'),
        type: 'warning',
      })
        .then(async () => {
          try {
            await postConversationClearContext({
              conversationId,
            })
            // 清空上下文时重置latestChatId
            console.log('latestChatId', latestChatIdValue)

            // 更新外部状态 (需要返回新状态)
            const newChatId = undefined
            const newShowContext = true

            // 显示"新的上下文"提示
            scrollToBottomIfAtBottom()
            ms.success('已清空对话上下文')

            // 返回更新后的状态
            resolve({
              latestChatId: newChatId,
              showNewContext: newShowContext,
            })
          } catch (e: any) {
            // ms.error(t('chat.clearFailed'))
            console.error(e)
            resolve(null)
          }
        })
        .catch(() => {
          resolve(null)
        })
    })
  }

  // 停止响应
  // resolved handleStop方法不再调用controller.abort()，而是调用接口postBizConversationCloseConversation，会在sse数据流中返回[CLOSE]事件
  async function handleStop(): Promise<boolean> {
    if (!streamResponseManager.isLoading(conversationId)) {
      return false
    }

    // 调用接口关闭会话
    // resolved 参数改为id,也就是本次对话的id
    // resolved 如果是onRetry，那么id不一定是最后一条消息的id，需要处理
    // 优先获取当前正在处理的消息信息（适用于重试场景）
    const currentProcessingMessage =
      streamResponseManager.getCurrentProcessingMessage(conversationId)

    if (currentProcessingMessage) {
      const { index, id } = currentProcessingMessage
      console.log('currentProcessingMessage', { index, id })

      if (id) {
        try {
          await postBizConversationCloseConversation({
            id,
          })
        } catch (err) {
          console.error('关闭会话请求失败', err)
        }
      } else {
        // resolved 此处可能是ai回复内容还没有在sse接口返回，所以对话id必然为空，需要处理
        console.log('当前处理消息的ID为空，直接中断当前流式响应')

        // 使用streamResponseManager停止会话
        const stopped = await streamResponseManager.stopConversation(conversationId)

        if (stopped) {
          const chatData = chatStore.getChatByConversationId(conversationId)
          const currentMessage = chatData[index]
          // 更新"思考中..."状态为"会话已停止"
          if (currentMessage && currentMessage.text === t('chat.thinking')) {
            updateChat(conversationId, index, {
              ...currentMessage,
              text: '会话已停止',
              loading: false,
              error: false,
            })
          }
        }
      }
      return true
    }

    // 兜底逻辑：如果无法获取当前处理消息，则使用最后一条消息
    const chatData = chatStore.getChatByConversationId(conversationId)
    if (chatData.length > 0) {
      const lastMessage = chatData[chatData.length - 1]
      console.log('fallback to lastMessage', lastMessage)
      const id = lastMessage.conversationOptions?.id

      if (id) {
        try {
          await postBizConversationCloseConversation({
            id,
          })
        } catch (err) {
          console.error('关闭会话请求失败', err)
        }
      } else {
        console.log('对话ID为空，直接中断当前流式响应')
        const stopped = await streamResponseManager.stopConversation(conversationId)

        if (stopped) {
          if (lastMessage.text === t('chat.thinking')) {
            updateChat(conversationId, chatData.length - 1, {
              ...lastMessage,
              text: '会话已停止',
              loading: false,
              error: false,
            })
          }
        }
      }
      return true
    }
    return false
  }

  // 流式响应处理函数（保持向下兼容）
  async function processStreamResponse(
    response: Response,
    index: number,
    userMessage: string,
    currentModel: string,
    getSummary: (conversationId: string) => Promise<void>,
    latestChatId: Ref<string | undefined>,
  ) {
    return streamResponseManager.coreStreamResponse(
      conversationId,
      response,
      index,
      userMessage,
      getSummary,
      latestChatId,
      scrollToBottomIfAtBottom,
      scrollToBottom,
    )
  }

  // 发起对话
  async function onConversation(
    loading: Ref<boolean>,
    prompt: Ref<string>,
    currentModel: string,
    latestChatId: Ref<string | undefined>,
    showNewContext: Ref<boolean>,
    onStreamResponse: (response: Response, index: number, message: string) => Promise<void>,
    images?: Array<{ url: string; fileName: string; ossId: string }>,
  ) {
    const message = prompt.value

    if (streamResponseManager.isLoading(conversationId)) return

    if (!message || message.trim() === '') return

    if (!currentModel) {
      ms.warning('请先选择模型')
      return
    }

    // 隐藏"新的上下文"提示
    showNewContext.value = false

    // 构建用户消息数据
    const userChatData: Chat.Chat = {
      dateTime: new Date().toLocaleString(),
      text: message,
      inversion: true,
      error: false,
      conversationOptions: {
        conversationId,
        chatId: latestChatId.value,
      },
    }

    // 如果有图片，添加到用户消息中
    if (images && images.length > 0) {
      userChatData.imageInfoList = images.map(img => ({
        imageUrl: img.url,
        ossId: img.ossId,
        fileName: img.fileName,
      }))
    }

    useChat().addChat(conversationId, userChatData)
    scrollToBottomIfAtBottom()

    // 不在这里设置加载状态，让sendRequest方法自己管理
    prompt.value = ''
    useChat().addChat(conversationId, {
      dateTime: new Date().toLocaleString(),
      text: t('chat.thinking'),
      loading: true,
      inversion: false,
      error: false,
      conversationOptions: null,
    })

    // 使用streamResponseManager来处理对话
    try {
      await streamResponseManager.sendRequest(
        conversationId,
        message,
        latestChatId.value,
        onStreamResponse,
        images,
      )
    } catch (error: any) {
      console.error('发送请求失败:', error)
      // 错误处理已在streamResponseManager中处理
    }
  }

  // 重新生成
  async function onRetry(
    index: number,
    loading: Ref<boolean>,
    currentModel: string,
    latestChatId: Ref<string | undefined>,
    onStreamResponse: (response: Response, index: number, message: string) => Promise<void>,
  ) {
    if (streamResponseManager.isLoading(conversationId)) return

    // 使用streamResponseManager来处理重试请求
    try {
      await streamResponseManager.retryRequest(
        conversationId,
        index,
        latestChatId.value,
        onStreamResponse,
      )
    } catch (error: any) {
      console.error('重试请求失败:', error)
      // 错误处理已在streamResponseManager中处理
    }
  }

  // 设置新的controller（保持向下兼容，实际由streamResponseManager管理）
  function setController(newController: AbortController) {
    // 重置streamResponseManager中的controller
    streamResponseManager.resetController(conversationId)
  }

  // 获取当前controller（保持向下兼容）
  const controller = computed(() => {
    const state = streamResponseManager.getState(conversationId)
    return state?.controller ?? new AbortController()
  })

  // 恢复会话状态（用于组件重新挂载时）
  function restoreConversationState() {
    const stateInfo = streamResponseManager.restoreConversationState(conversationId)

    if (stateInfo.isLoading && stateInfo.restoreData) {
      console.log(`恢复会话 ${conversationId} 的流式响应状态`)

      // 恢复UI上的loading状态和已累积的内容
      const { updateChatSome } = useChat()
      const updateData: Partial<Chat.Chat> = {
        loading: true,
      }

      // 如果有累积内容，直接使用（StreamManager已经处理过清理）
      if (stateInfo.restoreData.content) {
        updateData.text = stateInfo.restoreData.content
        console.log(`恢复累积内容: ${stateInfo.restoreData.content.length} 字符`)
      }

      // 如果有推理内容，也要恢复
      if (stateInfo.restoreData.reasoningContent) {
        updateData.reasoningContent = stateInfo.restoreData.reasoningContent
        console.log(`恢复推理内容: ${stateInfo.restoreData.reasoningContent.length} 字符`)
      }

      updateChatSome(conversationId, stateInfo.restoreData.messageIndex, updateData)

      // 使用 nextTick 确保DOM更新后再滚动到底部
      nextTick(() => {
        scrollToBottom && scrollToBottom()
      })
    }

    return stateInfo
  }

  return {
    handleEdit,
    handleDelete,
    handleClear,
    onConversation,
    onRetry,
    processStreamResponse,
    handleStop,
    loading,
    controller,
    setController,
    restoreConversationState,
  }
}
