import type { Ref } from 'vue'
import { nextTick, ref } from 'vue'

type ScrollElement = HTMLDivElement | null

interface ScrollReturn {
  scrollRef: Ref<ScrollElement>
  scrollToBottom: () => Promise<void>
  scrollToTop: () => Promise<void>
  scrollToBottomIfAtBottom: () => Promise<void>
  isUserNearBottom: Ref<boolean>
  maintainScrollPosition: (callback: () => Promise<void>) => Promise<void>
}

export function useScroll(): ScrollReturn {
  const scrollRef = ref<ScrollElement>(null)
  const isUserNearBottom = ref(true) // 默认认为用户在底部

  // 检查是否接近底部
  const checkIfNearBottom = () => {
    if (scrollRef.value) {
      const { scrollTop, scrollHeight, clientHeight } = scrollRef.value
      const threshold = 200 // 距离底部的阈值
      const distanceToBottom = scrollHeight - scrollTop - clientHeight
      isUserNearBottom.value = distanceToBottom <= threshold
    }
  }

  // 添加滚动事件监听器
  const setupScrollListener = () => {
    if (scrollRef.value) {
      scrollRef.value.addEventListener('scroll', checkIfNearBottom)
      // 初始检查
      checkIfNearBottom()
    }
  }

  const scrollToBottom = async () => {
    await nextTick()
    if (scrollRef.value) {
      scrollRef.value.scrollTop = scrollRef.value.scrollHeight
      isUserNearBottom.value = true
    }
  }

  const scrollToTop = async () => {
    await nextTick()
    if (scrollRef.value) {
      scrollRef.value.scrollTop = 0
      isUserNearBottom.value = false
    }
  }

  const scrollToBottomIfAtBottom = async () => {
    await nextTick()
    // console.log('scrollToBottomIfAtBottom', scrollRef.value, isUserNearBottom.value)
    if (scrollRef.value && isUserNearBottom.value) {
      scrollRef.value.scrollTop = scrollRef.value.scrollHeight
    }
  }

  // 维持滚动位置的函数，在加载更多历史记录时使用
  const maintainScrollPosition = async (callback: () => Promise<void>) => {
    if (!scrollRef.value) return await callback()

    // 记录当前滚动元素的位置信息
    const scrollContainer = scrollRef.value
    const beforeHeight = scrollContainer.scrollHeight
    const beforeScrollTop = scrollContainer.scrollTop

    // 执行可能会改变DOM的回调（如加载历史消息）
    await callback()
    await nextTick()

    // 调整滚动位置，确保相对位置不变
    const afterHeight = scrollContainer.scrollHeight
    const heightDifference = afterHeight - beforeHeight

    if (heightDifference > 0) {
      scrollContainer.scrollTop = beforeScrollTop + heightDifference
    }
  }

  // 组件挂载后设置滚动监听
  setTimeout(setupScrollListener, 0)

  // 为scrollRef.value添加maintainScrollPosition方法
  setTimeout(() => {
    if (scrollRef.value) {
      // 使用类型断言解决TypeScript的类型检查问题
      ;(scrollRef.value as any).maintainScrollPosition = maintainScrollPosition
    }
  }, 100)

  return {
    scrollRef,
    scrollToBottom,
    scrollToTop,
    scrollToBottomIfAtBottom,
    isUserNearBottom,
    maintainScrollPosition,
  }
}
