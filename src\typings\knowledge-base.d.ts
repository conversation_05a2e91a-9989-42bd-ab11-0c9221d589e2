declare namespace KnowledgeBase {
  /**
   * KnowledgeBaseVo
   */
  interface KnowledgeBaseVo {
    /**
     * 描述
     */
    description?: string
    /**
     * 编辑时间
     */
    editTime?: Date
    /**
     * 是否启用
     */
    enabled?: boolean
    /**
     * 主键
     */
    id?: string
    icon?: string
    /**
     * 模型id
     */
    modelId?: number
    /**
     * 名称
     */
    name?: string
    /**
     * 权限列表
     */
    permissionList?: KnowledgeBasePermission[]
    /**
     * 分享权限等级 1-仅查看 2-可编辑
     */
    sharePermissionLevel?: string
    /**
     * 分享状态 0-私人 1-全体共享 2-指定部门共享 3-指定成员共享
     */
    shareStatus?: number
    /**
     * 大小
     */
    size?: number
    /**
     * 状态 1-处理中 2-完成
     */
    status?: number
    /**
     * 类型 1-文本 2-图像
     */
    type?: number
    /**
     * 所有者
     */
    userId?: number
    /**
     * 所有者名称
     */
    userName?: string
    /**
     * 模型是否失效
     */
    modelExist?: boolean
  }

  interface KbListParams {
    /**
     * 描述
     */
    description?: string
    firstNum?: number
    /**
     * 主键
     */
    id?: number
    /**
     * 排序的方向desc或者asc
     */
    isAsc?: string
    /**
     * 模型id
     */
    modelId?: number
    /**
     * 名称
     */
    name?: string
    /**
     * 排序列
     */
    orderByColumn?: string
    /**
     * 当前页数
     */
    pageNum?: number
    /**
     * 分页大小
     */
    pageSize?: number
    /**
     * 共享列表 如果是全体共享，则为空
     */
    shareInfoList?: KnowledgeBasePermissionBo[]
    /**
     * 分享状态 0-私人 1-全体共享 2-指定部门共享 3-指定成员共享
     */
    shareStatus?: number
    /**
     * 类型 1-文本 2-图像
     */
    type?: number
  }

  /**
   * KnowledgeBasePermissionBo
   */
  interface KnowledgeBasePermissionBo {
    /**
     * 0-不可见 1-仅查看 2-可编辑
     */
    permissionLevel?: number
    /**
     * 目标id
     */
    targetId: string | number
    /**
     * 权限目标类型 1-部门 2-用户
     */
    targetType: string
  }

  interface AddKbParams {
    /**
     * 描述
     */
    description?: string
    /**
     * 主键
     */
    id?: number
    /**
     * 模型id
     */
    modelId?: number
    /**
     * 名称
     */
    name?: string
    /**
     * 共享列表 如果是全体共享，则为空
     */
    shareInfoList?: KnowledgeBasePermissionBo[]
    /**
     * 分享状态 0-私人 1-全体共享 2-指定部门共享 3-指定成员共享
     */
    shareStatus?: number
    /**
     * 类型 1-文本 2-图像
     */
    type?: number
  }

  /**
   * 知识库内容列表 请求参数
   */
  interface KbItemListParams {
    firstNum?: number
    /**
     * 主键
     */
    id?: string
    /**
     * 排序的方向desc或者asc
     */
    isAsc?: string
    /**
     * 知识库id
     */
    kbId?: string
    /**
     * 重叠token数
     */
    maxOverlapSizeInTokens?: number
    /**
     * token切块最大长度
     */
    maxSegmentSizeInTokens?: number
    /**
     * 名称
     */
    name?: string
    /**
     * 排序列
     */
    orderByColumn?: string
    /**
     * oss对象存储ids
     */
    ossIds?: number[]
    /**
     * 当前页数
     */
    pageNum?: number
    /**
     * 分页大小
     */
    pageSize?: number
  }

  /**
   * KnowledgeBaseItemVo
   */
  interface KnowledgeBaseItemVo {
    /**
     * 分块数量
     */
    blockSize?: number
    /**
     * 是否启用
     */
    enabled: boolean
    /**
     * 错误信息
     */
    errorMsg?: string
    /**
     * 文件后缀名
     */
    fileSuffix?: string
    /**
     * 主键
     */
    id: string
    /**
     * 知识库id
     */
    kbId: number
    /**
     * 名称
     */
    name?: string
    /**
     * OSS对象存储表id
     */
    ossId: string
    /**
     * 大小
     */
    size?: number
    /**
     * 状态 1-处理中 2-完成 3-失败
     */
    status?: number
    /**
     * 类型 1-文本 2-图像
     */
    type?: number
    /**
     * 上传时间
     */
    uploadTime?: Date
  }

  /**
   * KnowledgeBaseItemBo
   */
  interface KnowledgeBaseItemBo {
    /**
     * 主键
     */
    id?: string
    /**
     * 知识库id
     */
    kbId?: string
    /**
     * 重叠token数
     */
    maxOverlapSizeInTokens?: number
    /**
     * token切块最大长度
     */
    maxSegmentSizeInTokens?: number
    /**
     * 名称
     */
    name?: string
    /**
     * oss对象存储ids
     */
    ossIds?: string[]
    segmentSign?: string
    clearUrlAndEmail?: boolean
    replaceSpecialChar?: boolean
    [property: string]: any
  }

  /**
   * previewSlice Params
   */
  interface PreviewSliceParams {
    maxOverlapSizeInTokens: number
    maxSegmentSizeInTokens: number
    ossId: string | number
    segmentSign?: string
    clearUrlAndEmail?: boolean
    replaceSpecialChar?: boolean
  }

  /**
   * SearchTestBo，检索测试业务对象
   */
  interface SearchTestBo {
    kbIdList?: string[]
    kbItemIdList?: string[]
    /**
     * 向量库最大数量
     */
    maxResults?: number
    /**
     * 最低匹配度
     */
    minScore?: number
    query?: string
    /**
     * rerank模型id
     */
    rerankModelId?: number | string
  }

  /**
   * EmbeddingStoreVo
   */
  interface EmbeddingStoreVo {
    embeddingId: string
    metadata?: RAGMetadataDto
    text?: string
  }

  /**
   * RAGMetadataDto
   */
  interface RAGMetadataDto {
    enabled: string
    fileName?: string
    fileType?: string
    fileUrl?: string
    index?: string
    kbId?: string
    kbItemId?: string
  }

  interface KnowledgeBasePermission {
    /**
     * 知识库id
     */
    kbId?: number
    /**
     * 0-不可见 1-仅查看 2-可编辑
     */
    permissionLevel: number
    /**
     * 权限目标 ID
     */
    targetId: number
    /**
     * 权限目标类型 1-部门 2-用户
     */
    targetType: string
  }
  /**
   * KnowledgeBaseBo
   */
  interface KnowledgeBaseBo {
    /**
     * 描述
     */
    description?: string
    /**
     * 主键
     */
    id?: string
    /**
     * 模型id
     */
    modelId?: number
    /**
     * 名称
     */
    name?: string
    /**
     * 共享列表 如果是全体共享，则为空
     */
    shareInfoList?: KnowledgeBasePermissionBo[]
    /**
     * 分享状态 0-私人 1-全体共享 2-指定部门共享 3-指定成员共享
     */
    shareStatus?: number
    /**
     * 类型 1-文本 2-图像
     */
    type?: number
    /**
     * 模型是否失效
     */
    modelExist?: boolean
  }
}
