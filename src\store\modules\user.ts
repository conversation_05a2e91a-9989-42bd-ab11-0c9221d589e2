import { to } from 'await-to-js'
import { ref } from 'vue'
import { defineStore } from 'pinia'
import { useStorage } from '@vueuse/core'
import { getToken, removeToken, setToken } from '@/components/common/LoginDialog/utils/auth'
import type { LoginData } from '@/components/common/LoginDialog/utils/api'
import { getUserInfo, login as loginApi } from '@/components/common/LoginDialog/utils/api'

// import store from '@/store/index'

// 创建用户Store的工厂函数
const createUserStore = (storeId: string, tokenKey?: string) => {
  return defineStore(storeId, () => {
    // 如果提供了tokenKey，使用特定的token存储，否则使用默认的
    const token = ref(tokenKey ? useStorage<string | null>(tokenKey, null).value : getToken())
    const name = ref('')
    const nickname = ref('')
    // resolved userId参考token，也要useStorage
    const userIdKey = tokenKey ? `User-Id-${tokenKey}` : 'User-Id'
    const userIdStorage = useStorage<string | number>(userIdKey, '') // 效果同getToken
    const userId = ref<string | number>(userIdStorage.value)
    const tenantId = ref<string>('')
    const avatar = ref('')
    const roles = ref<Array<string>>([]) // 用户角色编码集合 → 判断路由权限
    const permissions = ref<Array<string>>([]) // 用户权限编码集合 → 判断按钮权限
    const showLogin = ref(false)
    const userInfo = ref<any>({})

    /**
     * 登录
     * @param userInfo
     * @returns
     */
    const login = async (userInfo: LoginData): Promise<void> => {
      const [err, res] = await to(loginApi(userInfo))
      if (res) {
        const data = res.data
        if (tokenKey) {
          // 助手特定的token存储
          const tokenStorage = useStorage<string | null>(tokenKey, null)
          tokenStorage.value = data.access_token
        } else {
          // 默认token存储
          setToken(data.access_token)
        }
        token.value = data.access_token
        return Promise.resolve()
      }
      return Promise.reject(err)
    }

    // 获取用户信息
    const getInfo = async (): Promise<void> => {
      const [err, res] = await to(getUserInfo())
      if (res) {
        const data = res.data
        const user = data.user
        const profile = user.avatar

        if (data.roles && data.roles.length > 0) {
          // 验证返回的roles是否是一个非空数组
          roles.value = data.roles
          permissions.value = data.permissions
        } else {
          roles.value = ['ROLE_DEFAULT']
        }
        name.value = user.userName
        nickname.value = user.nickName
        avatar.value = profile
        userId.value = user.userId
        userIdStorage.value = user.userId
        tenantId.value = user.tenantId
        userInfo.value = user
        return Promise.resolve()
      }
      return Promise.reject(err)
    }

    // 注销
    const logout = async (): Promise<void> => {
      // await logoutApi()
      token.value = ''
      userId.value = ''
      userIdStorage.value = ''
      roles.value = []
      permissions.value = []
      // showLogin.value = true
      if (tokenKey) {
        // 清除助手特定的token
        const tokenStorage = useStorage<string | null>(tokenKey, null)
        tokenStorage.value = null
      } else {
        // 清除默认token
        removeToken()
      }
    }

    const setAvatar = (value: string) => {
      avatar.value = value
    }

    const setShowLogin = (show: boolean) => {
      showLogin.value = show
    }

    // 设置token的方法（用于助手试用场景）
    const setUserToken = (accessToken: string) => {
      if (tokenKey) {
        // 助手特定的token存储
        const tokenStorage = useStorage<string | null>(tokenKey, null)
        tokenStorage.value = accessToken
      } else {
        // 默认token存储
        setToken(accessToken)
      }
      token.value = accessToken
    }

    return {
      userId,
      tenantId,
      token,
      nickname,
      avatar,
      roles,
      permissions,
      showLogin,
      userInfo,
      login,
      getInfo,
      logout,
      setAvatar,
      setShowLogin,
      setUserToken,
    }
  })
}

// 默认的用户Store（原有功能保持不变）
export const useUserStore = createUserStore('user-store')

// Store实例缓存
const storeInstances = new Map<string, ReturnType<typeof createUserStore>>()

// 获取助手特定的用户Store
export const useAgentUserStore = (agentId: string) => {
  const storeId = `user-store-agent-${agentId}`
  const tokenKey = `Agent-Token-${agentId}`

  if (!storeInstances.has(storeId)) {
    const storeDefinition = createUserStore(storeId, tokenKey)
    storeInstances.set(storeId, storeDefinition)
  }

  return storeInstances.get(storeId)!()
}

// 清理助手Store实例（可选，用于内存管理）
export const clearAgentUserStore = (agentId: string) => {
  const storeId = `user-store-agent-${agentId}`
  storeInstances.delete(storeId)

  // 清理对应的存储
  const tokenKey = `Agent-Token-${agentId}`
  const userIdKey = `User-Id-${tokenKey}`
  localStorage.removeItem(tokenKey)
  localStorage.removeItem(userIdKey)
}

export default useUserStore
// 非setup
// export function useUserStoreHook() {
//   return useUserStore(store)
// }
