{"prettier.enable": true, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[less]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue", "html", "json", "jsonc", "json5", "yaml", "yml", "markdown"], "cSpell.words": ["ant<PERSON>", "axios", "bumpp", "chatgpt", "chenz<PERSON>yu", "commitlint", "<PERSON><PERSON><PERSON>", "docker<PERSON>b", "esno", "GPTAPI", "highlightjs", "hljs", "iconify", "katex", "ka<PERSON><PERSON><PERSON><PERSON>", "linkify", "logprobs", "mdhljs", "mila", "nodata", "OPENAI", "pinia", "Popconfirm", "rushstack", "<PERSON><PERSON>", "tailwindcss", "traptitech", "tsup", "Typecheck", "unplugin", "VITE", "vueuse", "<PERSON>"], "i18n-ally.enabledParsers": ["ts"], "i18n-ally.sortKeys": true, "i18n-ally.keepFulfilled": true, "i18n-ally.localesPaths": ["src/locales"], "i18n-ally.keystyle": "nested", "volar.takeOverMode.enabled": true, "volar.completion.preferredTagNameCase": "pascal", "volar.completion.preferredAttrNameCase": "kebab", "volar.inlayHints.eventArgumentInInlineHandlers": false, "typescript.tsdk": "node_modules/typescript/lib", "typescript.enablePromptUseWorkspaceTsdk": true, "editor.suggest.showWords": false, "javascript.suggest.completeFunctionCalls": false, "typescript.suggest.completeFunctionCalls": false, "[dotenv]": {"editor.defaultFormatter": "foxundermoon.shell-format"}}