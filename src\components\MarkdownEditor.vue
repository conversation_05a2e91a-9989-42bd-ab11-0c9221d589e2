<script setup lang="ts">
import { MdEditor } from 'md-editor-v3'
import 'md-editor-v3/lib/style.css'
import { ref, watch } from 'vue'

interface Props {
  modelValue?: string
  disabled?: boolean
  placeholder?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '',
})

const emit = defineEmits(['update:modelValue'])

const text = ref(props.modelValue)

// 监听 props.modelValue 的变化
watch(
  () => props.modelValue,
  newVal => {
    if (newVal !== text.value) {
      text.value = newVal
    }
  },
  { immediate: true },
)

const handleChange = (value: string) => {
  text.value = value
  emit('update:modelValue', value)
}
</script>

<template>
  <div class="markdown-editor">
    <MdEditor
      v-model="text"
      :placeholder="placeholder"
      :disabled="disabled"
      language="zh-CN"
      :toolbars="[]"
      :preview="false"
      :show-code-row-number="false"
      :show-word-limit="false"
      style="box-shadow: none; height: 100%"
      @change="handleChange"
    />
  </div>
</template>

<style lang="less" scoped>
.markdown-editor {
  width: 100%;
  height: 100%;
  :deep(.md-editor) {
    border: none;
    height: 100%;
    &::-webkit-scrollbar {
      width: 5px !important;
    }
    &::-webkit-scrollbar-corner,
    &::-webkit-scrollbar-track {
      // background-color: red !important;
    }

    .cm-scroller {
      &::-webkit-scrollbar {
        width: 5px !important;
        // background-color: red !important;
      }
      ::-webkit-scrollbar-thumb {
        // background-color: red !important;
      }
    }

    .cm-content {
      margin: 0 !important;
    }
    .md-editor-input {
      padding: 16px;
      height: 100%;

      textarea,
      pre,
      code {
        font-family: inherit !important;
      }

      .cm-editor {
        font-family: inherit !important;
        height: 100%;
      }

      .cm-line {
        font-family: inherit !important;
      }
    }

    // 隐藏工具栏
    .md-editor-toolbar {
      display: none;
    }

    // 移除预览区域
    .md-editor-preview {
      display: none;
    }

    // 隐藏字数统计
    .md-editor-footer {
      display: none !important;
    }
  }
}
</style>

<style>
/* 设置 # 号的颜色 */
/*  resolved 为什么这个效果在win电脑上符合预期，在mac电脑上不符合预期 */
/* 使用更通用的选择器组合，增强跨平台兼容性 */
.cm-line .ͼ1i,
.cm-line [class*='ͼz'],
.cm-line [class*='ͼ1'],
.cm-line .cm-header,
.cm-line .cm-hashtag {
  color: #4865e8 !important;
}

/* 确保所有编辑器相关元素使用继承的字体 */
.md-editor-input-wrapper * {
  font-family: inherit !important;
  color: #b6bac1;
}
</style>
