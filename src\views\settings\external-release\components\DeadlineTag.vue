<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps<{
  deadline: string
}>()

const tagInfo = computed(() => {
  const { deadline } = props
  const deadlineDate = new Date(deadline)
  if (!deadline || isNaN(deadlineDate.getTime())) {
    return { color: '#9DA0A6', text: '失效' }
  }

  const now = new Date()
  const timeDiff = deadlineDate.getTime() - now.getTime()
  const daysDiff = Math.ceil(timeDiff / (1000 * 60 * 60 * 24))

  if (daysDiff <= 0) {
    return { color: '#9DA0A6', text: '失效' }
  }
  if (daysDiff >= 365) {
    return { color: '#4C5CEC', text: '长期' }
  }
  if (daysDiff < 3) {
    return { color: '#F25B37', text: `${daysDiff}天` }
  }
  if (daysDiff < 7) {
    return { color: '#FFC411', text: `${daysDiff}天` }
  }
  return { color: '#1EBF60', text: `${daysDiff}天` }
})
</script>

<template>
  <span class="deadline-tag g-family-medium" :style="{ color: tagInfo.color }">
    {{ tagInfo.text }}
  </span>
</template>
<style lang="less" scoped>
.deadline-tag {
  display: inline-block;
  font-size: 12px;
  line-height: 1;
  background-color: transparent;
  padding: 4px 6px;
  border-radius: 4px;
  border: 1.5px solid;
}
</style>
