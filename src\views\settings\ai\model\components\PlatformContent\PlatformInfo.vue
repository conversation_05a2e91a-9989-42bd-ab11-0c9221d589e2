<script setup lang="ts">
import { toggleAiPlatformStatus, updateAiPlatform } from '@/api/model-base/index'
import PlatformLogo from '../PlatformLogo.vue'
// @ts-ignore
import { debounce } from 'lodash'
defineOptions({
  name: 'PlatformContent',
})

const emits = defineEmits(['detection', 'updatePlatformList', 'updatePlatfromInfo'])

const props = defineProps<{
  currentPlatformId?: string | number
  /**
   * 是否存在平台
   */
  hasPlatform?: boolean
  platformLogo?: string
  platformName?: string
  apiKey?: string
  baseUrl?: string
  isEnabled: boolean
  /**
   * 是否是默认平台
   */
  isDefault: boolean | null
  /**
   * 默认平台的地址
   */
  defaultBaseUrl?: string
}>()

/**
 * 保存loading状态提示
 */
function saveLoadingMessage() {
  const { close } = ElMessage({
    message: h('p', { style: 'line-height: 1; font-size: 14px' }, [h('span', null, '保存中...')]),
    plain: true,
    duration: 0,
  })
  return close
}

function filterValue(value: string) {
  if (typeof value === 'string') {
    return value.replace(/[^\x00-\x7F]/g, '').trim()
  } else {
    return value
  }
}

function usePlatformEnable() {
  const isEnabled = ref(false)
  watch(
    () => props.currentPlatformId,
    () => {
      isEnabled.value = props.isEnabled
    },
    {
      immediate: true,
    },
  )

  const switchLoading = ref(false)
  /**
   * 平台状态改变
   */
  function beforeStatusChange(): Promise<boolean> {
    switchLoading.value = true
    return new Promise(resolve => {
      toggleAiPlatformStatus(props.currentPlatformId!)
        .then(res => {
          if (res.code === 200) {
            emits('updatePlatformList')
            resolve(true)
          } else {
            resolve(false)
          }
        })
        .catch(err => {
          console.error(err)
          resolve(false)
        })
        .finally(() => {
          switchLoading.value = false
        })
    })
  }

  return {
    isEnabled,
    switchLoading,
    beforeStatusChange,
  }
}

const { isEnabled, switchLoading, beforeStatusChange } = usePlatformEnable()

function usePlatformApiKey() {
  const apiKeyModel = ref<string>()
  // 记录老的值
  let oldApiKey: string | undefined
  const canEditApiKey = ref(true)
  /**
   * 是否更新 apiKey 的显示
   * 什么进行展示？
   * 	1. 新增的时候，没有 apikey
   *  2. 重新添加的时候
   */
  const isUpdateApiKeyVisible = ref(true)
  watch(
    () => props.currentPlatformId,
    () => {
      oldApiKey = apiKeyModel.value = props.apiKey
      canEditApiKey.value = Boolean(!props.apiKey)
      isUpdateApiKeyVisible.value = Boolean(!props.apiKey)
    },
    {
      immediate: true,
    },
  )

  /**
   * 重新填写 密钥
   */
  function resetApiSecret() {
    apiKeyModel.value = ''
    canEditApiKey.value = true
    isUpdateApiKeyVisible.value = false
  }

  /**
   * 保存API密钥信息
   */
  const savePlatformApiKey = debounce(function () {
    if (!canEditApiKey.value) {
      return
    }
    // apikey

    if (apiKeyModel.value === oldApiKey) {
      return
    }

    const close = saveLoadingMessage()

    updateAiPlatform({
      id: props.currentPlatformId,
      apiKey: apiKeyModel.value,
    })
      .then(res => {
        if (res.code === 200) {
          ElMessage.success('API密钥保存成功')
          oldApiKey = apiKeyModel.value
        } else {
          ElMessage.error('API密钥保存失败')
          apiKeyModel.value = oldApiKey
        }
      })
      .catch(err => {
        console.error(err)
        apiKeyModel.value = oldApiKey
      })
      .finally(() => {
        close()
      })
  }, 600)

  return {
    apiKeyModel,
    canEditApiKey,
    resetApiSecret,
    savePlatformApiKey,
  }
}

const { apiKeyModel, canEditApiKey, resetApiSecret, savePlatformApiKey } = usePlatformApiKey()

function usePlatformApiBaseUrl() {
  const baseUrl = ref<string>()

  watchEffect(() => {
    baseUrl.value = props.baseUrl
  })

  /**
   * 默认平台 重置 url
   */
  function resetBaseUrl() {
    baseUrl.value = props.defaultBaseUrl
    savePlatformApiBaseUrl()
  }

  /**
   * 保存API地址信息
   * 如果和原来内容一样，不提交
   */
  const savePlatformApiBaseUrl = debounce(function () {
    if (baseUrl.value === props.baseUrl) {
      return
    }
    const close = saveLoadingMessage()

    updateAiPlatform({
      id: props.currentPlatformId,
      baseUrl: baseUrl.value,
    })
      .then(res => {
        if (res.code === 200) {
          ElMessage.success('API地址保存成功')
          emits('updatePlatfromInfo')
        } else {
          ElMessage.error('API地址保存失败')
        }
      })
      .catch(err => {
        console.error(err)
      })
      .finally(() => {
        close()
      })
  }, 600)

  return {
    baseUrl,
    resetBaseUrl,
    savePlatformApiBaseUrl,
  }
}

const { baseUrl, resetBaseUrl, savePlatformApiBaseUrl } = usePlatformApiBaseUrl()

/**
 * 检测模型
 */
function checkModel() {
  if (!apiKeyModel.value) {
    ElMessage.error('请填写API密钥')
    return
  }
  if (!baseUrl.value) {
    ElMessage.error('请填写API地址')
    return
  }
  emits('detection')
}
</script>

<template>
  <div class="flex justify-between align-middle mb-[35px]">
    <div class="flex-1 w-10 pr-5 truncate">
      <PlatformLogo :platformName="platformName" :logo-src="platformLogo" :size="56" />
      <span class="mx-4 text-[18px] g-family-medium">{{ platformName }}</span>
      <!-- <img
          class="size-[24px] inline-block cursor-pointer"
          src="@/assets/settings/home/<USER>"
          alt=""
          @click="emits('editPlatform')"
        /> -->
    </div>
    <div>
      <span class="mr-3 g-family-medium">启用</span>
      <el-switch v-model="isEnabled" :loading="switchLoading" :before-change="beforeStatusChange" />
    </div>
  </div>
  <div class="mb-6">
    <div class="text-[16px] mb-3 g-family-medium">API密钥</div>
    <el-input
      class="api-item"
      v-model.trim="apiKeyModel"
      :readonly="!canEditApiKey"
      maxlength="200"
      :formatter="filterValue"
      @change="savePlatformApiKey"
      @blur="savePlatformApiKey"
    >
      <template #suffix>
        <span v-if="!canEditApiKey" class="cursor-pointer text-blue-500" @click="resetApiSecret">
          重新填写
        </span>
      </template>
      <template #append>
        <span class="cursor-pointer" @click="checkModel">检测</span>
      </template>
    </el-input>
  </div>
  <div class="mb-6">
    <div class="text-[16px] mb-3 g-family-medium">API地址</div>
    <el-input
      class="api-item"
      v-model="baseUrl"
      maxlength="200"
      :formatter="filterValue"
      @change="savePlatformApiBaseUrl"
      @blur="savePlatformApiBaseUrl"
    >
      <template v-if="isDefault" #append>
        <span class="cursor-pointer" @click="resetBaseUrl">重置</span>
      </template>
    </el-input>
  </div>
</template>
<style scoped lang="less">
.api-item {
  background-color: #fff;

  :deep(.el-input-group__append) {
    background-color: #fff;
  }
}
</style>
