import type { App } from 'vue'
import type { RouteRecordRaw } from 'vue-router'
import { createRouter, createWebHashHistory } from 'vue-router'
import { setupPageGuard } from './permission'
import { Layout } from '@/layout'
import ChatLayout from '@/views/chat/layout/Layout.vue'
import { useAppStore, useChatStore } from '@/store'
import { useBasicLayout } from '@/hooks/useBasicLayout'

// 动态获取移动端模式的函数
const getIsMobileMode = () => {
  try {
    const { isMobile } = useBasicLayout()
    return isMobile.value
  } catch {
    // 如果 hook 还没有初始化，使用默认值
    return false
  }
}

const baseRoutes: RouteRecordRaw[] = [
  {
    path: '/chat',
    name: 'chat',
    component: ChatLayout,
    meta: {
      title: '聊天',
    },
    redirect: to => {
      const chatStore = useChatStore()
      const history = chatStore.history
      if (history.length > 0) {
        return `/chat/${history[0].conversationId}`
      }
      return '/chat'
    },
    children: [
      {
        path: '/chat/public/:conversationId?',
        name: 'public-chat',
        component: () => import('@/views/chat/index.vue'),
        meta: {
          title: '公开聊天',
        },
      },
      {
        path: '/chat/:conversationId?',
        name: 'Chat',
        component: () => import('@/views/chat/index.vue'),
        meta: {
          title: '聊天对话',
        },
      },
    ],
  },
]

const fullRoutes: RouteRecordRaw[] = [
  {
    path: '/knowledge-base',
    name: 'knowledge-base',
    meta: {
      title: '知识库',
    },
    redirect: '/knowledge-base/my-knowledge-base',
    component: () => import('@/views/knowledge-base/layout.vue'),
    children: [
      {
        path: 'my-knowledge-base',
        name: 'my-knowledge-base',
        meta: {
          title: '我的知识库',
        },
        component: () => import('@/views/knowledge-base/my-knowledge-base/index.vue'),
      },
      {
        path: 'my-knowledge/:kbId',
        name: 'knowledge-base-layout',
        meta: { title: '', dynamicTitle: true },
        component: () => import('@/views/knowledge-base/common/layout.vue'),
        children: [
          {
            path: 'dataset',
            name: 'dataset',
            component: () => import('@/views/knowledge-base/common/dataset.vue'),
            meta: { hideBreadcrumb: true },
          },
          {
            path: 'searchTest',
            name: 'searchTest',
            component: () => import('@/views/knowledge-base/common/search-test.vue'),
            meta: { hideBreadcrumb: true },
          },
          {
            path: 'config',
            name: 'config',
            component: () => import('@/views/knowledge-base/my-knowledge-base/config.vue'),
            meta: { hideBreadcrumb: true },
          },
          {
            path: 'filePreview/:ossId',
            name: 'filePreview',
            component: () => import('@/views/knowledge-base/common/file-preview/index.vue'),
            meta: { hideBreadcrumb: true },
          },
        ],
      },
      {
        path: 'shared-knowledge-base',
        name: 'shared-knowledge-base',
        meta: {
          title: '共享知识库',
        },
        component: () => import('@/views/knowledge-base/shared-knowledge-base/index.vue'),
      },
      {
        path: 'share-knowledge/:kbId',
        name: 'share-knowledge-base-layout',
        meta: { title: '', dynamicTitle: true },
        component: () => import('@/views/knowledge-base/common/layout.vue'),
        children: [
          {
            path: 'share-dataset',
            name: 'share-dataset',
            component: () => import('@/views/knowledge-base/common/dataset.vue'),
            meta: { hideBreadcrumb: true },
          },
          {
            path: 'share-searchTest',
            name: 'share-searchTest',
            component: () => import('@/views/knowledge-base/common/search-test.vue'),
            meta: { hideBreadcrumb: true },
          },
        ],
      },
    ],
  },
  {
    path: '/agent',
    name: 'agent',
    meta: {
      title: '助手',
    },
    redirect: '/agent/my-agent',
    component: () => import('@/views/agent/layout.vue'),
    children: [
      {
        path: 'my-agent',
        name: 'my-agent',
        meta: {
          title: '我的助手',
        },
        component: () => import('@/views/agent/my-agent/index.vue'),
      },
      {
        path: 'agent-operation',
        name: 'agent-operation',
        meta: {
          title: '新增助手',
        },
        component: () => import('@/views/agent/my-agent/operation.vue'),
      },
      {
        path: 'agent-store',
        name: 'agent-store',
        meta: {
          title: '企业助手',
        },
        component: () => import('@/views/agent/agent-store.vue'),
      },
    ],
  },
  {
    path: '/settings',
    name: 'settings',
    meta: {
      title: '设置',
    },
    redirect: '/settings/settings-home',
    component: () => import('@/views/settings/layout.vue'),
    children: [
      {
        path: 'settings-home',
        name: 'settings-home',
        meta: {
          title: '首页',
        },
        component: () => import('@/views/settings/home/<USER>'),
      },
      {
        path: 'user',
        name: 'user',
        meta: {
          title: '部门成员',
        },
        component: () => import('@/views/settings/user/index.vue'),
      },
      {
        path: 'external-release',
        name: 'external-release',
        meta: {
          title: '对外发布',
        },
        component: () => import('@/views/settings/external-release/index.vue'),
      },
      {
        path: 'ai',
        name: 'ai',
        meta: {
          title: 'AI管理',
        },
        redirect: '/ai/model',
        children: [
          {
            path: 'model',
            name: 'ai-model',
            meta: {
              title: '模型库',
            },
            component: () => import('@/views/settings/ai/model/index.vue'),
          },
          {
            path: 'agent',
            name: 'ai-agent',
            meta: {
              title: '助手库',
            },
            component: () => import('@/views/settings/ai/agent/index.vue'),
          },
          {
            path: 'agent-operation',
            name: 'ai-agent-operation',
            meta: {
              title: '审核助手',
            },
            component: () => import('@/views/settings/ai/agent/operation-box.vue'),
          },
          {
            path: 'knowledge',
            name: 'ai-knowledge',
            meta: {
              title: '知识库',
            },
            component: () => import('@/views/settings/ai/knowledge/index.vue'),
          },
          {
            path: 'settings-knowledge/:kbId',
            name: 'settings-knowledge-base-layout',
            meta: { title: '', dynamicTitle: true },
            component: () => import('@/views/knowledge-base/common/layout.vue'),
            children: [
              {
                path: 'settings-dataset',
                name: 'settings-dataset',
                component: () => import('@/views/knowledge-base/common/dataset.vue'),
                meta: { hideBreadcrumb: true },
              },
              {
                path: 'settings-searchTest',
                name: 'settings-searchTest',
                component: () => import('@/views/knowledge-base/common/search-test.vue'),
                meta: { hideBreadcrumb: true },
              },
              {
                path: 'settings-config',
                name: 'settings-config',
                component: () => import('@/views/knowledge-base/my-knowledge-base/config.vue'),
                meta: { hideBreadcrumb: true },
              },
              {
                path: 'settings-filePreview/:ossId',
                name: 'settings-filePreview',
                component: () => import('@/views/knowledge-base/common/file-preview/index.vue'),
                meta: { hideBreadcrumb: true },
              },
            ],
          },
        ],
      },
    ],
  },
  {
    path: '/dev',
    name: 'dev',
    meta: {
      title: '开发者工具',
    },
    component: () => import('@/views/dev/index.vue'),
  },
]

const routes: RouteRecordRaw[] = [
  {
    path: '/invite',
    name: 'invite',
    component: () => import('@/views/login/index.vue'),
    meta: {
      title: '邀请注册',
    },
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/login/index.vue'),
    meta: {
      title: '用户登录',
    },
  },
  {
    path: '/terms',
    name: 'terms',
    component: () => import('@/views/terms/index.vue'),
    meta: {
      title: '用户协议与隐私政策',
    },
  },
  {
    path: '/share/:agentId',
    name: 'public-agent',
    component: () => import('@/views/agent/public.vue'),
    meta: {
      title: '助手试用',
    },
  },
  {
    path: '/',
    name: 'Root',
    meta: {
      title: '首页',
      hideBreadcrumb: true,
    },
    component: Layout,
    redirect: '/chat',
    children: getIsMobileMode() ? baseRoutes : [...baseRoutes, ...fullRoutes],
  },
  {
    path: '/404',
    name: '404',
    meta: {
      title: '404 未找到',
    },
    component: () => import('@/views/exception/404/index.vue'),
  },
  {
    path: '/500',
    name: '500',
    meta: {
      title: '500 服务器错误',
    },
    component: () => import('@/views/exception/500/index.vue'),
  },
]

export const router = createRouter({
  history: createWebHashHistory(),
  routes,
  scrollBehavior: () => ({ left: 0, top: 0 }),
})

setupPageGuard(router)

export async function setupRouter(app: App) {
  app.use(router)
  await router.isReady()
}

router.onError(error => {
  // 检测是否为动态导入失败（通常意味着应用版本已更新）
  const isDynamicImportError = error.message.includes('Failed to fetch dynamically imported module')

  if (isDynamicImportError) {
    // 动态导入失败时不打印错误，也不进行重定向
    // 让 BuildTimeChecker 处理版本更新提醒
    return
  }

  console.error('路由错误:', error)
  if (error.message.includes('No match')) {
    router.push('/chat')
  } else {
    router.push('/500')
  }
})
