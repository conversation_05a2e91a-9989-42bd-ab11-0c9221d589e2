declare namespace System {
  /**
   * ProfileVo，用户个人信息
   */
  interface ProfileVo {
    /**
     * 用户所属岗位组
     */
    postGroup?: string
    /**
     * 用户所属角色组
     */
    roleGroup?: string
    user?: SysUserVo
    [property: string]: any
  }

  /**
   * SysUserVo，用户信息视图对象 sys_user
   */
  interface SysUserVo {
    /**
     * 头像地址
     */
    avatar?: number
    /**
     * 创建时间
     */
    createTime?: Date
    /**
     * 部门ID
     */
    deptId?: number
    /**
     * 部门名
     */
    deptName?: string
    /**
     * 用户邮箱
     */
    email?: string
    /**
     * 加入时间(激活时间)
     */
    joinDate?: Date
    /**
     * 最后登录时间
     */
    loginDate?: Date
    /**
     * 最后登录IP
     */
    loginIp?: string
    /**
     * 用户昵称
     */
    nickName?: string
    /**
     * 手机号码
     */
    phonenumber?: string
    /**
     * 岗位组
     */
    postIds?: number[]
    /**
     * 备注
     */
    remark?: string
    /**
     * 数据权限 当前角色ID
     */
    roleId?: number
    /**
     * 角色组
     */
    roleIds?: number[]
    /**
     * 角色对象
     */
    roles?: SysRoleVo[]
    /**
     * 用户性别（0男 1女 2未知）
     */
    sex?: string
    /**
     * 帐号状态（0正常 1停用）
     */
    status?: string
    /**
     * 租户ID
     */
    tenantId?: string
    /**
     * 用户ID
     */
    userId?: number
    /**
     * 用户账号
     */
    userName?: string
    /**
     * 用户类型（sys_user系统用户）
     */
    userType?: string
    [property: string]: any
  }

  /**
   * SysRoleVo，角色信息视图对象 sys_role
   */
  interface SysRoleVo {
    /**
     * 创建时间
     */
    createTime?: Date
    /**
     * 数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）
     */
    dataScope?: string
    /**
     * 部门树选择项是否关联显示
     */
    deptCheckStrictly?: boolean
    /**
     * 用户是否存在此角色标识 默认不存在
     */
    flag?: boolean
    /**
     * 菜单树选择项是否关联显示
     */
    menuCheckStrictly?: boolean
    /**
     * 备注
     */
    remark?: string
    /**
     * 角色ID
     */
    roleId?: number
    /**
     * 角色权限字符串
     */
    roleKey?: string
    /**
     * 角色名称
     */
    roleName?: string
    /**
     * 显示顺序
     */
    roleSort?: number
    /**
     * 角色状态（0正常 1停用）
     */
    status?: string
    superAdmin?: boolean
    [property: string]: any
  }

  /**
   * SysUserProfileBo，个人信息业务处理
   */
  interface ProfileBo {
    /**
     * 创建者
     */
    createBy?: number
    /**
     * 创建部门
     */
    createDept?: number
    /**
     * 创建时间
     */
    createTime?: Date
    /**
     * 用户邮箱
     */
    email?: string
    /**
     * 用户昵称
     */
    nickName?: string
    /**
     * 请求参数
     */
    params?: { [key: string]: { [key: string]: any } }
    /**
     * 手机号码
     */
    phonenumber?: string
    /**
     * 用户性别（0男 1女 2未知）
     */
    sex?: string
    /**
     * 更新者
     */
    updateBy?: number
    /**
     * 更新时间
     */
    updateTime?: Date
    [property: string]: any
  }

  /**
   * RInvitationCodeVO，响应信息主体
   */
  export interface Response {
    code?: number
    data?: InvitationCodeVO
    msg?: string
    [property: string]: any
  }

  /**
   * InvitationCodeVO，Author: miracle
   * Date: 2025/5/12 13:37
   */
  interface InvitationCodeVO {
    /**
     * 邀请码
     */
    code?: string
    /**
     * 有效时长（天）
     */
    day?: number
    /**
     * 结束时间
     */
    endDay?: Date
    /**
     * ID
     */
    id?: number
    /**
     * 分享人
     */
    shareUserId?: number
    shareUserName?: string
    /**
     * 开始时间
     */
    startDay?: Date
    /**
     * 所属团队
     */
    tenantName?: string
    /**
     * 地址
     */
    url?: string
    [property: string]: any
  }

  /**
   * SysUserInfoVo，用户信息
   */
  interface SysUserInfoVo {
    /**
     * 岗位ID列表
     */
    postIds?: number[]
    /**
     * 岗位列表
     */
    posts?: SysPostVo[]
    /**
     * 角色ID列表
     */
    roleIds?: number[]
    /**
     * 角色列表
     */
    roles?: SysRoleVo[]
    user?: SysUserVo
    [property: string]: any
  }

  /**
   * SysPostVo，岗位信息视图对象 sys_post
   */
  interface SysPostVo {
    /**
     * 创建时间
     */
    createTime?: Date
    /**
     * 部门id
     */
    deptId?: number
    /**
     * 部门名
     */
    deptName?: string
    /**
     * 岗位类别编码
     */
    postCategory?: string
    /**
     * 岗位编码
     */
    postCode?: string
    /**
     * 岗位ID
     */
    postId?: number
    /**
     * 岗位名称
     */
    postName?: string
    /**
     * 显示顺序
     */
    postSort?: number
    /**
     * 备注
     */
    remark?: string
    /**
     * 状态（0正常 1停用）
     */
    status?: string
    [property: string]: any
  }
}
