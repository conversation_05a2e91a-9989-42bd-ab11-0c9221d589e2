<script setup lang="ts">
import type { CSSProperties } from 'vue'
import { computed, onMounted, ref, watch } from 'vue'
import { NButton, NInput, NLayoutSider } from 'naive-ui'
import { ElMessageBox } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { throttle } from 'lodash'
import List from './List.vue'
import ms from '@/utils/message'
import { useAppStore, useChatStore, useSettingStore, useUserStore } from '@/store'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { PromptStore, SvgIcon } from '@/components/common'
import { t } from '@/locales'
import {
  createGuestOutwardApply,
  getBizConversationGetDefaultConversation,
  postBizConversationCreateConversation,
} from '@/api'
import { getBizAgentDetailId } from '@/api/agent'
import { usePublicChat } from '../../hooks/usePublicChat'
const route = useRoute()
const { isPublicChat } = usePublicChat()

const router = useRouter()
const appStore = useAppStore()
const chatStore = useChatStore()
const settingStore = useSettingStore()
const userStore = useUserStore()

const searchValue = ref('')
const { isMobile } = useBasicLayout()
const show = ref(false)

const collapsed = computed(() => appStore.siderCollapsed)

// 判断当前是否是随便聊聊状态
const isCasualChatActive = computed(() => {
  const currentChat = chatStore.history.find(item => item.conversationId === chatStore.active)
  return currentChat?.isCasualChat ?? false
})

async function handleCasualChat() {
  try {
    // 先检查是否已经存在随便聊聊的对话
    const existingCasualChat = chatStore.history.find(item => item.isCasualChat)
    if (existingCasualChat && existingCasualChat.conversationId) {
      // 如果存在，直接切换到这个对话
      await chatStore.setActive(existingCasualChat.conversationId)
      router.push({ name: 'Chat', params: { conversationId: existingCasualChat.conversationId } })
      if (isMobile.value) appStore.setSiderCollapsed(true)
      return
    }

    // 如果不存在，创建新的随便聊聊对话
    const { data } = await getBizConversationGetDefaultConversation<Chat.ConversationVo>()
    if (data && data.id) {
      // 添加历史记录，标记为随便聊聊
      chatStore.addHistory({
        title: '随便聊聊',
        conversationId: data.id,
        isCasualChat: true,
      })

      // 如果有系统消息，则添加到对话中
      // systemMessage不是角色设定吗？现在看来系统消息内容放在text字段对吗？
      if (data.systemMessage) {
        chatStore.addChatByConversationId(data.id, {
          dateTime: new Date().toLocaleString(),
          text: data.systemMessage,
          inversion: false,
          error: false,
          loading: false,
          conversationOptions: null,
        })
      }

      // 导航到对话页面
      router.push({ name: 'Chat', params: { conversationId: data.id } })
    }

    if (isMobile.value) appStore.setSiderCollapsed(true)
  } catch (error) {
    console.error('获取默认对话失败:', error)
    ms.error(t('common.error'))
  }
}

function handleAddBaseAgent() {
  router.push('/agent')
}

async function handleAddBaseAgentPublic() {
  // resolved 参考本页面的handleAdd和src\views\agent\public.vue的handleStartTrial，另外，releaseId从route.query.releaseId
  const releaseId = route.query.releaseId as string

  try {
    const { data } = await createGuestOutwardApply(releaseId)

    if (data?.id) {
      // 添加历史记录
      chatStore.addHistory({
        title: data.agentName || data.name,
        conversationId: data.id,
      })

      if (isMobile.value) appStore.setSiderCollapsed(true)
    } else {
      ms.error('创建对话失败，请稍后重试')
    }
  } catch (error) {
    console.error('创建对话失败:', error)
  }
}

// resolved: 对创建对话的函数进行节流处理，防止用户快速点击创建多个
// resolved: 创建对话时候调用getBizAiModelDefault获取默认对话模型并设置
const handleAdd = throttle(
  async () => {
    try {
      let systemMessage = ''
      if (chatStore.useSystemMessage) {
        systemMessage = await fetch('/2443F399-4F32-4c38-B236-A2DEF3C0E8E3.txt')
          .then(res => res.blob())
          .then(
            blob =>
              new Promise((resolve, reject) => {
                const reader = new FileReader()
                reader.onload = () => resolve(reader.result as string)
                reader.onerror = reject
                reader.readAsText(blob, 'utf-8')
              }),
          )
      }

      // 从store中获取默认对话模型
      // resolved 使用解构赋值直接获取chatModelId
      const { chatModelId: modelId } = (await settingStore.fetchDefaultModel()) || {}

      const { data } = await postBizConversationCreateConversation<Chat.ConversationVo>({
        // name: '来自前端的name',
        systemMessage,
        modelId, // 使用获取到的默认模型ID
      })

      if (data?.id) {
        // 添加新的历史记录
        const conversationId = data.id
        chatStore.addHistory({ title: t('chat.newChatTitle'), conversationId })

        // 如果有系统消息，则添加到对话中
        // systemMessage不是角色设定吗？现在看来系统消息内容放在text字段对吗？
        if (data.systemMessage) {
          chatStore.addChatByConversationId(conversationId, {
            dateTime: new Date().toLocaleString(),
            text: data.systemMessage,
            inversion: false,
            error: false,
            loading: false,
            conversationOptions: null,
          })
        }
      }

      if (isMobile.value) appStore.setSiderCollapsed(true)
    } catch (error) {
      console.error('创建对话失败:', error)
      ms.error(t('创建对话失败'))
      // 即使接口失败，也要创建新对话
      // const conversationId = Date.now()
      // chatStore.addHistory({ title: t('chat.newChatTitle'), conversationId })
      if (isMobile.value) appStore.setSiderCollapsed(true)
    }
  },
  800,
  { trailing: false },
)

function handleUpdateCollapsed() {
  appStore.setSiderCollapsed(!collapsed.value)
}

function handleLogout() {
  // resolved 用elementplus
  ElMessageBox.confirm(t('common.logoutConfirm'), t('common.logout'), {
    confirmButtonText: t('common.yes'),
    cancelButtonText: t('common.no'),
    type: 'warning',
  })
    .then(async () => {
      try {
        await userStore.logout()
        ms.success('退出成功')
        router.push('/login')
      } catch (error) {
        console.error('退出登录失败:', error)
        ms.error('退出失败')
      }
    })
    .catch(() => {
      // 用户取消退出，不做任何操作
    })
}

const getMobileClass = computed<CSSProperties>(() => {
  if (isMobile.value) {
    return {
      position: 'fixed',
      zIndex: 50,
    }
  }
  return {}
})

const floatingButtonStyle = computed<CSSProperties>(() => ({
  position: 'fixed',
  bottom: '20px',
  right: collapsed.value ? '20px' : '300px',
  zIndex: 99,
  transition: 'right 0.3s',
}))

const mobileSafeArea = computed(() => {
  if (isMobile.value) {
    return {
      paddingBottom: 'env(safe-area-inset-bottom)',
    }
  }
  return {}
})

watch(
  isMobile,
  val => {
    appStore.setSiderCollapsed(val)
  },
  {
    immediate: true,
    flush: 'post',
  },
)

onMounted(async () => {
  if (!isPublicChat) {
    settingStore.fetchDefaultModel(true)
  }
})
</script>

<template>
  <NLayoutSider
    :collapsed="collapsed"
    :collapsed-width="0"
    :width="360"
    :show-trigger="isMobile ? false : 'arrow-circle'"
    collapse-mode="transform"
    position="absolute"
    bordered
    :style="getMobileClass"
    @update-collapsed="handleUpdateCollapsed"
  >
    <div class="flex flex-col h-full" :style="mobileSafeArea">
      <main class="flex flex-col flex-1 min-h-0">
        <div class="p-4">
          <div class="mb-2">
            <NInput
              v-model:value="searchValue"
              type="text"
              placeholder="Search"
              :style="{ background: '#F4F5FA', borderRadius: '8px' }"
            >
              <template #suffix>
                <SvgIcon icon="ri:search-line" />
              </template>
            </NInput>
          </div>
          <div
            v-if="!isPublicChat"
            class="casual-chat-btn mt-4 relative flex items-center gap-3 px-3 py-2 break-all rounded-md cursor-pointer hover:bg-neutral-100 group dark:border-neutral-800 dark:hover:bg-[#24272e]"
            :class="isCasualChatActive ? 'bg-isActive' : ''"
            @click="handleCasualChat"
          >
            <img
              src="@/assets/DEFAULT_ROBOT_AVATAR.png"
              class="w-[48px] h-[48px] rounded-full"
              alt="robot"
            />
            <span class="text-base">随便聊聊</span>
          </div>
        </div>
        <div class="flex-1 min-h-0 pb-4 overflow-hidden">
          <List :search-value="searchValue" @create-conversation="handleAdd" />
        </div>
      </main>
      <footer class="p-4 border-t">
        <div class="flex items-center justify-between">
          <template v-if="!isPublicChat">
            <!-- resolved isMobile时候此按钮改为退出登录按钮 -->
            <NButton v-if="isMobile" text class="btn-create" @click="handleLogout">
              <template #icon>
                <i class="iconfont iconfont-c icon-tuichu"></i>
              </template>
              退出登录
            </NButton>
            <NButton v-else text class="btn-create" @click="handleAddBaseAgent">
              <template #icon>
                <i class="iconfont iconfont-c icon-xinzeng"></i>
              </template>
              从助手创建
            </NButton>
            <NButton text class="btn-create" @click="handleAdd">
              <template #icon>
                <i class="iconfont iconfont-c icon-chuangjianduihua"></i>
              </template>
              创建对话
            </NButton>
          </template>
          <template v-else>
            <NButton text class="btn-create !w-full" @click="handleAddBaseAgentPublic">
              <template #icon>
                <i class="iconfont iconfont-c icon-chuangjianduihua"></i>
              </template>
              创建对话
            </NButton>
          </template>
        </div>
      </footer>
    </div>
  </NLayoutSider>
  <template v-if="isMobile">
    <div
      v-show="!collapsed"
      class="fixed inset-0 z-40 w-full h-full bg-black/40"
      @click="handleUpdateCollapsed"
    />
  </template>
  <PromptStore v-model:visible="show" />
</template>

<style scoped>
.casual-chat-btn {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.bg-isActive {
  background: rgba(72, 101, 232, 0.1) !important;
}

.btn-create {
  width: 156px;
  height: 40px;
  border-radius: 8px;
  border: 1px solid #dadde8;
}
</style>
