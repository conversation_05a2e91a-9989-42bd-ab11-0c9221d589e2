<script lang="ts" setup>
import { reactive, ref } from 'vue'

const props = defineProps({
  avatar: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update:modelValue', 'save'])

const dialogVisible = ref(false)
const form = reactive({
  username: '',
  phone: '',
  email: '',
})

const handleSave = () => {
  emit('save', form)
  dialogVisible.value = false
}

defineExpose({
  dialogVisible,
})
</script>

<template>
  <el-dialog
    draggable
    v-model="dialogVisible"
    title="用户信息"
    width="30%"
    :close-on-click-modal="false"
  >
    <div class="flex flex-col items-center">
      <el-avatar :size="64" :src="props.avatar" />
      <div class="mt-4">
        <el-form :model="form" label-width="80px">
          <el-form-item label="用户名称">
            <el-input v-model="form.username" />
          </el-form-item>
          <el-form-item label="绑定手机">
            <el-input v-model="form.phone" />
          </el-form-item>
          <el-form-item label="邮箱">
            <el-input v-model="form.email" />
          </el-form-item>
        </el-form>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSave"> 保存修改 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
