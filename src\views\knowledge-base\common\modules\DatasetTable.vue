<script setup lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, Sort } from 'element-plus'
import { computed, defineEmits, defineOptions, defineProps, inject, ref } from 'vue'
import { Search } from '@element-plus/icons-vue'
import Pagination from '@/components/common/Pagination/index.vue'
import EnableSwitch from '@/components/common/EnableSwitch.vue'
import '@/typings/knowledge-base.d.ts'
import {
  getBizKbItemList,
  postBizKbItemEnable,
  postBizKbItemRemove,
  postBizKbItemRetry,
} from '@/api/knowledge-base'
import FileTypePic from '@/components/common/FileTypePic.vue'
import { useKnowledgeBaseEditPermission } from '@/hooks/useKnowledgeBaseEditPermission'

const props = defineProps<{
  kbId: string
}>()

const emits = defineEmits(['changeMode'])

defineOptions({
  name: 'Dataset',
})

const loading = ref(false)
const tableData = ref<KnowledgeBase.KnowledgeBaseItemVo[]>([])

const dialogVisible = ref(false)
const dialogTitle = ref('新增知识')
const queryFormRef = ref<FormInstance>()
const total = ref(0)

// 查询参数

const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  name: '',
  orderByColumn: '',
  isAsc: 'asc',
})

const getKnowledgeItemList = async () => {
  loading.value = true
  try {
    const res = await getBizKbItemList<{ rows: KnowledgeBase.KnowledgeBaseItemVo[] }>({
      ...queryParams.value,
      kbId: props.kbId,
    })
    if (res.code === 200) {
      tableData.value = res.rows
      total.value = res.total
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error: any) {
    ElMessage.error(error.message || error)
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1
  getKnowledgeItemList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  queryParams.value.pageNum = 1
  handleQuery()
}

const handleAdd = () => {
  dialogTitle.value = '新增文件'
  dialogVisible.value = true
}

const handleDelete = (id: number) => {
  ElMessageBox.confirm('确认删除该文件吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    postBizKbItemRemove(id)
      .then(res => {
        if (res.code === 200) {
          ElMessage.success('删除成功')
          getKnowledgeItemList()
        } else {
          ElMessage.error(`删除失败: ${res.msg}`)
        }
      })
      .catch((error: any) => {
        ElMessage.error(`删除失败: ${error.message}`)
      })
  })
}

getKnowledgeItemList()

function sortChange({ prop, order }: Sort) {
  let isAsc = ''
  if (order === 'ascending') {
    isAsc = 'asc'
  } else if (order === 'descending') {
    isAsc = 'desc'
  }

  queryParams.value.pageNum = 1
  queryParams.value.orderByColumn = prop
  queryParams.value.isAsc = isAsc
  getKnowledgeItemList()
}

const retryLoading = ref(false)
/**
 * 重试
 */
function handleRetry(row: KnowledgeBase.KnowledgeBaseItemVo) {
  if (retryLoading.value) return
  retryLoading.value = true
  postBizKbItemRetry({
    idList: [row.id],
  })
    .then(res => {
      if (res.code === 200) {
        getKnowledgeItemList()
      }
    })
    .finally(() => {
      retryLoading.value = false
    })
}

const multipleSelection = ref<KnowledgeBase.KnowledgeBaseItemVo[]>([])
function handleSelectionChange(val: KnowledgeBase.KnowledgeBaseItemVo[]) {
  console.log(val)
  multipleSelection.value = val
}

function addFile() {
  emits('changeMode', 'add')
}

function handleFile(row: KnowledgeBase.KnowledgeBaseItemVo) {
  // 当前文件的处理状态是完成状态时，才允许查看详情，如果不是完成状态，弹出提示
  if (row.status !== 2) {
    ElMessage.warning('当前文件未解析完成，暂时无法查看详情！')
    return
  }
  emits('changeMode', 'detail', row)
}

const handleEnable = (id: string, enabled: boolean) => {
  return postBizKbItemEnable({ kbItemIdList: [id], enabled })
}

/**
 * 启动/禁用
 */
function handleEnableBatch(enabled: boolean) {
  ElMessageBox.confirm(`确认${enabled ? '启用' : '禁用'}选中的数据吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    postBizKbItemEnable({
      kbItemIdList: multipleSelection.value.map(item => item.id),
      enabled,
    }).then(res => {
      if (res.code === 200) {
        ElMessage.success(`${enabled ? '启用' : '禁用'}成功`)
        getKnowledgeItemList()
      } else {
        ElMessage.error(`${enabled ? '启用' : '禁用'}失败: ${res.msg}`)
      }
    })
  })
}

const canEditHere = useKnowledgeBaseEditPermission(props.kbId)
</script>

<template>
  <div v-if="canEditHere" class="flex justify-between">
    <div>
      <el-button class="ex-el-button-gray" type="primary" plain @click="addFile">
        <i class="iconfont iconfont-c icon-xinzeng mr-2"></i>
        新增文件
      </el-button>
      <el-dropdown trigger="click">
        <el-button
          class="ease-btn ml-[16px]"
          size="small"
          :disabled="!multipleSelection.length"
          :class="{ 'is-disabled': !multipleSelection.length }"
        >
          <i
            class="iconfont iconfont-c !text-[#4c5cec] icon-piliang mr-2"
            :class="{ '!text-[#C0C4CC]': !multipleSelection.length }"
          ></i>
          批量操作
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="handleEnableBatch(true)"> 启用 </el-dropdown-item>
            <el-dropdown-item @click="handleEnableBatch(false)"> 禁用 </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <div class="flex justify-end">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="text-right">
        <el-form-item label="" prop="name" class="!mr-0">
          <el-input
            v-model="queryParams.name"
            style="width: 400px"
            placeholder="搜索文件"
            clearable
            @clear="handleQuery"
            @keyup.enter="handleQuery"
          >
            <template #suffix>
              <el-icon>
                <Search />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>
      </el-form>
    </div>
  </div>

  <el-table
    v-loading="loading"
    :data="tableData"
    row-key="id"
    style="width: 100%"
    @sort-change="sortChange"
    @selection-change="handleSelectionChange"
  >
    <el-table-column type="selection" width="55" />
    <el-table-column prop="name" label="名称" min-width="200" show-overflow-tooltip>
      <template #default="{ row }">
        <div class="knowledge-name-in-table" @click="handleFile(row)">
          <FileTypePic :suffix="row.fileSuffix" />
          <div class="knowledge-name">
            {{ row.name }}
            <el-tooltip
              v-if="!row.modelExist"
              effect="dark"
              content="向量模型被删除，请重新设置向量模型。"
              placement="bottom-start"
            >
              <img class="has-error" src="@/assets/chat/error.svg" alt="" />
            </el-tooltip>
          </div>
        </div>
      </template>
    </el-table-column>
    <el-table-column prop="blockSize" label="分块数" width="120" />
    <el-table-column prop="uploadTime" label="上传时间" width="170" sortable="custom" />
    <el-table-column prop="enabled" label="启用" width="90">
      <template #default="{ row }">
        <EnableSwitch
          v-model="row.enabled"
          :handle-func-promise="() => handleEnable(row.id, row.enabled)"
        />
      </template>
    </el-table-column>
    <el-table-column prop="status" label="处理状态" width="158">
      <!-- 状态 1-处理中 2-完成 3-失败 -->

      <template #default="{ row }">
        <template v-if="row.status === 3">
          <el-tooltip :content="row.errorMsg" placement="top">
            <span>
              <el-icon color="#F25B37" size="20" class="align-sub mr-[8px]">
                <CircleCloseFilled />
              </el-icon>
              <span class="color-[#30343A]">处理失败</span>
            </span>
          </el-tooltip>

          <img
            class="cursor-pointer size-[16px] inline-block ml-[6px] align-sub"
            :class="{ 'rotate-ccw': retryLoading }"
            src="@/assets/knowledge/refresh-icon.png"
            alt=""
            @click="handleRetry(row)"
          />
        </template>
        <template v-else-if="row.status === 2">
          <el-icon color="#1EBF60" size="20" class="align-sub mr-[8px]"><SuccessFilled /></el-icon>
          <span class="color-[#30343A]">完成</span>
        </template>
        <template v-else-if="row.status === 1">
          <el-icon color="#FFC411" size="20" class="align-sub mr-[8px]"><RemoveFilled /></el-icon>
          <span class="color-[#30343A]">处理中</span>
        </template>
      </template>
    </el-table-column>
    <el-table-column v-if="canEditHere" label="操作" width="80" fixed="right">
      <template #default="{ row }">
        <el-button link type="primary" @click="handleDelete(row.id)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>

  <Pagination
    v-show="total > 0"
    v-model:page="queryParams.pageNum"
    v-model:limit="queryParams.pageSize"
    :total="total"
    @pagination="getKnowledgeItemList"
  />
</template>

<style scoped lang="less">
.ease-btn {
  border-radius: 8px;
  border: 1px solid #dadde8;
  padding: 9px 12px;
  background: #f7f8fa;
  font-size: 14px;
  &.disabled {
    background: rgba(230, 234, 244, 0.6);
  }
  &.is-disabled {
    cursor: not-allowed;
    opacity: 0.7;
    background: rgba(230, 234, 244, 0.6);
  }
}

:deep(.el-checkbox) {
  background-color: #fff;
}

@keyframes rotate-ccw {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-360deg);
  }
}

.rotate-ccw {
  animation: rotate-ccw 1s linear infinite;
}

.knowledge-name-in-table {
  display: flex;
  align-items: center;
  cursor: pointer;

  .knowledge-name {
    max-width: 100%;
    margin-left: 4px;
    overflow: hidden;
    white-space: nowrap;
    min-width: 0;
    text-overflow: ellipsis;

    position: relative;
    padding-right: 26px;
  }

  .has-error {
    width: 17px;
    height: 17px;
    position: absolute;
    right: 0;
    top: 2px;
  }
}
</style>
