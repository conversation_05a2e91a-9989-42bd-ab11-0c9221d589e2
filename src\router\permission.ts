import type { Router } from 'vue-router'
import { useAgentUserStore, useUserStore } from '@/store'
import { setRequestAgentContext } from '@/utils/token-manager'
import { buildTimeChecker } from '@/utils/build-time-checker'

// 白名单路由
const whiteList = ['/404', '/500', '/invite', '/login', '/terms']

// 白名单路由模式（支持通配符匹配）
const whiteListPatterns = [
  /^\/share\/.+/, // 对外发布公开页面，格式：/share/:agentId
  // /^\/chat\/public/, // 公开聊天页面，格式：/chat/public
]

// 需要管理员权限的路由前缀
const adminRoutes = ['/settings']

export function setupPageGuard(router: Router) {
  router.beforeEach(async (to, from, next) => {
    // console.log('=== 路由守卫开始 ===')
    // console.log('目标路由:', to.path)

    // 在路由跳转时检查构建时间更新
    // 只在非首次加载时检查（from.name 存在表示不是首次访问）
    // 避免在已经检测到更新且通知显示中时重复检查
    if (
      from.name &&
      !buildTimeChecker.isUpdateDetected() &&
      !buildTimeChecker.isNotificationActive()
    ) {
      buildTimeChecker.forceCheck()
    }

    // 检查是否为公开助手页面，如果是则使用助手特定的Store
    const isPublicAgentRoute = /^\/share\/(.+)/.test(to.path)
    const isPublicChatRoute = to.name === 'public-chat' && to.query.releaseId

    let userStore
    let releaseId: string | null = null

    if (isPublicAgentRoute) {
      // 从路由参数提取agentId
      const match = to.path.match(/^\/agent\/public\/(.+)/)
      releaseId = match?.[1] || null
    } else if (isPublicChatRoute) {
      // 从query参数提取agentId
      releaseId = to.query.releaseId as string
    }

    if (releaseId) {
      // 设置助手上下文
      setRequestAgentContext(releaseId)
      userStore = useAgentUserStore(releaseId)
      console.log('路由守卫 - 使用助手Store:', releaseId)
    } else {
      // 使用默认用户Store
      userStore = useUserStore()
      console.log('路由守卫 - 使用默认Store')
    }

    try {
      // resolved auth模块已废弃，移除相关session检查代码

      // 处理登录状态
      if (!isPublicChatRoute && userStore.token && userStore.userId) {
        // 如果已登录且要访问登录页，则重定向到首页
        if (to.path === '/login') {
          return next('/')
        }

        // console.log('有token，检查用户角色')
        if (!userStore.roles.length) {
          try {
            // console.log('无角色信息，获取用户信息')
            await userStore.getInfo()
            // console.log('获取用户信息成功')

            // 检查权限路由
            if (
              adminRoutes.some(route => to.path.startsWith(route)) &&
              !(
                userStore.roles.includes('superadmin') ||
                userStore.permissions.includes('settings:module:all')
              )
            ) {
              return next('/404')
            }

            return next()
          } catch (error) {
            // console.log('获取用户信息失败:', error)
            await userStore.logout()

            // 如果是白名单路由（如邀请页面），在清除过期token后直接放行，避免重定向
            const isWhiteList =
              whiteList.includes(to.path) ||
              whiteListPatterns.some(pattern => pattern.test(to.path))

            if (isWhiteList) {
              console.log('token过期但访问白名单路由，清除token后直接通过')
              return next()
            }

            // 如果是公开助手相关页面，跳转到助手公开页面而不是登录页
            if (releaseId && (isPublicAgentRoute || isPublicChatRoute)) {
              return next(`/share/${releaseId}`)
            }

            // 其他情况重定向到登录页
            return next(`/login?redirect=${to.path}`)
          }
        }

        // 检查权限路由
        if (
          adminRoutes.some(route => to.path.startsWith(route)) &&
          !(
            userStore.roles.includes('superadmin') ||
            userStore.permissions.includes('settings:module:all')
          )
        ) {
          return next('/404')
        }

        return next()
      } else if (isPublicChatRoute && userStore.token && releaseId) {
        return next()
      }

      // 未登录情况
      console.log('无(userStore.token && userStore.userId)，检查是否白名单路由')
      const isWhiteList =
        whiteList.includes(to.path) || whiteListPatterns.some(pattern => pattern.test(to.path))

      if (isWhiteList) {
        console.log('是白名单路由，直接通过')
        return next()
      } else {
        console.log('非白名单路由，重定向到登录页')

        // 如果是公开聊天页面且有agentId，跳转到助手公开页面
        if (releaseId && isPublicChatRoute) {
          return next(`/share/${releaseId}`)
        }

        return next(`/login?redirect=${to.path}`)
      }
    } catch (error) {
      console.error('路由守卫出错:', error)
      return next()
    } finally {
      // console.log('=== 路由守卫结束 ===')
    }
  })
}
