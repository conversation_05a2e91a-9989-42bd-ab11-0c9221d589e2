const { chromium } = require('playwright')

;(async () => {
  // 启动浏览器
  console.log('启动浏览器...')
  const browser = await chromium.launch({
    headless: false, // 设置为非无头模式，以便可以看到浏览器界面
  })

  // 创建新页面
  console.log('创建新页面...')
  const page = await browser.newPage()

  // 导航到百度
  console.log('正在打开百度页面...')
  await page.goto('https://www.baidu.com')

  console.log('已成功打开百度页面！')

  // 等待用户手动关闭浏览器
  console.log('浏览器将保持打开状态，请手动关闭或按Ctrl+C终止程序')

  // 这里不调用browser.close()，以便用户可以看到并与页面交互
})().catch(err => {
  console.error('发生错误:', err)
  process.exit(1)
})
