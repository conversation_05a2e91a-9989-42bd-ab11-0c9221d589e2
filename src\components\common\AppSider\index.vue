<script setup lang="ts">
import { computed, h, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import type { MenuOption } from 'naive-ui'
import { SvgIcon } from '@/components/common'
import Settings from '@/components/common/SettingsDialog/index.vue'
import { useUserStore } from '@/store'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 检查用户是否为超级管理员
const isAdmin = computed(
  () =>
    userStore.roles.includes('superadmin') || userStore.permissions.includes('settings:module:all'),
)

// 检查是否为开发环境
const isDev = computed(() => import.meta.env.DEV)

const activeKey = computed(() => {
  if (route.path.startsWith('/chat')) return 'chat'
  if (route.path.startsWith('/agent')) return 'agent'
  if (route.path.startsWith('/knowledge-base')) return 'knowledge-base'
  if (route.path.startsWith('/settings')) return 'settings'
  if (route.path.startsWith('/dev')) return 'dev'
  return route.name as string
})

const menuOptions = computed(() => {
  // resolved menuOptions这些icon都替换成iconfont的icon
  const baseMenu = [
    {
      label: 'AI对话',
      key: 'chat',
      icon: () =>
        h('i', { style: 'font-size:1.5rem;color:#9da0a6;', class: 'iconfont icon-Aiduihua' }),
    },
    {
      label: '助手',
      key: 'agent',
      icon: () =>
        h('i', { style: 'font-size:1.5rem;color:#9da0a6;', class: 'iconfont icon-zhushou' }),
    },
    {
      label: '知识库',
      key: 'knowledge-base',
      icon: () =>
        h('i', { style: 'font-size:1.5rem;color:#9da0a6;', class: 'iconfont icon-zhishiku' }),
    },
  ] as MenuOption[]

  // 仅当用户角色包含 superadmin 时才添加设置菜单选项
  if (isAdmin.value) {
    baseMenu.push({
      label: '设置',
      key: 'settings',
      icon: () =>
        h('i', { style: 'font-size:1.5rem;color:#9da0a6;', class: 'iconfont icon-shezhi' }),
    })
  }

  // 仅在开发环境下添加开发菜单选项
  if (isDev.value) {
    baseMenu.push({
      label: '开发',
      key: 'dev',
      icon: () =>
        h('i', { style: 'font-size:1.5rem;color:#9da0a6;', class: 'iconfont icon-cengjishezhi' }),
    })
  }

  return baseMenu
})

function handleMenuSelect(key: string) {
  // 如果点击了设置菜单项但用户不是超级管理员，则不允许导航
  if (key === 'settings' && !isAdmin.value) {
    return
  }
  router.push({ name: key })
}

function handleOpenHome() {
  router.push({ name: 'chat' })
}

const showSettings = ref(false)
</script>

<template>
  <div class="app-sider h-full flex flex-col bg-white dark:bg-[#111114]">
    <!-- Logo -->
    <div
      class="h-14 flex items-center justify-center dark:border-neutral-800 pt-[20px] mb-[20px] cursor-pointer"
      @click="handleOpenHome"
    >
      <img src="@/assets/blue_logo.png" alt="logo" class="h-[64px] w-[64px] logo-transition" />
    </div>

    <!-- Navigation Menu -->
    <div class="flex-1 overflow-hidden hover:overflow-y-auto text-base">
      <NMenu
        :value="activeKey"
        :options="menuOptions"
        class="tiny-menu"
        @update:value="handleMenuSelect"
      />
    </div>
    <div
      class="h-14 flex items-center justify-center dark:border-neutral-800 pb-[30px] cursor-pointer"
    >
      <img
        src="@/assets/logo-gray.png"
        alt="logo"
        class="h-[56px] w-[56px] logo-gray-transition"
        @click="showSettings = true"
      />
    </div>
    <Settings v-model:show="showSettings" />
  </div>
</template>

<style lang="less" scoped>
@import '@/styles/variables.less';
:deep(.n-menu-item) {
  margin: 5px 0;
  min-width: 82px !important;
  width: 82px !important;
  height: 80px !important;

  .n-menu-item-content--selected {
    .iconfont {
      color: @c!important;
    }
  }

  .n-menu-item-content {
    // background-color: red;
    padding: 0 !important;
    justify-content: center;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    text-align: center !important;
    gap: 4px;
  }

  .n-menu-item-content-icon {
    margin: 0 auto !important;
    text-align: center !important;
    font-size: 1.2em;
  }

  .n-menu-item-content__icon {
    margin-right: 0 !important;
  }

  .n-menu-item-content__text {
    font-size: 12px;
    margin-top: 4px;
  }

  /* 让菜单项垂直排列文字和图标 */
  .n-menu-item-content {
    flex-direction: column;
    align-items: center;
  }
}
/* 添加 Logo hover 效果 */
.logo-transition {
  transition: all 0.3s ease-in-out;

  &:hover {
    transform: scale(1.05);
    filter: brightness(1.3);
  }
}
.logo-gray-transition {
  transition: all 0.3s ease-in-out;

  &:hover {
    transform: scale(1.05);
  }
}
</style>
