<script lang="ts" setup>
import { computed } from 'vue'
import { NAvatar } from 'naive-ui'
import { useUserStore } from '@/store'
import { isString } from '@/utils/is'
import defaultAvatar from '@/assets/DEFAULT_USER_AVATAR.png'
import robotAvatar from '@/assets/DEFAULT_ROBOT_AVATAR.png'

interface Props {
  image?: boolean
}
defineProps<Props>()

const userStore = useUserStore()

const avatar = computed(() => userStore.avatar)
const userInfo = computed(() => userStore.userInfo)

// 生成头像显示字符
const avatarText = computed(() => {
  if (!userInfo.value?.nickName) return ''
  return userInfo.value.nickName.charAt(0)
})

// 根据姓名生成随机颜色
const avatarColor = computed(() => {
  if (!userInfo.value?.nickName) return '#4c5cec'

  // 简单哈希函数生成颜色
  let hash = 0
  for (let i = 0; i < userInfo.value.nickName.length; i++) {
    hash = userInfo.value.nickName.charCodeAt(i) + ((hash << 5) - hash)
  }

  // 固定一些好看的颜色
  const colors = ['#4c5cec']

  const index = Math.abs(hash) % colors.length
  return colors[index]
})
</script>

<template>
  <template v-if="image">
    <NAvatar
      v-if="isString(avatar) && avatar.length > 0"
      :src="avatar"
      :fallback-src="defaultAvatar"
    />
    <div v-else class="avatar-circle" :style="{ backgroundColor: avatarColor }">
      <template v-if="userInfo?.nickName">
        <span class="text-[14px]">{{ avatarText }}</span>
      </template>
      <NAvatar v-else round :src="defaultAvatar" />
    </div>
  </template>
  <span v-else class="text-[28px] dark:text-white">
    <NAvatar round :src="robotAvatar" />
  </span>
</template>

<style lang="less" scoped>
.n-avatar {
  width: 40px;
  height: 40px;
  flex: 0 0 auto;
  img {
    width: 40px;
    height: 40px;
  }
}

.avatar-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #4c5cec;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}
</style>
