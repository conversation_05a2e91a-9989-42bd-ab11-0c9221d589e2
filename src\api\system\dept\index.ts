import type { AxiosPromise } from 'axios'
import type { DeptF<PERSON>, DeptQ<PERSON>y, DeptVO } from './types'
import request from '@/utils/request'

// 查询部门列表
export const listDept = (query?: DeptQuery) => {
  return request({
    url: '/system/dept/list',
    method: 'get',
    params: query,
  })
}

// 查询部门列表（排除节点）
export const listDeptExcludeChild = (deptId: string | number): AxiosPromise<DeptVO[]> => {
  return request({
    url: `/system/dept/list/exclude/${deptId}`,
    method: 'get',
  })
}

// 查询部门详细
export const getDept = (deptId: string | number): AxiosPromise<DeptVO> => {
  return request({
    url: `/system/dept/${deptId}`,
    method: 'get',
  })
}

// 查询部门下拉树结构
export const treeselect = (): AxiosPromise<DeptVO[]> => {
  return request({
    url: '/system/dept/treeselect',
    method: 'get',
  })
}

// 新增部门
export const addDept = (data: DeptForm) => {
  return request({
    url: '/system/dept',
    method: 'post',
    data,
  })
}

// 修改部门
export const updateDept = (data: DeptForm) => {
  return request({
    url: '/system/dept',
    method: 'put',
    data,
  })
}

// 删除部门
export const delDept = (deptId: number | string) => {
  return request({
    url: `/system/dept/${deptId}`,
    method: 'delete',
  })
}
