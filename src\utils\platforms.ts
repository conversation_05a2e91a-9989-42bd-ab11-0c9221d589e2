import ZhinaoProviderLogo from '@/assets/model-logos/360.png'
import HunyuanProviderLogo from '@/assets/model-logos/hunyuan.png'
import AzureProviderLogo from '@/assets/model-logos/microsoft.png'
import Ai302ProviderLogo from '@/assets/platform-logos/302ai.webp'
import AiHubMixProviderLogo from '@/assets/platform-logos/aihubmix.webp'
import AlayaNewProviderLogo from '@/assets/platform-logos/alayanew.webp'
import AnthropicProviderLogo from '@/assets/platform-logos/anthropic.png'
import BaichuanProviderLogo from '@/assets/platform-logos/baichuan.png'
import BaiduCloudProviderLogo from '@/assets/platform-logos/baidu-cloud.svg'
import BailianProviderLogo from '@/assets/platform-logos/bailian.png'
import BurnCloudProviderLogo from '@/assets/platform-logos/burncloud.png'
import CephalonProviderLogo from '@/assets/platform-logos/cephalon.jpeg'
import DeepSeekProviderLogo from '@/assets/platform-logos/deepseek.png'
import DmxapiProviderLogo from '@/assets/platform-logos/DMXAPI.png'
import FireworksProviderLogo from '@/assets/platform-logos/fireworks.png'
import GiteeAIProviderLogo from '@/assets/platform-logos/gitee-ai.png'
import GithubProviderLogo from '@/assets/platform-logos/github.png'
import GoogleProviderLogo from '@/assets/platform-logos/google.png'
import GPUStackProviderLogo from '@/assets/platform-logos/gpustack.svg'
import GrokProviderLogo from '@/assets/platform-logos/grok.png'
import GroqProviderLogo from '@/assets/platform-logos/groq.png'
import HyperbolicProviderLogo from '@/assets/platform-logos/hyperbolic.png'
import InfiniProviderLogo from '@/assets/platform-logos/infini.png'
import JinaProviderLogo from '@/assets/platform-logos/jina.png'
import LanyunProviderLogo from '@/assets/platform-logos/lanyun.png'
import LMStudioProviderLogo from '@/assets/platform-logos/lmstudio.png'
import MinimaxProviderLogo from '@/assets/platform-logos/minimax.png'
import MistralProviderLogo from '@/assets/platform-logos/mistral.png'
import ModelScopeProviderLogo from '@/assets/platform-logos/modelscope.png'
import MoonshotProviderLogo from '@/assets/platform-logos/moonshot.png'
import NewAPIProviderLogo from '@/assets/platform-logos/newapi.png'
import NvidiaProviderLogo from '@/assets/platform-logos/nvidia.png'
import O3ProviderLogo from '@/assets/platform-logos/o3.png'
import OcoolAiProviderLogo from '@/assets/platform-logos/ocoolai.png'
import OllamaProviderLogo from '@/assets/platform-logos/ollama.png'
import OpenAiProviderLogo from '@/assets/platform-logos/openai.png'
import OpenRouterProviderLogo from '@/assets/platform-logos/openrouter.png'
import PerplexityProviderLogo from '@/assets/platform-logos/perplexity.png'
import Ph8ProviderLogo from '@/assets/platform-logos/ph8.png'
import PPIOProviderLogo from '@/assets/platform-logos/ppio.png'
import QiniuProviderLogo from '@/assets/platform-logos/qiniu.webp'
import SiliconFlowProviderLogo from '@/assets/platform-logos/silicon.png'
import StepProviderLogo from '@/assets/platform-logos/step.png'
import TencentCloudProviderLogo from '@/assets/platform-logos/tencent-cloud-ti.png'
import TogetherProviderLogo from '@/assets/platform-logos/together.png'
import TokenFluxProviderLogo from '@/assets/platform-logos/tokenflux.png'
import VertexAIProviderLogo from '@/assets/platform-logos/vertexai.svg'
import BytedanceProviderLogo from '@/assets/platform-logos/volcengine.png'
import VoyageAIProviderLogo from '@/assets/platform-logos/voyageai.png'
import XirangProviderLogo from '@/assets/platform-logos/xirang.png'
import ZeroOneProviderLogo from '@/assets/platform-logos/zero-one.png'
import ZhipuProviderLogo from '@/assets/platform-logos/zhipu.png'

// 添加默认LOGO（使用OpenAI作为临时默认）
import DefaultProviderLogo from '@/assets/platform-logos/openai.png'

// 显示名称映射表
// 显示名称映射表 - 完善了中英文对照
const DISPLAY_NAME_MAPPING: Record<string, keyof typeof PROVIDER_LOGO_MAP> = {
  // 中文映射 - 重点加强智谱AI相关映射
  '360智脑': 'zhinao',
  '混元大模型': 'hunyuan',
  '深度求索': 'deepseek',
  '智谱AI': 'zhipu',
  '智谱': 'zhipu',
  'GLM': 'zhipu',
  '清言': 'zhipu',
  '百度智能云': 'baidu-cloud',
  '文心一言': 'baidu-cloud',
  '火山方舟': 'doubao',
  '豆包': 'doubao',
  '腾讯云': 'tencent-cloud-ti',
  '腾讯混元': 'hunyuan',
  '阶跃星辰': 'stepfun',
  '跃问': 'stepfun',
  '零一万物': 'yi',
  '万知': 'yi',
  '月之暗面': 'moonshot',
  '百川智能': 'baichuan',
  '阿里通义': 'dashscope',
  '通义千问': 'dashscope',
  '魔搭社区': 'modelscope',
  '魔搭': 'modelscope',
  '硅基流动': 'silicon',
  '稀壤': 'xirang',
  '海螺AI': 'minimax',
  '七牛云': 'qiniu',
  '英伟达': 'nvidia',
  '码云AI': 'gitee-ai',
  '阶跃函数': 'stepfun',
  '阿里灵积': 'dashscope',
  '字节跳动': 'doubao',
  '超参数': 'hyperbolic',
  'MiniMax': 'minimax',
  '智谱清言': 'zhipu',
  '智谱开放平台': 'zhipu',
  '火山引擎': 'doubao',
  '蓝耘科技': 'gpustack',
  '派欧云': 'ppio',
  '302': '302ai',
  '百川': 'baichuan',
  '阿里云百炼': 'dashscope',
  '百度云': 'baidu-cloud',
  '天翼云': 'lanyun',
  '无问苍穹': 'hyperbolic',

  // 英文别名映射
  'ChatGLM': 'zhipu',
  'GLM-4': 'zhipu',
  'OpenAI': 'openai',
  'ChatGPT': 'openai',
  'Azure OpenAI': 'azure-openai',
  'Azure': 'azure-openai',
  'Google': 'gemini',
  'Gemini': 'gemini',
  'Anthropic': 'anthropic',
  'Claude': 'anthropic',
  'Groq': 'groq',
  'Mistral': 'mistral',
  'Ollama': 'ollama',
  'LM Studio': 'lmstudio',
  'OpenRouter': 'openrouter',
  'DeepSeek': 'deepseek',
  'Zhipu AI': 'zhipu',
  'ZhipuAI': 'zhipu',
  'Moonshot': 'moonshot',
  'Baichuan': 'baichuan',
  'Minimax': 'minimax',
  'GitHub Copilot': 'github',
  'Vertex AI': 'vertexai',
  'Perplexity': 'perplexity',
  'Fireworks': 'fireworks',
  'Together': 'together',
  'NVIDIA': 'nvidia',
  'Gitee AI': 'gitee-ai',
  '01.AI': 'yi',
  'Step Fun': 'stepfun',
  'ModelScope': 'modelscope',
  'Hyperbolic': 'hyperbolic',
  'ByteDance': 'doubao',
};

// 名称标准化函数
function normalizeName(name: string) {
  return name.toLowerCase().replace(/[\s\-_]+/g, '');
}

const PROVIDER_LOGO_MAP = {
  ph8: Ph8ProviderLogo,
  '302ai': Ai302ProviderLogo,
  openai: OpenAiProviderLogo,
  silicon: SiliconFlowProviderLogo,
  deepseek: DeepSeekProviderLogo,
  'gitee-ai': GiteeAIProviderLogo,
  yi: ZeroOneProviderLogo,
  groq: GroqProviderLogo,
  zhipu: ZhipuProviderLogo,
  ollama: OllamaProviderLogo,
  lmstudio: LMStudioProviderLogo,
  moonshot: MoonshotProviderLogo,
  openrouter: OpenRouterProviderLogo,
  baichuan: BaichuanProviderLogo,
  dashscope: BailianProviderLogo,
  modelscope: ModelScopeProviderLogo,
  xirang: XirangProviderLogo,
  anthropic: AnthropicProviderLogo,
  aihubmix: AiHubMixProviderLogo,
  burncloud: BurnCloudProviderLogo,
  gemini: GoogleProviderLogo,
  stepfun: StepProviderLogo,
  doubao: BytedanceProviderLogo,
  minimax: MinimaxProviderLogo,
  github: GithubProviderLogo,
  copilot: GithubProviderLogo,
  ocoolai: OcoolAiProviderLogo,
  together: TogetherProviderLogo,
  fireworks: FireworksProviderLogo,
  zhinao: ZhinaoProviderLogo,
  nvidia: NvidiaProviderLogo,
  'azure-openai': AzureProviderLogo,
  hunyuan: HunyuanProviderLogo,
  grok: GrokProviderLogo,
  hyperbolic: HyperbolicProviderLogo,
  mistral: MistralProviderLogo,
  jina: JinaProviderLogo,
  ppio: PPIOProviderLogo,
  'baidu-cloud': BaiduCloudProviderLogo,
  dmxapi: DmxapiProviderLogo,
  perplexity: PerplexityProviderLogo,
  infini: InfiniProviderLogo,
  o3: O3ProviderLogo,
  'tencent-cloud-ti': TencentCloudProviderLogo,
  gpustack: GPUStackProviderLogo,
  alayanew: AlayaNewProviderLogo,
  voyageai: VoyageAIProviderLogo,
  qiniu: QiniuProviderLogo,
  tokenflux: TokenFluxProviderLogo,
  cephalon: CephalonProviderLogo,
  lanyun: LanyunProviderLogo,
  vertexai: VertexAIProviderLogo,
  'new-api': NewAPIProviderLogo
} as const

export function getProviderLogo(providerId: string) {
  // 1. 直接匹配
  const directMatch = PROVIDER_LOGO_MAP[providerId as keyof typeof PROVIDER_LOGO_MAP];
  if (directMatch) return directMatch;

  // 2. 标准化处理并匹配
  const normalized = normalizeName(providerId);
  const normalizedMatch = Object.entries(PROVIDER_LOGO_MAP).find(
    ([key]) => normalizeName(key) === normalized
  )?.[1];
  if (normalizedMatch) return normalizedMatch;

  // 3. 显示名称映射匹配
  const mappedKey = DISPLAY_NAME_MAPPING[providerId];
  if (mappedKey) {
    return PROVIDER_LOGO_MAP[mappedKey];
  }

  // 4. 返回默认LOGO
  // return DefaultProviderLogo;
}