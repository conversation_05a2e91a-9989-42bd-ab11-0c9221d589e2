<script setup lang="ts">
import { NMenu } from 'naive-ui'
import { useRoute, useRouter } from 'vue-router'
import { h, onMounted, ref, watch } from 'vue'
import { SvgIcon } from '@/components/common'
import { useKnowledgeBaseStore } from '@/store/modules/knowledge-base'
import ms from '@/utils/message'
import { useStorageUsage } from '@/hooks/useStorageUsage'

const router = useRouter()
const route = useRoute()
const collapsed = ref(false)
const activeKey = ref(route.name as string)
const knowledgeStore = useKnowledgeBaseStore()
const breadcrumbList = ref<string[]>([])

// 计算面包屑路径
const generateBreadcrumb = () => {
  const matched = route.matched.filter(item => !item.meta?.hideBreadcrumb)

  breadcrumbList.value = matched
    .map(item => {
      // 处理动态标题的路由
      if (item.meta?.dynamicTitle) {
        return {
          title: knowledgeStore.currentKnowledgeTitle || '',
          path: item.path,
        }
      }
      return {
        title: (item.meta?.title || '') as string,
        path: item.path,
      }
    })
    .filter(item => item.title)
    .map(item => item.title)
}

const { usedSize, totalSize, usagePercentage } = useStorageUsage()

watch(() => route.path, generateBreadcrumb, { immediate: true })
watch(() => knowledgeStore.currentKnowledgeTitle, generateBreadcrumb)

// 监听路由变化更新菜单激活状态
watch(
  () => route.name,
  newName => {
    if (newName) {
      const routeName = newName as string
      if (['dataset', 'searchTest', 'config'].includes(routeName)) {
        activeKey.value = 'my-knowledge-base'
      } else if (['share-dataset', 'share-searchTest'].includes(routeName)) {
        activeKey.value = 'shared-knowledge-base'
      } else {
        activeKey.value = routeName
      }
    }
  },
)

// resolved 改成用icon-wodezhishiku和icon-gongxiangzhishiku
const menuOptions = [
  {
    label: '我的知识库',
    key: 'my-knowledge-base',
    icon: renderImg('icon-wodezhishiku'),
  },
  {
    label: '共享知识库',
    key: 'shared-knowledge-base',
    icon: renderImg('icon-gongxiangzhishiku'),
  },
]

function renderImg(url: string) {
  return () =>
    h(
      'div',
      {
        // resolved 为什么i没有居中
        style: {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          marginRight: '16px',
          width: '32px',
          height: '32px',
          flexShrink: '0',
          border: '1px solid #C3CCF9',
          background: '#FFFFFF',
          borderRadius: '50%',
        },
      },
      [
        h('i', {
          class: `iconfont iconfont-c ${url}`,
          style: {
            width: '24px',
            height: '24px',
            borderRadius: '50%',
            flexShrink: '0',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '16px',
            lineHeight: '24px',
            textAlign: 'center',
          },
        }),
      ],
    )
}

function renderIcon(icon: string) {
  return () =>
    h(
      'div',
      {
        style: {
          display: 'inline-flex',
          alignItems: 'center',
          marginRight: '16px',
        },
      },
      [h(SvgIcon, { icon })],
    )
}

function handleUpdateValue(key: string) {
  const route = router.resolve(`/knowledge-base/${key}`)
  if (route.matched.length === 0) {
    ms.warning('该功能暂未开放')
    return
  }
  router.push(`/knowledge-base/${key}`)
}

function handleDetail() {
  console.log('handleDetail')
}
</script>

<template>
  <div class="knowledge-container">
    <div class="relative h-full">
      <div class="knowledge-sider h-full flex flex-col" :class="{ contract: collapsed }">
        <div class="top-con">
          <div class="box1">
            <div class="t1">企业剩余容量</div>
            <!-- <div class="btn1" @click="handleDetail">查看明细</div> -->
          </div>
          <div class="box2">
            <!-- resolved 调用getResourceOssUsedDistSize获取 -->
            <span class="t1">{{ usedSize.toFixed(2) }}GB</span>
            <span class="t2">共<span style="font-size: 1.5em" class="px-1">∞</span>GB</span>
          </div>
          <el-progress :percentage="usagePercentage" :show-text="false" />
        </div>
        <div class="flex-1 ex-n-menu-box">
          <NMenu
            :options="menuOptions"
            :value="activeKey"
            :default-value="activeKey"
            @update:value="handleUpdateValue"
          />
        </div>
      </div>
      <!-- <span class="collapse-tag" @click="collapsed = !collapsed">
        <el-icon v-if="collapsed" color="#575B66" size="19"><ArrowRightBold /></el-icon>
        <el-icon v-else color="#575B66" size="19"><ArrowLeftBold /></el-icon>
      </span> -->
    </div>
    <div class="knowledge-content">
      <div class="my-breadcrumb">
        <!-- resolved 按src\views\settings\index.vue的breadcrumbs改造 -->
        <div class="flex justify-between items-center">
          <div class="w-[500px] whitespace-nowrap text-ellipsis overflow-hidden">
            {{ breadcrumbList.join(' / ') }}
          </div>
        </div>
      </div>
      <div class="knowledge-content-body">
        <router-view />
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.knowledge-container {
  height: 100%;
  display: flex;
  overflow-x: hidden;
}

.knowledge-sider {
  width: 240px;
  background: #fff;
  padding: 8px;
  overflow: hidden;
  transition: all 0.3s ease;

  &.contract {
    width: 0px;
  }

  .top-con {
    width: 224px;
    display: flex;
    flex-direction: column;
    padding: 16px;
    border-bottom: 1px solid #dadde8;
    margin-bottom: 16px;

    .box1 {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .t1 {
        font-size: 16px;
      }
      .btn1 {
        font-size: 14px;
        color: #4865e8;
        cursor: pointer;
      }
    }

    .box2 {
      display: flex;
      align-items: baseline;
      justify-content: space-between;

      .t1 {
        font-size: 24px;
        font-weight: bold;
        color: #30343a;
      }

      .t2 {
        font-size: 14px;
        font-weight: bold;
        color: #646a73;
      }
    }
  }
}

.knowledge-content {
  flex: 1;
  min-width: 0;
  width: 0;
  display: flex;
  flex-direction: column;
  border-left: 1px solid #e5e7eb;
  background: #f7f8fa;
  height: 100%;
}

// .knowledge-header {
//   padding: 16px 24px;
//   border-bottom: 1px solid #e5e7eb;
//   display: flex;
//   justify-content: space-between;
//   align-items: center;
// }

// .knowledge-title {
//   font-size: 20px;
// }

.knowledge-content-body {
  flex: 1;
  min-height: 0;
}

.collapse-tag {
  cursor: pointer;
  border-radius: 50%;
  position: absolute;
  right: -0.75rem;
  top: 50%;
  width: 28px;
  height: 28px;
  background: #ffffff;
  box-shadow: 2px 0px 4px 0px rgba(105, 139, 207, 0.2);
  border: 1px solid rgba(218, 221, 232, 0.65);
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
