<script setup lang="ts">
const props = defineProps<{
  suffix: string
}>()

defineOptions({
  name: 'FiletypePid',
})

const fileType = computed(() => {
  switch (props.suffix) {
    case '.doc':
    case '.docx':
      return 'word';
    case '.xls':
    case '.xlsx':
      return 'excel';
    case '.pdf':
      return 'pdf';
    case '.txt':
      return 'txt';
    case '.md':
      return 'md';
    case '.html':
      return 'html';
    case '.ppt':
    case '.pptx':
      return 'ppt';
    default:
      return 'other';
  }
});
</script>

<template>
  <img
    v-if="fileType === 'word'"
    src="@/assets/word.png"
    alt="word"
    class="w-[22px] h-[22px] inline-block"
  />
  <img
    v-if="fileType === 'excel'"
    src="@/assets/excel.png"
    alt="excel"
    class="w-[22px] h-[22px] inline-block"
  />
  <img
    v-if="fileType === 'pdf'"
    src="@/assets/pdf.png"
    alt="pdf"
    class="w-[22px] h-[22px] inline-block"
  />
  <img
    v-if="fileType === 'txt'"
    src="@/assets/txt.png"
    alt="txt"
    class="w-[22px] h-[22px] inline-block"
  />
  <img
    v-if="fileType === 'md'"
    src="@/assets/md.png"
    alt="md"
    class="w-[22px] h-[22px] inline-block"
  />
  <img
    v-if="fileType === 'html'"
    src="@/assets/html.png"
    alt="html"
    class="w-[17px] h-[20px] inline-block"
  />
  <img
    v-if="fileType === 'ppt'"
    src="@/assets/ppt.png"
    alt="ppt"
    class="w-[18px] h-[20px] inline-block"
  />
  <img
    v-if="fileType === 'other'"
    src="@/assets/knowledge/file-icon.png"
    alt="other"
    class="w-[22px] h-[22px] inline-block"
  />
  <!-- 如无相关图片，可补充或用默认file.png
  <img
    v-if="fileType === 'jpg'"
    src="@/assets/jpg.png"
    alt=""
    class="w-[22px] h-[22px] inline-block"
  />
  <img
    v-if="fileType === 'png'"
    src="@/assets/png.png"
    alt=""
    class="w-[22px] h-[22px] inline-block"
  /> -->
</template>
