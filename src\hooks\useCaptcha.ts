import { ref } from 'vue'
import { checkCaptcha } from '@/components/common/LoginDialog/utils/api'

export function useCaptcha() {
  const showCaptcha = ref(false)
  const captchaData = ref<any>(null)

  // 检查是否需要滑块验证
  const checkNeedCaptcha = async (usernameOrPhone: string, tenantId: string) => {
    try {
      const response = await checkCaptcha(usernameOrPhone, tenantId)
      return response.data.needCaptcha
    } catch (error) {
      console.error('检查验证码失败:', error)
      return false
    }
  }

  // 显示滑块验证
  const showCaptchaDialog = () => {
    showCaptcha.value = true
  }

  // 隐藏滑块验证
  const hideCaptchaDialog = () => {
    showCaptcha.value = false
    // 注意：不要在这里清空captchaData，因为登录时还需要使用
  }

  // 重置验证数据
  const resetCaptchaData = () => {
    captchaData.value = null
  }

  // 验证成功回调
  const handleCaptchaSuccess = (data: any) => {
    captchaData.value = data
    hideCaptchaDialog()
  }

  return {
    showCaptcha,
    captchaData,
    checkNeedCaptcha,
    showCaptchaDialog,
    hideCaptchaDialog,
    resetCaptchaData,
    handleCaptchaSuccess,
  }
}
