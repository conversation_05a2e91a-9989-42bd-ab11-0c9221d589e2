.ex-el-button-gray {
  border-color: #dadde8 !important;
  cursor: pointer;
  span {
    color: #333;
  }
  &:hover {
    span {
      color: #fff;
    }
  }
  .el-icon {
    color: #4c5cec;
  }
  &:hover {
    .el-icon {
      color: #fff;
    }
  }
}

.ex-el-button-img-hover-white {
  &:hover {
    .icon {
      filter: brightness(0) invert(1);
    }
  }
}

// 有效 但是怕影响范围太大，估计这么写也没问题
// .el-table {
//   tr th{
//     background: #F7F8FA!important;
//   }
//   .el-table__body{
//     background: #F7F8FA;
//   }
//   .el-table__row{
//     background: #F7F8FA;
//   }
// }
