import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store/modules/user'

export interface ImageData {
  url: string
  fileName: string
  ossId: string
}

export function useImageUpload() {
  const userStore = useUserStore()
  const uploadedImages = ref<ImageData[]>([])
  const isUploading = ref(false)

  /**
   * 处理图片上传
   * @param files 文件列表
   */
  async function handleImageUpload(files: FileList | File[]) {
    if (!files || files.length === 0) return

    const fileArray = Array.from(files)
    const maxSize = 20 * 1024 * 1024 // 20MB
    const allowedTypes = ['image/bmp', 'image/gif', 'image/jpg', 'image/jpeg', 'image/png']
    const validFiles: File[] = []

    // 验证文件
    for (const file of fileArray) {
      // 验证文件类型
      if (!allowedTypes.includes(file.type.toLowerCase())) {
        ElMessage.error(`${file.name} 格式不支持，请选择 BMP、GIF、JPG、JPEG、PNG 格式的图片`)
        continue
      }

      // 验证文件大小
      if (file.size > maxSize) {
        ElMessage.error(`${file.name} 大小超过20MB限制`)
        continue
      }

      validFiles.push(file)
    }

    if (validFiles.length === 0) return

    isUploading.value = true

    try {
      const uploadPromises = validFiles.map(file => uploadSingleImage(file))
      const results = await Promise.allSettled(uploadPromises)

      let successCount = 0
      let failCount = 0

      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          uploadedImages.value.push(result.value)
          successCount++
        } else {
          console.error(`上传 ${validFiles[index].name} 失败:`, result.reason)
          failCount++
        }
      })

      if (successCount > 0) {
        ElMessage.success(
          `成功上传 ${successCount} 张图片${failCount > 0 ? `，${failCount} 张失败` : ''}`,
        )
      }
      if (failCount > 0 && successCount === 0) {
        ElMessage.error('图片上传失败，请重试')
      }
    } catch (error) {
      console.error('批量上传失败:', error)
      ElMessage.error('图片上传失败，请重试')
    } finally {
      isUploading.value = false
    }
  }

  /**
   * 上传单个图片
   */
  async function uploadSingleImage(file: File): Promise<ImageData> {
    const formData = new FormData()
    formData.append('file', file)

    const baseUrl = (import.meta.env as any).VITE_APP_BASE_API
    const token = userStore.token

    const response = await fetch(`${baseUrl}/resource/oss/upload`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
        Clientid: (import.meta.env as any).VITE_APP_CLIENT_ID,
      },
      body: formData,
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`)
    }

    const result = await response.json()

    if (result.code === 200) {
      return {
        url: result.data.url,
        fileName: result.data.fileName,
        ossId: result.data.ossId,
      }
    } else {
      throw new Error(result.msg || '上传失败')
    }
  }

  /**
   * 打开文件选择器
   * @param multiple 是否支持多选
   */
  function openFileSelector(multiple = true) {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.bmp,.gif,.jpg,.jpeg,.png'
    input.multiple = multiple

    input.onchange = async event => {
      const files = (event.target as HTMLInputElement).files
      if (files) {
        await handleImageUpload(files)
      }
    }

    input.click()
  }

  /**
   * 移除图片
   */
  function removeImage(index: number) {
    uploadedImages.value.splice(index, 1)
  }

  /**
   * 清空所有图片
   */
  function clearImages() {
    uploadedImages.value = []
  }

  return {
    uploadedImages,
    isUploading,
    handleImageUpload,
    openFileSelector,
    removeImage,
    clearImages,
  }
}
