import { createApp } from 'vue'
import App from './App.vue'
import { setupI18n } from './locales'
import { setupAssets, setupScrollbarStyle } from './plugins'
import ElementIcons from './plugins/svgicon'
import { setupStore } from './store'
import { setupRouter } from './router'
import { buildTimeChecker } from './utils/build-time-checker'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'
import './styles/iconfont.css'

async function bootstrap() {
  const app = createApp(App)
  setupAssets()

  setupScrollbarStyle()

  setupStore(app)

  setupI18n(app)

  await setupRouter(app)

  app.use(ElementIcons)

  // 初始化构建时间检测器
  void buildTimeChecker

  app.mount('#app')
}

bootstrap()
