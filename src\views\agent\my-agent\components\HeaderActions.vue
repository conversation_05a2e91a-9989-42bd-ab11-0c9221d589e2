<script setup lang="ts">
// 定义事件
import { h, ref, watch } from 'vue'
import { ElButton } from 'element-plus'
import HistoryPopover from './HistoryPopover.vue'

const props = defineProps<{
  onBack: () => void
  onHistory: () => void
  onDropdown: (key: string) => void
  isReview?: boolean
  onAudit?: () => void
  agentId?: string
  isPrivate?: boolean
  status?: number
}>()

const emit = defineEmits<{
  (e: 'back'): void
  (e: 'history'): void
  (e: 'dropdown', key: string): void
  (e: 'restore', versionId: string | number): void
  (e: 'saveWithPrivate', isPrivate: boolean): void
}>()

const selectedAgent = ref(props.isPrivate ? 'private' : 'public')
const showHistory = ref(false)

// 监听props.isPrivate的变化，更新selectedAgent
watch(
  () => props.isPrivate,
  newValue => {
    selectedAgent.value = newValue ? 'private' : 'public'
  },
)

// 事件处理函数
const handleBack = () => emit('back')
const handleSaveWithPrivate = () => {
  // 根据当前选择的助手类型传递isPrivate值
  emit('saveWithPrivate', selectedAgent.value === 'private')
}
const handleHistory = () => {
  console.log('打开历史记录，当前agentId:', props.agentId)
  showHistory.value = true
}
const handleRestore = (versionId: string | number) => {
  emit('restore', versionId)
  showHistory.value = false
}
const handleDropdown = (key: string) => emit('dropdown', key)
</script>

<template>
  <div class="flex items-center space-x-4">
    <!-- 历史记录按钮 -->
    <HistoryPopover v-model:visible="showHistory" :agent-id="agentId" @restore="handleRestore">
      <button class="history-btn" :class="{ active: showHistory }" @click="handleHistory">
        <img
          src="@/assets/agent/history.svg"
          alt="history"
          class="w-5 h-5 history-icon"
          :class="{ active: showHistory }"
        />
      </button>
    </HistoryPopover>

    <!-- 下拉菜单 -->
    <!-- resolved handleSave时候传递isPrivate给表单，查看时候要能回显 -->
    <div v-if="!isReview" class="relative">
      <el-select
        v-model="selectedAgent"
        placeholder="请选择助手"
        class="!w-[120px] !h-[40px]"
        @change="handleDropdown"
      >
        <el-option label="私人助手" value="private" />
        <el-option label="企业助手" value="public" />
      </el-select>
    </div>

    <!-- 根据isReview和status显示不同的按钮，只有当status真正加载且等于0时显示审核按钮 -->
    <template v-if="isReview && status === 0">
      <ElButton class="el-button el-button--primary !px-4" @click="onAudit">审核</ElButton>
    </template>
    <template v-else-if="!isReview">
      <ElButton class="el-button el-button--primary !px-4" @click="handleSaveWithPrivate">{{
        selectedAgent === 'private' ? '保存' : '发布'
      }}</ElButton>
    </template>

    <!-- 返回按钮 -->
    <ElButton class="!px-4" type="primary" plain @click="handleBack">返回</ElButton>
  </div>
</template>

<style scoped>
.history-btn {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  border: 1px solid #dadde8;
  display: flex;
  align-items: center;
  justify-content: center;
}
.history-btn.active {
  background: #dadde8;
}

.history-icon {
  filter: brightness(0) saturate(100%);
  transition: filter 0.3s ease;
}

.history-icon.active {
  filter: brightness(0) saturate(100%) invert(37%) sepia(74%) saturate(1045%) hue-rotate(212deg)
    brightness(91%) contrast(107%);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}
</style>
