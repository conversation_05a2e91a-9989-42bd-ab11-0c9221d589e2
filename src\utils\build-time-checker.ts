/**
 * 构建时间检测工具
 * 基于构建时间戳检测应用是否有更新
 */

import { ElNotification } from 'element-plus'

const BUILD_TIME_KEY = 'app_build_time'
const CHECK_INTERVAL = 5 * 60 * 1000 // 每5分钟检查一次服务器的构建时间
const PAUSE_DURATION = 30 * 60 * 1000 // 用户关闭提醒后暂停30分钟
const TIME_TOLERANCE = 5 * 1000 // 容忍5秒的时间差
const NOTIFICATION_COOLDOWN = 5 * 60 * 1000 // 通知冷却时间：5分钟内不重复展示

export class BuildTimeChecker {
  private currentBuildTime: string
  private checkTimer: NodeJS.Timeout | null = null
  private isChecking = false
  private lastNotificationTime = 0 // 上次显示通知的时间
  private activeNotification: any = null // 当前活跃的通知实例
  private hasUpdateDetected = false // 标记是否已检测到更新
  private beforeUnloadHandler: (() => void) | null = null // beforeunload事件处理器

  constructor() {
    // 获取构建时的时间戳（通过Vite在构建时注入）
    this.currentBuildTime = import.meta.env.VITE_BUILD_TIME || ''

    // 开发环境下不启用构建时间检测
    if (!import.meta.env.DEV) {
      this.init()
      this.setupBeforeUnloadHandler()
    }
  }

  private init() {
    const savedBuildTime = localStorage.getItem(BUILD_TIME_KEY)

    if (
      savedBuildTime &&
      !this.isSameBuildTime(savedBuildTime, this.currentBuildTime) &&
      this.currentBuildTime
    ) {
      // 检测到构建时间更新
      this.hasUpdateDetected = true
      this.showUpdateNotification()
    } else {
      // 只有在没有检测到更新时才立即保存当前构建时间
      // 如果检测到更新，等用户操作后再更新localStorage
      if (this.currentBuildTime) {
        localStorage.setItem(BUILD_TIME_KEY, this.currentBuildTime)
      }
    }

    // 开始定期检查
    this.startPeriodicCheck()
  }

  /**
   * 设置页面卸载前的处理器
   * 当用户手动刷新或离开页面时，如果已检测到更新，更新localStorage
   */
  private setupBeforeUnloadHandler() {
    this.beforeUnloadHandler = () => {
      // 如果已检测到更新且当前构建时间存在，更新localStorage
      if (this.hasUpdateDetected && this.currentBuildTime) {
        localStorage.setItem(BUILD_TIME_KEY, this.currentBuildTime)
        console.log('页面刷新前已更新localStorage中的构建时间')
      }
    }
    window.addEventListener('beforeunload', this.beforeUnloadHandler)
  }

  /**
   * 比较两个构建时间戳是否为同一次构建
   * 容忍5秒内的时间差，避免构建过程中的时间差误判
   */
  private isSameBuildTime(time1: string, time2: string): boolean {
    if (!time1 || !time2) return false

    try {
      const timestamp1 = new Date(time1).getTime()
      const timestamp2 = new Date(time2).getTime()

      // 如果时间差在容忍范围内，认为是同一次构建
      return Math.abs(timestamp1 - timestamp2) <= TIME_TOLERANCE
    } catch (error) {
      console.warn('构建时间比较失败:', error)
      return time1 === time2 // 降级为字符串比较
    }
  }

  /**
   * 检查是否可以显示通知
   * 防止短时间内重复显示通知
   */
  private canShowNotification(): boolean {
    const now = Date.now()
    return now - this.lastNotificationTime >= NOTIFICATION_COOLDOWN
  }

  private showUpdateNotification() {
    // 检查通知冷却时间
    if (!this.canShowNotification()) {
      console.log('通知冷却中，跳过显示（5分钟内已显示过更新提醒）')
      return
    }

    // 检查是否已有未关闭的通知
    if (this.activeNotification) {
      console.log('已有更新提醒显示中，跳过新通知')
      return
    }

    // 添加自定义样式到页面
    this.injectNotificationStyles()

    // 创建并显示通知
    this.activeNotification = ElNotification({
      title: '应用更新提醒',
      message: '检测到应用已更新，点击此处刷新页面获得最佳体验',
      type: 'info',
      duration: 0, // 不自动关闭
      showClose: true,
      position: 'bottom-right', // 位于右下角
      customClass: 'build-update-notification',
      onClick: () => {
        // 点击通知刷新页面前，先更新localStorage中的构建时间，防止刷新后再次弹出通知
        if (this.currentBuildTime) {
          localStorage.setItem(BUILD_TIME_KEY, this.currentBuildTime)
        }
        this.activeNotification = null // 清除引用
        window.location.reload()
      },
      onClose: () => {
        // 用户关闭通知，停止检查一段时间
        this.activeNotification = null // 清除引用
        this.hasUpdateDetected = false // 重置更新检测标志，允许后续重新检测
        this.stopPeriodicCheck()
        setTimeout(() => this.startPeriodicCheck(), PAUSE_DURATION) // 30分钟后再开始检查
        console.log('用户关闭更新提醒，30分钟后再次检查')
      },
    })

    // 记录本次通知时间
    this.lastNotificationTime = Date.now()
    console.log('已显示构建更新提醒')
  }

  /**
   * 注入通知的自定义样式
   */
  private injectNotificationStyles() {
    // 避免重复注入样式
    if (document.getElementById('build-update-notification-styles')) {
      return
    }

    const style = document.createElement('style')
    style.id = 'build-update-notification-styles'
    style.textContent = `
      .build-update-notification {
        cursor: pointer !important;
        transition: all 0.3s ease !important;
      }
      
      .build-update-notification:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
      }
      
      .build-update-notification .el-notification__content {
        cursor: pointer !important;
      }
      
      .build-update-notification .el-notification__title {
        cursor: pointer !important;
      }
      
      .build-update-notification .el-notification__content p {
        cursor: pointer !important;
        margin: 0 !important;
      }
    `
    document.head.appendChild(style)
  }

  private async checkBuildTime(): Promise<boolean> {
    if (this.isChecking) return false

    try {
      this.isChecking = true

      // 请求build-info.json获取服务器的构建时间
      const response = await fetch(`/build-info.json?t=${Date.now()}`, {
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache',
        },
      })

      if (response.ok) {
        const data = await response.json()
        if (data.buildTime && !this.isSameBuildTime(data.buildTime, this.currentBuildTime)) {
          console.log('检测到新的构建版本:', {
            current: this.currentBuildTime,
            server: data.buildTime,
            timeDiff: `${Math.abs(new Date(data.buildTime).getTime() - new Date(this.currentBuildTime).getTime())}ms`,
          })
          // 更新当前构建时间为服务器的构建时间
          this.currentBuildTime = data.buildTime
          // 标记已检测到更新
          this.hasUpdateDetected = true
          this.showUpdateNotification()
          // 检测到更新后停止定期检查，避免重复请求
          this.stopPeriodicCheck()
          return true
        }
      }
    } catch (error) {
      console.warn('构建时间检查失败:', error)
    } finally {
      this.isChecking = false
    }

    return false
  }

  private startPeriodicCheck() {
    if (this.checkTimer || import.meta.env.DEV) return // 开发环境不启用定期检查

    this.checkTimer = setInterval(() => {
      this.checkBuildTime()
    }, CHECK_INTERVAL)

    console.log('已启动构建时间检查，每5分钟检查一次')
  }

  private stopPeriodicCheck() {
    if (this.checkTimer) {
      clearInterval(this.checkTimer)
      this.checkTimer = null
      console.log('已停止构建时间检查')
    }
  }

  public forceCheck() {
    // 开发环境下不执行检测
    if (import.meta.env.DEV) {
      return Promise.resolve(false)
    }
    return this.checkBuildTime()
  }

  public forceRefresh() {
    window.location.reload()
  }

  /**
   * 检查是否已检测到更新
   */
  public isUpdateDetected(): boolean {
    return this.hasUpdateDetected
  }

  /**
   * 检查是否有活跃的通知
   */
  public isNotificationActive(): boolean {
    return this.activeNotification !== null
  }

  public destroy() {
    this.stopPeriodicCheck()
    // 清理活跃通知
    if (this.activeNotification) {
      this.activeNotification.close()
      this.activeNotification = null
    }
    // 移除beforeunload事件监听器
    if (this.beforeUnloadHandler) {
      window.removeEventListener('beforeunload', this.beforeUnloadHandler)
      this.beforeUnloadHandler = null
    }
  }
}

// 创建全局实例
export const buildTimeChecker = new BuildTimeChecker()

// 开发环境测试功能 - 生产环境构建时会自动移除
if (import.meta.env.DEV) {
  // 模拟构建时间更新的测试函数（仅开发环境）
  ;(window as any).testBuildTimeUpdate = () => {
    console.log('🧪 [DEV ONLY] 模拟构建时间更新测试')
    // 使用全局实例而不是创建新实例，这样防重复机制才能生效
    const checker = buildTimeChecker
    // 直接触发更新提醒（开发环境下绕过构建时间检测）
    ;(checker as any).showUpdateNotification()
  }

  console.log('🚀 [DEV ONLY] 可执行 testBuildTimeUpdate() 测试构建时间检测')
  console.log('🚀 [DEV ONLY] 或使用快捷键 Ctrl+Shift+V 测试')
}
