/// <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

interface ImportMetaEnv {
  readonly VITE_APP_BASE_API: string
  readonly VITE_APP_BASE_API_PROXY: string
  readonly VITE_GLOB_OPEN_LONG_REPLY: string
  readonly VITE_GLOB_APP_PWA: string
  readonly VITE_APP_CLIENT_ID: string
}
