<script setup lang="ts">
import type { Ref } from 'vue'
import { computed, h, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { NAutoComplete, NButton, NInput, NSelect } from 'naive-ui'
import { ElMessage, ElTooltip } from 'element-plus'
import { Message, NicknameUpdateDialog, ShareDialog, Skeleton } from './components'
import { useScroll } from './hooks/useScroll'
import { useChat } from './hooks/useChat'
import { useUsingContext } from './hooks/useUsingContext'
import { useModelManagement } from './hooks/useModelManagement'
import { useConversationDetail } from './hooks/useConversationDetail'
import { useChatRecords } from './hooks/useChatRecords'
import { useConversationActions } from './hooks/useConversationActions'
import { useImageUpload } from './hooks/useImageUpload'
import MobileHeaderComponent from './components/MobileHeader/index.vue'
import SettingSider from './components/SettingSider/index.vue'
import ModelSelector from './components/ModelSelector/index.vue'
import { usePublicChat } from './hooks/usePublicChat'
import Settings from '@/components/common/SettingsDialog/index.vue'
import { HoverButton, SvgIcon } from '@/components/common'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { useAgentUserStore, useChatStore, usePromptStore, useUserStore } from '@/store'
import { t } from '@/locales'
import ms from '@/utils/message'
import { useAppStore } from '@/store/modules/app'
import { setRequestAgentContext } from '@/utils/token-manager'
// resolved 进入该页面时候，如果用户nickname === phonenumber，则提示用户修改昵称
const route = useRoute()
const { isPublicChat } = usePublicChat()

// 从路由获取会话ID和助手ID
const { conversationId } = route.params as { conversationId: string }
const releaseId = computed(() => route.query.releaseId as string)

// releaseId的处理通过watch来完成，确保响应式更新

// 基础状态和布局
const { isMobile } = useBasicLayout()
const chatStore = useChatStore()
const promptStore = usePromptStore()
const userStore = useUserStore()
const appStore = useAppStore()

// 用户昵称提示相关状态
const showNicknameDialog = ref(false)
const currentNickname = ref('')

// 使用钩子函数
const {
  scrollRef,
  scrollToBottom,
  scrollToBottomIfAtBottom,
  isUserNearBottom,
  maintainScrollPosition,
} = useScroll()
const { usingContext } = useUsingContext()
const { updateChatSome } = useChat()
const { modelList, currentModel, fetchModelList, handleModelChange } =
  useModelManagement(conversationId)
const { conversationDetail, kbList, fetchConversationDetail } = useConversationDetail()
const {
  latestChatId,
  hasMoreChatRecords,
  isLoadingMoreRecords,
  initialLoadComplete,
  fetchChatRecord,
  loadMoreChatRecords,
  getSummary,
} = useChatRecords(conversationId, scrollToBottom, maintainScrollPosition)
const {
  handleEdit,
  handleDelete,
  handleClear,
  onConversation,
  onRetry,
  loading,
  controller,
  processStreamResponse,
  handleStop,
  setController,
  restoreConversationState,
} = useConversationActions(conversationId, scrollToBottomIfAtBottom, scrollToBottom)

// 图片上传相关
const { uploadedImages, isUploading, openFileSelector, removeImage, clearImages } = useImageUpload()

// UI状态变量
const prompt = ref<string>('')
const inputRef = ref<Ref | null>(null)
const showSettings = ref(false)
const showShareDialog = ref(false)
const settingSiderCollapsed = ref(false)
const showMobileSettingSider = ref(false)
const inputAreaHeight = ref(150) // 默认高度
const isDragging = ref(false)
const startY = ref(0)
const startHeight = ref(0)
const showNewContext = ref(false) // 显示"新的上下文"提示
const isWidthLimited = computed(() => appStore.isWidthLimited)

// 图片上传相关状态已移至 useImageUpload hook

// 计算当前模型是否支持视觉
const currentModelSupportsVision = computed(() => {
  const selectedModel = modelList.value.find(model => model.value === currentModel.value)
  return selectedModel?.capabilities?.includes('vision') || false
})

// 数据源和计算属性
const dataSources = computed(() => {
  const data = chatStore.getChatByConversationId(conversationId)
  // 过滤掉undefined或不完整的项目
  return data.filter(item => item && typeof item === 'object' && 'dateTime' in item)
})
const { promptList: promptTemplate } = promptStore as any

// 计算属性
const lastQuestionFromHistory = computed(() => {
  const currentHistory = chatStore.getChatHistoryByCurrentActive
  return currentHistory?.lastQuestion
})

// 检查是否没有消息
const hasNoLastQuestion = computed(() => {
  // 优先从history中获取lastQuestion
  const hasLastQuestion = lastQuestionFromHistory.value || conversationDetail.value?.lastQuestion
  // 检查dataSources中是否有消息，如果有至少一条消息，则不显示空白界面
  return !hasLastQuestion && dataSources.value.length === 0
})

// 获取当前聊天标题
const currentChatTitle = computed(() => {
  const current = chatStore.history.find(item => item.conversationId === conversationId)
  return current?.title || t('chat.newChatTitle')
})

// 获取系统消息
// resolved 创建新对话时候，目前的逻辑获取不到这个新对话的systemMessage，为什么
const currentSystemMessage = computed(() => {
  // 首先从history中获取，因为那里包含了从服务器获取的systemMessage
  const currentHistory = chatStore.getChatHistoryByCurrentActive
  if (currentHistory?.systemMessage) return currentHistory.systemMessage
  return ''
})

// 搜索选项
const searchOptions = computed(() => {
  if (prompt.value.startsWith('/')) {
    return promptTemplate
      .filter((item: { key: string }) =>
        item.key.toLowerCase().includes(prompt.value.substring(1).toLowerCase()),
      )
      .map((obj: { value: any }) => {
        return {
          label: obj.value,
          value: obj.value,
        }
      })
  } else {
    return []
  }
})

// 输入框提示文本
const placeholder = computed(() => {
  if (isMobile.value) return t('chat.placeholderMobile')
  return t('chat.placeholder')
})

// 按钮是否禁用
const buttonDisabled = computed(() => {
  return loading.value || !prompt.value || prompt.value.trim() === ''
})

// 底部样式类
const footerClass = computed(() => {
  let classes = ['footer-container']
  if (isMobile.value) {
    classes = ['sticky', 'left-0', 'bottom-0', 'right-0', 'p-2', 'pr-3', 'overflow-hidden']
  }
  return classes
})

// 计算模型配置，避免每次渲染都创建新对象
const modelConfig = computed(() => ({
  temperature: conversationDetail.value?.temperature,
  maxContextCount: conversationDetail.value?.maxContextCount,
  singleReplyLimitFlag: conversationDetail.value?.singleReplyLimitFlag,
  maxTokens: conversationDetail.value?.maxTokens,
}))

// UI交互函数
function handleSubmit() {
  // 获取当前的图片列表
  const currentImages = uploadedImages.value.length > 0 ? [...uploadedImages.value] : undefined

  onConversation(
    loading,
    prompt,
    currentModel.value,
    latestChatId,
    showNewContext,
    (response: Response, index: number, message: string) =>
      processStreamResponse(response, index, message, currentModel.value, getSummary, latestChatId),
    currentImages,
  )

  // 发送后清空图片列表
  clearImages()
}

// 函数定义
function handleRetry(index: number) {
  onRetry(
    index,
    loading,
    currentModel.value,
    latestChatId,
    (response: Response, index: number, message: string) =>
      processStreamResponse(response, index, message, currentModel.value, getSummary, latestChatId),
  )
}

function handleEnter(event: KeyboardEvent) {
  if (!isMobile.value) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault()
      handleSubmit()
    }
  } else {
    if (event.key === 'Enter') {
      event.preventDefault()
      handleSubmit()
    }
  }
}

function handleImage() {
  // 检查当前模型是否支持视觉
  if (!currentModelSupportsVision.value) {
    return // 如果不支持视觉，不执行任何操作（按钮应该是禁用状态）
  }

  // 使用 hook 中的方法，支持多图上传
  openFileSelector(true)
}

// 处理图片加载完成事件
function handleImageLoad() {
  // 图片加载完成后，如果用户在底部附近，重新滚动到底部
  if (isUserNearBottom.value) {
    scrollToBottom()
  }
}

// 拖动相关
function handleDragStart(e: MouseEvent) {
  isDragging.value = true
  startY.value = e.clientY
  startHeight.value = inputAreaHeight.value
  // console.log('startY.value', startY.value)
  // console.log('startHeight.value', startHeight.value)

  // 添加事件监听器
  document.addEventListener('mousemove', handleDragMove)
  document.addEventListener('mouseup', handleDragEnd)
}

function handleDragMove(e: MouseEvent) {
  if (!isDragging.value) return

  const deltaY = startY.value - e.clientY
  // console.log('deltaY', deltaY)
  // console.log('startHeight.value + deltaY', startHeight.value + deltaY)

  const newHeight = Math.max(150, Math.min(400, startHeight.value + deltaY))
  inputAreaHeight.value = newHeight
}

function handleDragEnd() {
  isDragging.value = false
  document.removeEventListener('mousemove', handleDragMove)
  document.removeEventListener('mouseup', handleDragEnd)
}

function toggleMobileSettingSider() {
  showMobileSettingSider.value = !showMobileSettingSider.value
}

// renderOption辅助函数
const renderOption = (option: { label: string }) => {
  for (const i of promptTemplate) {
    if (i.value === option.label) return [i.key]
  }
  return []
}

// 检查用户昵称是否等于手机号
function checkNicknameEqualPhoneNumber() {
  const userInfo = userStore.userInfo
  if (userInfo && userInfo.nickName && userInfo.phonenumber) {
    if (userInfo.sameNickNameWithPhonenumber) {
      showNicknameDialog.value = true
      currentNickname.value = userInfo.nickName
    }
  }
}

// 处理昵称更新完成
function handleNicknameUpdated() {
  // 可以在这里添加额外的处理逻辑
}

// 处理聊天宽度限制切换
function toggleChatWidth() {
  // resolved 存到appstore里
  appStore.setIsWidthLimited(!appStore.isWidthLimited)
}

// 处理分享弹窗
function handleShare() {
  showShareDialog.value = true
}

// 自动调整输入区域高度
// todo 实际使用效果中，输入框高度可以等我再多输入些时候才开始变动，现在我输入三行就变动了
function adjustInputAreaHeight() {
  if (!inputRef.value || isMobile.value) return

  nextTick(() => {
    const textareaElement = inputRef.value?.$el?.querySelector('.n-input__textarea')
    if (!textareaElement) return

    const textareaHeight = textareaElement.scrollHeight
    // 下方俩数值并不是按实际拿的，魔改的，但结果函数效果符合要求，就这样吧
    const toolbarHeight = 30 // 工具栏和按钮区域的大概高度
    const padding = 20 // 一些内边距
    const calculatedHeight = Math.max(150, Math.min(400, textareaHeight + toolbarHeight + padding))
    // console.log('calculatedHeight', calculatedHeight)
    // console.log('inputAreaHeight.value', inputAreaHeight.value)
    // console.log(
    //   'Math.abs(calculatedHeight - inputAreaHeight.value)',
    //   Math.abs(calculatedHeight - inputAreaHeight.value),
    // )

    // 只有当计算出的高度与当前高度差异较大时才调整
    if (Math.abs(calculatedHeight - inputAreaHeight.value) > 10) {
      inputAreaHeight.value = calculatedHeight
    }
  })
}

// 生命周期钩子
onMounted(async () => {
  if (!conversationId) return
  try {
    await fetchConversationDetail(conversationId) // 先获取会话详情
    // resolved 如何确保getBizConversationList执行完之后才执行await fetchChatRecord(false, currentModel.value)
    // 确保chatStore中有当前会话数据
    if (!chatStore.getChatHistoryByCurrentActive) {
      // 等待数据加载完成
      await new Promise<void>(resolve => {
        // 设置一个最长等待时间
        const timeout = setTimeout(() => resolve(), 3000)

        // 直接使用setInterval检查
        const interval = setInterval(() => {
          if (chatStore.getChatHistoryByCurrentActive) {
            clearInterval(interval)
            clearTimeout(timeout)
            resolve()
          }
        }, 100)
      })
    }

    // 恢复会话状态（如果当前会话正在进行流式响应）
    const restoredState = restoreConversationState()

    // resolved 当这个会话处于流式响应map之中，就不该触发fetchChatRecord，这样会覆盖流式响应的内容
    if (!restoredState.isLoading) {
      await fetchChatRecord(false, currentModel.value) // 再获取聊天记录 此处需要用到ConversationDetail里的modelId
    } else {
      console.log(`会话 ${conversationId} 正在流式响应中，跳过fetchChatRecord`)
      initialLoadComplete.value = true
    }

    await fetchModelList(conversationDetail.value) // 获取模型列表
    if (inputRef.value && !isMobile.value) inputRef.value?.focus()

    // 初始化显示"新的上下文"提示为false
    showNewContext.value = false

    // 为每个请求创建新的controller
    setController(new AbortController())

    // 未知原因刷新页面，loading 状态不会重置，手动重置
    dataSources.value.forEach((item, index) => {
      if (item.loading) updateChatSome(conversationId, index, { loading: false })
    })

    // 检查用户昵称是否等于手机号
    checkNicknameEqualPhoneNumber()

    // 初始化调整输入区域高度
    // adjustInputAreaHeight()
  } catch (error) {
    console.error('初始化聊天界面失败:', error)
  }
})

onUnmounted(() => {
  // 不在组件卸载时自动停止会话，让会话能够在后台持续运行
  // 只清理DOM事件监听器
  document.removeEventListener('mousemove', handleDragMove)
  document.removeEventListener('mouseup', handleDragEnd)
})

// 监听releaseId变化，重新设置全局上下文
watch(
  releaseId,
  newReleaseId => {
    if (isPublicChat && newReleaseId) {
      setRequestAgentContext(newReleaseId)
      console.log('Chat页面 - releaseId变化:', newReleaseId)
    } else if (!newReleaseId) {
      // 如果releaseId为空，清除助手上下文，使用默认用户Store
      setRequestAgentContext(null)
      console.log('Chat页面 - 清除releaseId上下文')
    }
  },
  { immediate: true },
)

// 监听 prompt 变化，自动调整输入区域高度
watch(
  prompt,
  () => {
    if (!isMobile.value) {
      adjustInputAreaHeight()
    }
  },
  { flush: 'post' },
)

// 监听 dataSources 变化，自动滚动（特别是在后台流式响应时）
watch(
  dataSources,
  (newData, oldData) => {
    // 如果当前会话正在loading，并且数据有更新，自动滚动
    if (loading.value && newData.length > 0) {
      const lastMessage = newData[newData.length - 1]
      if (lastMessage && lastMessage.loading) {
        // 使用nextTick确保DOM更新后再滚动
        nextTick(() => {
          scrollToBottomIfAtBottom()
        })
      }
    }
  },
  { deep: true, flush: 'post' },
)
</script>

<template>
  <div class="flex flex-col h-full bg-[#F7F8FA]">
    <header
      v-if="!isMobile"
      class="flex justify-between items-center p-4 border-b dark:border-neutral-800"
    >
      <div class="flex items-center gap-2">
        <img src="@/assets/logo-gray.png" alt="" class="w-[48px] h-[48px]" />
        <h2 class="text-lg dark:text-white font-bold" style="font-size: 20px">
          {{ currentChatTitle }}
        </h2>
      </div>
      <div class="flex gap-2">
        <!-- resolved 增加一个按钮，点击能够让chat主体内容宽度限制为960px -->
        <HoverButton @click="toggleChatWidth">
          <span class="text-xl text-[#4f555e] dark:text-white">
            <SvgIcon
              style="font-size: 24px"
              :icon="
                isWidthLimited ? 'mdi:arrow-expand-horizontal' : 'mdi:arrow-collapse-horizontal'
              "
            />
          </span>
        </HoverButton>
        <!-- resolved 点击弹出一个分享弹窗el-dialog，默认生成一个图片，图片内容为当前聊天记录，图片下方有下载按钮 -->
        <!-- resolved 点击这个按钮时候，先出现复选框能让用户勾选需要分享的聊天记录，再对应生成图片 -->
        <HoverButton @click="handleShare">
          <span class="text-xl text-[#4f555e] dark:text-white">
            <SvgIcon icon="material-symbols:share-outline" />
          </span>
        </HoverButton>
      </div>
    </header>

    <div class="flex flex-1 overflow-hidden">
      <div
        class="flex-1 flex flex-col overflow-hidden"
        :class="{ 'pr-[360px]': !settingSiderCollapsed && !isMobile }"
      >
        <Settings v-model:show="showSettings" />
        <MobileHeaderComponent
          v-if="isMobile"
          :using-context="usingContext"
          :show-right-sider="showMobileSettingSider"
          @handle-image="handleImage"
          @handle-clear="
            () => {
              const result = handleClear(showNewContext, latestChatId)
              if (result?.then) {
                // todo 参数“res”隐式具有“any”类型。
                result.then(res => {
                  if (res) {
                    showNewContext = res.showNewContext
                    latestChatId = res.latestChatId
                  }
                })
              }
            }
          "
          @toggle-right-sider="toggleMobileSettingSider"
        />
        <main class="flex-1 overflow-hidden relative">
          <div id="scrollRef" ref="scrollRef" class="absolute inset-0 overflow-y-auto">
            <div
              id="image-wrapper"
              class="w-full m-auto dark:bg-[#101014] bg-[#F7F8FA]"
              :class="[
                isMobile ? 'p-2 max-w-screen-xl' : 'p-4',
                { 'max-w-[960px]': isWidthLimited && !isMobile },
              ]"
            >
              <div
                class="relative"
                :class="{ 'max-w-[960px] mx-auto': isWidthLimited && !isMobile }"
              >
                <!-- 无数据状态 -->
                <div
                  v-if="hasNoLastQuestion"
                  class="flex items-center justify-center mt-4 text-center text-neutral-300"
                >
                  <SvgIcon icon="ri:bubble-chart-fill" class="mr-2 text-3xl" />
                  <span>{{ t('chat.newChatTitle') }}</span>
                </div>
                <!-- 添加数据加载过渡状态 -->
                <template v-else-if="!initialLoadComplete">
                  <div class="space-y-8">
                    <Skeleton v-for="i in 2" :key="i" />
                  </div>
                </template>
                <!-- 数据加载完成状态 -->
                <template v-else>
                  <!-- 加载更多按钮 -->
                  <div v-if="hasMoreChatRecords" class="flex justify-center py-4">
                    <NButton
                      style="color: #999"
                      size="small"
                      :loading="isLoadingMoreRecords"
                      @click="loadMoreChatRecords(currentModel)"
                    >
                      {{ isLoadingMoreRecords ? '加载中...' : '加载更多历史消息' }}
                    </NButton>
                  </div>
                  <div class="chat-messages-container">
                    <!-- resolved src\views\chat\hooks\useChatRecords.ts里获取记录时item自带chatContentRecordList，src\views\chat\hooks\useConversationActions.ts里进行对话时，item不自带chatContentRecordList，需要用conversationOptions.id去请求getBizChatContentRecordList返回chatContentRecordList -->
                    <!-- todo onRetry不要写到template里，写script里 -->
                    <Message
                      v-for="(item, index) of dataSources"
                      :key="index"
                      :latest-chat-id="latestChatId"
                      :date-time="item?.dateTime || ''"
                      :text="item?.text || ''"
                      :inversion="item?.inversion || false"
                      :error="item?.error || false"
                      :loading="item?.loading || false"
                      :can-retry="!item?.inversion && !loading"
                      :index="index"
                      :previous-chat-id="
                        index > 0 ? dataSources[index - 1]?.conversationOptions?.chatId : undefined
                      "
                      :current-chat-id="item?.conversationOptions?.chatId"
                      :tool-execution-list="item?.toolExecutionList"
                      :chat-content-record-list="item?.chatContentRecordList"
                      :reasoning-content="item?.reasoningContent"
                      :image-info-list="item?.imageInfoList"
                      :on-image-load="handleImageLoad"
                      @retry="() => handleRetry(index)"
                      @delete="handleDelete(index)"
                      @edit="(text: string) => handleEdit(text, index, currentModel)"
                    />
                    <!-- 新上下文提示 -->
                    <div v-if="showNewContext" class="flex items-center justify-center my-4">
                      <div class="w-[100px] border-t border-gray-200 dark:border-gray-600" />
                      <span class="mx-4 text-xs text-gray-400 dark:text-gray-500">新的上下文</span>
                      <div class="w-[100px] border-t border-gray-200 dark:border-gray-600" />
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </div>
          <!-- 停止响应按钮 -->
          <div class="stop-loading-btn">
            <NButton
              v-show="loading"
              style="border-radius: 8px"
              secondary
              type="primary"
              @click="handleStop"
            >
              <template #icon>
                <SvgIcon icon="ri:stop-circle-line" />
              </template>
              <span>停止响应</span>
            </NButton>
          </div>
          <!-- 滚动到底部按钮 -->
          <div v-show="dataSources.length && !isUserNearBottom" class="scroll-to-bottom-btn">
            <NButton secondary circle type="primary" @click="scrollToBottom">
              <template #icon>
                <SvgIcon icon="ri:arrow-down-line" />
              </template>
            </NButton>
          </div>
        </main>
        <footer :class="footerClass">
          <div
            class="drag-handle cursor-row-resize h-1 hover:bg-gray-300 dark:hover:bg-gray-600 rounded-full"
            :class="{ 'border-t': !isWidthLimited }"
            @mousedown="handleDragStart"
          />
          <div
            class="w-full m-auto"
            :class="[
              isMobile ? 'max-w-screen-xl' : '',
              { 'max-w-[960px]': isWidthLimited && !isMobile },
              { 'border p-[12px] rounded-[16px]': isWidthLimited },
            ]"
          >
            <!-- 工具栏 -->
            <div class="flex items-center justify-between dark:border-neutral-800 mb-2">
              <div class="flex items-center">
                <div class="flex items-center space-x-2">
                  <!-- 模型选择器 -->
                  <ModelSelector
                    v-model="currentModel"
                    :disabled="isPublicChat"
                    :model-list="modelList"
                    :loading="!modelList.length"
                    @change="handleModelChange"
                    @update="fetchModelList"
                  />
                  <!-- <NSelect
                    v-model:value="currentModel"
                    :options="modelList"
                    :style="{ width: isMobile ? '250px' : '300px' }"
                    :loading="!modelList.length"
                    :disabled="loading"
                    placeholder="选择模型"
                    :render-label="
                      (option: any) => {
                        return h('div', { class: 'flex items-center' }, [
                          h('img', {
                            src: option.icon || '',
                            class: `model-logo-mini mr-2 ${option.icon ? '' : 'display-none'}`,
                          }),
                          option.label,
                        ])
                      }
                    "
                    @update:value="handleModelChange"
                  /> -->
                </div>
                <!-- 上传图片按钮 -->
                <div class="flex items-center pl-[20px] gap-4">
                  <!-- 支持视觉的模型时不显示 tooltip -->
                  <div
                    v-if="!isMobile && currentModelSupportsVision"
                    class="cursor-pointer"
                    :class="[isUploading ? 'cursor-not-allowed opacity-50' : '']"
                    @click="!isUploading ? handleImage() : undefined"
                  >
                    <span
                      class="text-xl"
                      :class="[
                        !isUploading
                          ? 'text-[#4f555e] dark:text-white hover:text-[#4C5CEC]'
                          : 'text-gray-400 dark:text-gray-600',
                      ]"
                    >
                      <i v-if="!isUploading" class="iconfont icon-tupian iconfont-hover-c"></i>
                      <i v-else class="el-icon-loading"></i>
                    </span>
                  </div>

                  <!-- 不支持视觉的模型时显示 tooltip -->
                  <ElTooltip v-else-if="!isMobile" content="该模型不支持视觉识别" placement="top">
                    <div class="cursor-not-allowed opacity-50">
                      <span class="text-xl text-gray-400 dark:text-gray-600">
                        <i class="iconfont icon-tupian"></i>
                      </span>
                    </div>
                  </ElTooltip>
                  <!-- 清除上下文按钮 -->
                  <!-- resolved latestChatId作为参数传递给handleClear，在handleClear内部把变量handleClear置空，能影响到当前页面的latestChatId吗？ -->
                  <!-- resolved 在template里进行函数传参， handleClear的入参修改为接收普通值而不是ref -->
                  <div
                    v-if="!isMobile"
                    @click="
                      () => {
                        const result = handleClear(showNewContext, latestChatId)
                        if (result?.then) {
                          result.then(res => {
                            if (res) {
                              showNewContext = res.showNewContext
                              latestChatId = res.latestChatId
                            }
                          })
                        }
                      }
                    "
                  >
                    <span class="text-xl text-[#4f555e] dark:text-white">
                      <i class="iconfont iconfont-hover-c icon-qingkong"></i>
                    </span>
                  </div>
                </div>
              </div>
              <div class="flex items-center text-sm text-[#999] ml-2">
                <i class="iconfont icon-token"></i>
                <span class="ml-[3px] prompt-length">{{ prompt.length }}</span>
              </div>
            </div>
            <!-- 已上传图片显示区域 -->
            <div v-if="uploadedImages.length > 0" class="mb-2">
              <div class="flex flex-wrap gap-2">
                <div v-for="(image, index) in uploadedImages" :key="index" class="relative group">
                  <img
                    :src="image.url"
                    :alt="image.fileName"
                    class="w-20 h-20 object-cover rounded-lg border border-gray-200 dark:border-gray-600"
                  />
                  <!-- 白底蓝X的按钮，带阴影 -->
                  <button
                    class="absolute -top-1 -right-1 w-5 h-5 bg-white text-[4c5cec] rounded-full text-sm opacity-0 group-hover:opacity-100 transition-opacity shadow-md hover:shadow-lg border border-gray-200 flex items-center justify-center"
                    @click="removeImage(index)"
                  >
                    ×
                  </button>
                </div>
              </div>
            </div>

            <!-- 输入框区域 -->
            <div
              v-if="!isMobile"
              class="flex flex-col"
              :style="{ height: isMobile ? '150px' : `${inputAreaHeight}px` }"
            >
              <div class="h-full flex flex-col space-x-2">
                <div class="flex-1 bg-[#f9f9f9] dark:bg-[#1f1f1f] rounded-lg">
                  <NAutoComplete
                    v-model:value="prompt"
                    :options="searchOptions"
                    :render-label="renderOption"
                  >
                    <template #default="{ handleInput, handleBlur, handleFocus }">
                      <NInput
                        ref="inputRef"
                        v-model:value="prompt"
                        type="textarea"
                        :placeholder="placeholder"
                        :autosize="{ minRows: 3, maxRows: isMobile ? 3 : 15 }"
                        @input="handleInput"
                        @focus="handleFocus"
                        @blur="handleBlur"
                        @keypress="handleEnter"
                      />
                    </template>
                  </NAutoComplete>
                </div>
                <!-- resolved 当prompt内容过长时候，NInput会扩展，那么是不是应该可以撑大inputAreaHeight呢 -->
                <div class="flex items-center justify-end gap-4">
                  <div style="color: #999">↵ 发送</div>
                  <div style="color: #999">/</div>
                  <div style="color: #999">⇧↵ 换行</div>
                  <NButton
                    v-if="!isMobile"
                    type="primary"
                    size="small"
                    :disabled="buttonDisabled"
                    class="send-button"
                    @click="handleSubmit"
                  >
                    <template #icon>
                      <div class="dark:text-black flex items-center">
                        <i class="iconfont icon-fasong text-lg"></i>
                      </div>
                    </template>
                    <span class="ml-[4px]">发送</span>
                  </NButton>
                </div>
              </div>
            </div>
            <div v-if="isMobile">
              <div class="flex items-center justify-end gap-4">
                <NAutoComplete
                  v-model:value="prompt"
                  :options="searchOptions"
                  :render-label="renderOption"
                >
                  <template #default="{ handleInput, handleBlur, handleFocus }">
                    <!-- todo 圆角更大一点 -->
                    <NInput
                      ref="inputRef"
                      v-model:value="prompt"
                      type="textarea"
                      :placeholder="placeholder"
                      :autosize="{ minRows: 1, maxRows: isMobile ? 5 : 15 }"
                      class="chat-input-mobile"
                      @input="handleInput"
                      @focus="handleFocus"
                      @blur="handleBlur"
                      @keypress="handleEnter"
                    >
                      <template #suffix>
                        <div class="flex items-center justify-end gap-4">
                          <NButton round type="primary" size="small" @click="handleSubmit">
                            <i class="iconfont icon-fasong !text-sm"></i>
                          </NButton>
                        </div>
                      </template>
                    </NInput>
                  </template>
                </NAutoComplete>
              </div>
            </div>
          </div>
        </footer>
      </div>
      <!-- <div>currentSystemMessage-{{ currentSystemMessage }}</div> -->
      <!-- <div>kbList-{{ kbList }}</div> -->
      <div
        v-show="!isMobile || showMobileSettingSider"
        class="absolute right-0 h-full transition-transform duration-300"
        :class="[isMobile ? 'w-full bg-white dark:bg-black z-50' : '']"
      >
        <SettingSider
          v-model:collapsed="settingSiderCollapsed"
          :mobile-mode="isMobile"
          :initial-system-message="currentSystemMessage"
          :initial-kb-list="kbList"
          :initial-model-config="modelConfig"
          @close="showMobileSettingSider = false"
        />
      </div>
    </div>

    <!-- 昵称修改提示对话框 -->
    <NicknameUpdateDialog
      v-model:visible="showNicknameDialog"
      :nickname="currentNickname"
      @updated="handleNicknameUpdated"
    />

    <!-- 分享对话框 -->
    <ShareDialog
      v-model:visible="showShareDialog"
      :conversation-id="conversationId"
      :chat-title="currentChatTitle"
      :model-name="currentModel"
      :model-list="modelList"
      :chat-messages="
        dataSources.map((item, index) => ({
          index,
          text: item.text,
          inversion: item.inversion,
          dateTime: item.dateTime,
        }))
      "
    />
  </div>
</template>

<style scoped lang="less">
:deep(.n-base-selection) {
  border-radius: 8px;
  .n-base-selection__border {
    display: none;
  }
}

// 移动端输入框圆角样式
.chat-input-mobile {
  :deep(.n-input-wrapper) {
    padding-top: 10px !important;
    padding-bottom: 10px !important;
    border-radius: 20px !important;
  }
  :deep(.n-input__border) {
    border-radius: 20px !important;
  }
  :deep(.n-input__state-border) {
    border-radius: 20px !important;
  }
}
.footer-container {
  position: relative; // 添加相对定位
  padding: 16px 16px 16px 16px;
  :deep(.n-input) {
    --n-border: none !important;
    --n-border-hover: none !important;
    --n-border-focus: none !important;
    --n-box-shadow-focus: none !important;
    background-color: transparent !important;
  }
  :deep(.n-input-wrapper) {
    margin-right: -16px !important;
    padding-right: 16px !important;
    padding-left: 0 !important;
  }
  :deep(.n-input__textarea) {
    min-height: 44px !important;
    // padding: 10px 14px !important;
    resize: none;
    font-size: 14px;
    line-height: 1.5;
  }
  :deep(.n-base-selection .n-base-selection-label) {
    background: #e6eaf4 !important;
  }
}
.send-button {
  width: 88px;
  height: 32px;
  background: #4c5cec;
  border-radius: 4px;
}

.send-button .n-button__icon {
  margin-right: 0 !important;
}

.prompt-length {
  font-weight: bold;
  font-size: 14px;
  color: rgba(72, 101, 232, 0.5);
  line-height: 16px;
  text-align: left;
  font-style: normal;
}

.drag-handle {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  transition: background-color 0.2s;
  margin: 0;
  z-index: 1;
}

.footer-container {
  user-select: none; // 防止拖动时选中文本
}

.stop-loading-btn {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 1.5rem;
  z-index: 10;
  cursor: pointer;
  opacity: 0.8;
  transition: opacity 0.3s;

  &:hover {
    opacity: 1;
  }
}

// 添加滚动到底部按钮样式
.scroll-to-bottom-btn {
  position: absolute;
  right: 1.5rem;
  bottom: 1.5rem;
  z-index: 10;
  cursor: pointer;
  opacity: 0.8;
  transition: opacity 0.3s;

  &:hover {
    opacity: 1;
  }
}

// 添加消息容器的淡入效果
.chat-messages-container {
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// 自定义图标样式
.iconfont {
  font-size: 18px; // 默认大小
  &.icon-token {
    font-size: 24px;
    color: rgba(72, 101, 232, 0.5);
  }
  &.icon-fasong {
    font-weight: bold;
    color: #ffffff;
  }
  &.icon-tupian,
  &.icon-qingkong {
    font-size: 20px;
  }
}
</style>
