import { ElMessage } from 'element-plus'

// 当前使用的UI库类型
const UI_LIB = 'element' // 'naive' 或 'element'

interface MessageOptions {
  type?: 'success' | 'warning' | 'error' | 'info'
  duration?: number
}

// 创建naive-ui的离散API
// const { message: naiveMessage } = createDiscreteApi(['message'])

export function message(content: string, options: MessageOptions = {}) {
  const { type = 'info', duration = 3000 } = options

  // 会创建一个app实例
  // if (UI_LIB === 'naive') {
  //   return naiveMessage[type](content, {
  //     duration,
  //   })
  // }
  // else {
  return ElMessage({
    message: content,
    type,
    duration,
  })
  // }
}

// 导出便捷方法
export const success = (content: string, options?: Omit<MessageOptions, 'type'>) =>
  message(content, { ...options, type: 'success' })
export const warning = (content: string, options?: Omit<MessageOptions, 'type'>) =>
  message(content, { ...options, type: 'warning' })
export const error = (content: string, options?: Omit<MessageOptions, 'type'>) =>
  message(content, { ...options, type: 'error' })
export const info = (content: string, options?: Omit<MessageOptions, 'type'>) =>
  message(content, { ...options, type: 'info' })

const ms = {
  success,
  warning,
  error,
  info,
}

export default ms
