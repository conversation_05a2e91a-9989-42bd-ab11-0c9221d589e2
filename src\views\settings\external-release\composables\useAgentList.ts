import { getBizAgentAgentStoreList } from "@/api/agent"

export function useAgentList() {
  const agentList = ref<Agent.AgentVo[]>([])

  function getAgentList() {
    agentList.value = []
    getBizAgentAgentStoreList<{ rows: Agent.AgentVo[] }>({
      pageNum: 1,
      pageSize: 999999999,
      status: 1, // 状态 -1-私人助手 0-审核中 1-审核通过 2-审核拒绝
      isPrivate: false, // 是否私人助手
    }).then(res => {
      if (res.code === 200) {
        agentList.value = (res.rows || []).map(item => {
          return {
            ...item,
            label: item.name,
            value: item.id
          }
        })
      }
    })
  }

  return {
    agentList,
    getAgentList
  }
}