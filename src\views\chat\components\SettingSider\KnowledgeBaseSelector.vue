<script setup lang="ts">
import { computed, h, onMounted, ref, watch } from 'vue'
import { ArrowDown, Document, DocumentCopy, Grid, Picture, Search } from '@element-plus/icons-vue'
import { NMenu } from 'naive-ui'
import { getBizKbMyList, getBizKbShareList } from '@/api/knowledge-base'
import btn_mine from '@/assets/chat/btn-mine-knowledge-base.svg'
import btn_share from '@/assets/chat/btn-share-knowledge-base.svg'
import { SvgIcon } from '@/components/common'
import { useBasicLayout } from '@/hooks/useBasicLayout'

const props = defineProps<{
  visible: boolean
  initialKbList: KnowledgeBase.KnowledgeBaseVo[]
}>()

const emit = defineEmits(['update:visible', 'selected'])

const { isMobile } = useBasicLayout()

const kbList = ref<KnowledgeBase.KnowledgeBaseVo[]>([])
watch(
  () => props.initialKbList,
  newValue => {
    if (newValue) {
      kbList.value = newValue
    }
  },
  { immediate: true },
)

const searchQuery = ref('')
const activeTabIndex = ref(0)
const tabs = ['全部', '文档', '表格', '照片', '默认']

// 左侧我的知识库/共享知识库
const leftActiveTab = ref(0)
const leftTabs = ['我的知识库', '共享知识库']

// 分页相关
const total = ref(0)
const pageSize = ref(5)
const currentPage = ref(1)
const orderByColumn = ref('')

// 知识库列表数据
const knowledgeBases = ref<KnowledgeBase.KnowledgeBaseVo[]>([])
const loading = ref(false)

// 判断知识库是否已被选择
const isKnowledgeBaseSelected = (kbId: string) => {
  return kbList.value.some(kb => kb.id?.toString() === kbId?.toString())
}

// 加载知识库列表
const loadKnowledgeBases = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      name: searchQuery.value || undefined,
      orderByColumn: orderByColumn.value,
      isAsc: 'asc',
    }

    let res
    if (leftActiveTab.value === 0) {
      res = await getBizKbMyList<{ rows: KnowledgeBase.KnowledgeBaseVo[] }>(params)
    } else {
      res = await getBizKbShareList<{ rows: KnowledgeBase.KnowledgeBaseVo[] }>(params)
    }

    if (res.code === 200) {
      total.value = res.total
      knowledgeBases.value = res.rows.map(item => {
        // 根据文件类型判断图标
        let icon = 'file'
        if (item.type === 1) {
          icon = 'pdf'
        } else if (item.type === 2) {
          icon = 'image'
        }

        return {
          ...item,
          id: item.id?.toString() || '',
          name: item.name || '',
          description: item.description || '',
          enabled: item.enabled,
          size: item.size ? `${(item.size / (1024 * 1024)).toFixed(2)} MB` : '0 MB',
          createTime: item.editTime ? new Date(item.editTime).toLocaleString() : '',
          type: item.type === 1 ? 'pdf' : item.type === 2 ? 'image' : 'file',
          icon,
          downloadCount: 0,
        }
      })
    }
  } catch (error) {
    console.error('加载知识库列表失败', error)
  } finally {
    loading.value = false
  }
}

// 分页变化
const handlePageChange = (page: number) => {
  currentPage.value = page
  loadKnowledgeBases()
}

// 每页条数变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadKnowledgeBases()
}

// 根据搜索和标签过滤知识库
const filteredKnowledgeBases = computed(() => {
  let result = [...knowledgeBases.value]

  // 标签过滤
  if (activeTabIndex.value > 0 && activeTabIndex.value < 4) {
    const typeMap = {
      1: ['pdf', 'doc', 'docx'],
      2: ['xls', 'xlsx', 'csv'],
      3: ['jpg', 'png', 'gif', 'jpeg'],
    }
    const selectedTypes = typeMap[activeTabIndex.value as 1 | 2 | 3]
    result = result.filter(kb => selectedTypes.includes(kb.type))
  }

  return result
})

const getFileIconClass = (fileType: string) => {
  const iconMap: Record<string, string> = {
    pdf: 'text-red-500',
    word: 'text-blue-500',
    excel: 'text-green-500',
    image: 'text-cyan-500',
    file: 'text-gray-500',
  }
  return iconMap[fileType] || 'text-gray-500'
}

const selectKnowledgeBase = (kb: KnowledgeBase.KnowledgeBaseVo) => {
  emit('selected', kb)
}

const removeKnowledgeBase = (kb: KnowledgeBase.KnowledgeBaseVo) => {
  emit('selected', { ...kb, action: 'remove' })
}

const closeDialog = () => {
  emit('update:visible', false)
}

// 确保在dialog关闭时重置搜索和标签
watch(
  () => props.visible,
  newVal => {
    if (!newVal) {
      searchQuery.value = ''
      activeTabIndex.value = 0
      leftActiveTab.value = 0
    }
  },
)

// 监听搜索输入变化
watch(searchQuery, () => {
  currentPage.value = 1
  loadKnowledgeBases()
})

// 监听左侧标签变化
watch(leftActiveTab, () => {
  currentPage.value = 1
  loadKnowledgeBases()
})

// 监听排序变化
watch(orderByColumn, () => {
  loadKnowledgeBases()
})

onMounted(() => {
  if (props.visible) {
    loadKnowledgeBases()
  }
})

watch(
  () => props.visible,
  val => {
    if (val) {
      loadKnowledgeBases()
    }
  },
)

const hoverKbId = ref()

// 切换排序方式
const changeOrderBy = (column: string, text: string) => {
  orderByColumn.value = column
  tabs[4] = `${text}`
  currentPage.value = 1
  loadKnowledgeBases()
}

function renderImg(url: string) {
  return () =>
    h(
      'div',
      {
        style: {
          display: 'inline-flex',
          alignItems: 'center',
          marginRight: '16px',
          width: '24px',
          height: '24px',
          flexShrink: '0',
        },
      },
      [
        h('img', {
          src: url,
          style: {
            width: '24px',
            height: '24px',
            borderRadius: '50%',
            flexShrink: '0',
          },
        }),
      ],
    )
}

const menuOptions = [
  {
    label: '我的知识库',
    key: '0',
    icon: renderImg(btn_mine),
  },
  {
    label: '共享知识库',
    key: '1',
    icon: renderImg(btn_share),
  },
]

const handleUpdateTab = (key: string) => {
  leftActiveTab.value = parseInt(key)
  currentPage.value = 1
  loadKnowledgeBases()
}
</script>

<template>
  <el-dialog
    draggable
    :model-value="props.visible"
    :width="isMobile ? '95%' : '1000px'"
    style="padding: 0"
    destroy-on-close
    :close-on-click-modal="false"
    :before-close="closeDialog"
    @update:model-value="(val: boolean) => emit('update:visible', val)"
  >
    <div class="knowledge-base-selector">
      <div
        :class="isMobile ? 'flex flex-col' : 'flex'"
        :style="isMobile ? 'height: 80vh' : 'height: 700px'"
      >
        <!-- 左侧栏/顶部栏 - 知识库分类 -->
        <div
          :class="
            isMobile
              ? 'top-panel border-b p-4'
              : 'left-panel w-[220px] border-r overflow-auto p-[16px] mt-[-10px]'
          "
        >
          <div class="my-tit g-family">选择知识库</div>
          <!-- 搜索框 -->
          <div :class="isMobile ? 'search-container mt-4 mb-4' : 'search-container mt-6 mb-2'">
            <el-input
              v-model="searchQuery"
              style="background: #ffffff"
              placeholder="搜索"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
          <div :class="isMobile ? 'flex gap-2' : 'py-2'">
            <!-- resolved 给按钮添两个icon，来自src\assets\chat -->
            <NMenu
              v-if="!isMobile"
              :options="menuOptions"
              :value="leftActiveTab.toString()"
              default-value="0"
              @update:value="handleUpdateTab"
            />
            <!-- 移动端水平按钮组 -->
            <template v-else>
              <el-button
                v-for="(option, index) in menuOptions"
                :key="option.key"
                :type="leftActiveTab === index ? 'primary' : 'default'"
                :plain="leftActiveTab !== index"
                size="small"
                @click="handleUpdateTab(option.key)"
              >
                <img
                  :src="option.key === '0' ? btn_mine : btn_share"
                  style="width: 16px; height: 16px; margin-right: 4px"
                />
                {{ option.label }}
              </el-button>
            </template>
          </div>
        </div>

        <!-- 右侧栏/底部栏 - 文件列表 -->
        <div
          :class="
            isMobile
              ? 'bottom-panel flex-1 p-4 flex flex-col min-h-0'
              : 'right-panel flex-1 pl-4 p-[16px] !pb-[24px] flex flex-col'
          "
        >
          <!-- 分类标签 -->
          <div
            :class="isMobile ? 'tabs-container mb-3 flex' : 'tabs-container mb-4 flex items-center'"
          >
            <!-- <el-tabs v-model="activeTabIndex" class="flex-grow">
              <el-tab-pane
                v-for="(tab, index) in tabs.slice(0, 4)"
                :key="index"
                :label="tab"
                :name="index"
              />
            </el-tabs> -->
            <el-dropdown :class="isMobile ? '' : 'ml-4'">
              <span class="el-dropdown-link">
                <!-- 排序 -->
                <span class="t1">排序：</span><span class="t2">{{ tabs[4] }}</span>
                <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </span>
              <!-- resolved 此处切换能够在orderByColumnt体现，orderByColumnt是分页列表接口里的排序列，创建时间create_time，名称name -->
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="changeOrderBy('', '默认')">默认</el-dropdown-item>
                  <el-dropdown-item @click="changeOrderBy('create_time', '创建时间')"
                    >创建时间</el-dropdown-item
                  >
                  <el-dropdown-item @click="changeOrderBy('name', '名称')">名称</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>

          <!-- 知识库列表 -->
          <!-- resolved 预期这个div出现滚动条，但未按预期出现滚动条 -->
          <div class="knowledge-base-list overflow-y-auto flex-1 pr-2">
            <!-- resolved 调用接口getBizKbMyList，并增加一个el-pagination -->
            <el-empty
              v-if="filteredKnowledgeBases.length === 0 && !loading"
              description="暂无数据"
            />
            <el-skeleton v-if="loading" :rows="2" animated />
            <div
              v-for="kb in filteredKnowledgeBases"
              v-else
              :key="kb.id"
              class="knowledge-base-item"
              @mouseenter="hoverKbId = kb.id"
              @mouseleave="hoverKbId = null"
            >
              <div class="p-2 rounded">
                <div :class="isMobile ? 'flex flex-col gap-3' : 'flex items-top'">
                  <!-- 移动端顶部区域：图标+标题 -->
                  <div v-if="isMobile" class="flex items-center">
                    <!-- 文件图标 -->
                    <div :class="`file-icon ${getFileIconClass(kb.icon || 'file')} mr-3`">
                      <el-icon size="24">
                        <component
                          :is="
                            kb.icon === 'pdf'
                              ? Document
                              : kb.icon === 'word'
                                ? DocumentCopy
                                : kb.icon === 'excel'
                                  ? Grid
                                  : kb.icon === 'image'
                                    ? Picture
                                    : Document
                          "
                        />
                      </el-icon>
                    </div>
                    <!-- 文件名 -->
                    <div class="flex-1 min-w-0">
                      <div class="file-name text-base font-medium truncate">
                        {{ kb.name }}
                      </div>
                    </div>
                  </div>

                  <!-- 移动端描述、详情和操作按钮 -->
                  <div v-if="isMobile" class="mobile-details">
                    <div v-if="kb.description" class="file-description text-sm mb-3 text-gray-600">
                      {{ kb.description }}
                    </div>
                    <div class="flex items-center justify-between mb-3">
                      <div class="flex gap-2 text-xs text-gray-500">
                        <span class="my-file-tag">{{ kb.size }}</span>
                        <span class="my-file-tag">{{ (kb as any).downloadCount || 0 }}个</span>
                      </div>
                      <div class="text-xs text-gray-500">{{ (kb as any).createTime }}</div>
                    </div>
                    <!-- resolved 此处按钮可以做小点，或者跟文件名不放同一行 -->
                    <!-- 操作按钮单独一行 -->
                    <div class="my-actions">
                      <el-button
                        v-if="!isKnowledgeBaseSelected(kb.id || '')"
                        type="primary"
                        plain
                        size="small"
                        class="w-full"
                        @click="selectKnowledgeBase(kb)"
                        >添加到知识库</el-button
                      >
                      <template v-else>
                        <el-button
                          v-show="hoverKbId?.toString() === kb.id?.toString()"
                          type="danger"
                          plain
                          size="small"
                          class="w-full"
                          @click="removeKnowledgeBase(kb)"
                          >从知识库移除</el-button
                        >
                        <el-button
                          v-show="hoverKbId?.toString() !== kb.id?.toString()"
                          type="primary"
                          plain
                          size="small"
                          class="w-full !ml-0"
                          disabled
                          >已添加到知识库</el-button
                        >
                      </template>
                    </div>
                  </div>

                  <!-- 桌面端布局保持不变 -->
                  <template v-else>
                    <!-- 文件图标 -->
                    <div :class="`file-icon ${getFileIconClass(kb.icon || 'file')} mr-2`">
                      <el-icon size="28">
                        <component
                          :is="
                            kb.icon === 'pdf'
                              ? Document
                              : kb.icon === 'word'
                                ? DocumentCopy
                                : kb.icon === 'excel'
                                  ? Grid
                                  : kb.icon === 'image'
                                    ? Picture
                                    : Document
                          "
                        />
                      </el-icon>
                    </div>
                    <div class="flex-1">
                      <!-- top -->
                      <div class="flex items-top justify-between">
                        <!-- 文件信息 -->
                        <div class="file-info flex-1">
                          <div class="ell w-[500px] file-name text-base font-medium mb-2">
                            {{ kb.name }}
                          </div>
                          <div class="ell w-[500px] file-description text-base font-medium mb-1">
                            {{ kb.description }}
                          </div>
                        </div>
                        <!-- 操作按钮 -->
                        <div class="my-actions">
                          <el-button
                            v-if="!isKnowledgeBaseSelected(kb.id || '')"
                            type="primary"
                            plain
                            @click="selectKnowledgeBase(kb)"
                            >添加</el-button
                          >
                          <template v-else>
                            <el-button
                              v-show="hoverKbId?.toString() === kb.id?.toString()"
                              type="danger"
                              plain
                              class="ml-2"
                              @click="removeKnowledgeBase(kb)"
                              >移除</el-button
                            >
                            <el-button
                              v-show="hoverKbId?.toString() !== kb.id?.toString()"
                              type="primary"
                              plain
                              disabled
                              >已添加</el-button
                            >
                          </template>
                        </div>
                      </div>
                      <!-- bottom -->
                      <div class="flex items-center mt-2">
                        <div class="flex flex-1">
                          <div class="my-file-tag">
                            {{ kb.size }}
                          </div>
                          <div class="my-file-tag ml-2">{{ (kb as any).downloadCount || 0 }}个</div>
                        </div>
                        <div class="my-file-time">创建时间：{{ (kb as any).createTime }}</div>
                      </div>
                    </div>
                  </template>
                </div>
              </div>
            </div>
          </div>

          <!-- 分页组件 -->
          <div class="pagination-container mt-4 flex justify-center">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[5, 20, 50]"
              :layout="isMobile ? 'prev, pager, next' : 'total, sizes, prev, pager, next, jumper'"
              :total="total"
              :small="isMobile"
              @size-change="handleSizeChange"
              @current-change="handlePageChange"
            />
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<style scoped lang="less">
@import '@/styles/variables.less';

.knowledge-base-item {
  padding-top: 16px;
  padding-bottom: 16px;
  > div {
    &:hover {
      background: rgba(230, 234, 244, 0.35);
    }
  }
}
.knowledge-base-item + .knowledge-base-item {
  border-top: 1px solid #dadde8;
}

// 移动端样式优化
.top-panel {
  border-bottom: 1px solid #ebeef5;
  .my-tit {
    font-size: 20px;
    margin-bottom: 0;
  }
}

.bottom-panel {
  // 确保内容区域可以滚动
  .knowledge-base-list {
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
}

.mobile-details {
  .file-description {
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .my-actions {
    .el-button {
      border-radius: 6px;
      font-weight: 500;
      transition: all 0.2s ease;
    }
  }
}

// 移动端按钮优化
@media (max-width: 768px) {
  .knowledge-base-item {
    padding-top: 12px;
    padding-bottom: 12px;
  }

  .file-icon {
    width: 32px;
    height: 32px;
  }

  .my-file-tag {
    font-size: 11px;
    padding: 2px 6px;
  }
}
:deep(.n-menu) {
  .n-menu-item-content::before {
    left: 0;
    right: 0;
  }
}
.my-tit {
  font-size: 24px;
  font-weight: 500;
  color: #30343a;
  line-height: 33px;
  text-align: left;
  font-style: normal;
}

.my-file-tag {
  height: 20px;
  border-radius: 4px;
  padding: 0 8px;
  background: #e6eaf4;
  font-size: 12px;
  color: #646a73;
}

.my-file-time {
  font-size: 14px;
  color: #969ba4;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  padding: 0 8px;
}

.my-actions {
}
.file-name {
  font-size: 16px;
  color: #30343a;
  line-height: 22px;
  text-align: left;
  font-style: normal;
}

.file-description {
  font-size: 14px;
  color: #646a73;
  line-height: 20px;
  text-align: left;
  font-style: normal;
}

.knowledge-base-selector {
  overflow: hidden;
}

.el-tabs {
  margin-bottom: 0;
}

.file-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.el-dropdown-link {
  cursor: pointer;
  color: #000;
  display: flex;
  align-items: center;
  .t1 {
    color: #969ba4;
  }
  .t2 {
    color: #000;
  }
}

.left-panel {
  border-right: 1px solid #ebeef5;
  // background: #f7f8fa;
}

.right-panel {
  padding-right: 16px;
}

/* 深色模式适配 */
:deep(.el-dialog) {
  border-radius: 8px;
}

:deep(.dark .el-dialog) {
  background-color: #1f1f1f;
  color: #e5e7eb;
}

:deep(.dark .el-dialog__title) {
  color: #e5e7eb;
}

:deep(.dark .el-tabs__item) {
  color: #9ca3af;
}

:deep(.dark .el-tabs__item.is-active) {
  color: #409eff;
}

:deep(.dark .el-dropdown-link) {
  color: #9ca3af;
}

:deep(.dark .el-menu) {
  background-color: transparent;
  border-right-color: #2d2d2d;
}

:deep(.dark .el-menu-item) {
  color: #e5e7eb;
}

:deep(.dark .el-menu-item.is-active) {
  color: #409eff;
}

:deep(.dark .left-panel) {
  border-right-color: #2d2d2d;
}

:deep(.dark .knowledge-base-item:hover) {
  background-color: @bg-active;
}
</style>
