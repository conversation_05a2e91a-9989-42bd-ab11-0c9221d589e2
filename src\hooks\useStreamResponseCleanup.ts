import { onBeforeUnmount, onUnmounted } from 'vue'
import { streamResponseManager } from '@/utils/streamResponseManager'

// 在组件卸载或窗口关闭时清理流式响应状态的hook
export function useStreamResponseCleanup() {
  // 清理所有流式响应状态
  function cleanupAllStates() {
    console.log('清理所有流式响应状态')
    streamResponseManager.clearAllStates()
  }

  // 清理指定会话的状态
  function cleanupConversationState(conversationId: string) {
    console.log(`清理会话 ${conversationId} 的流式响应状态`)
    streamResponseManager.clearState(conversationId)
  }

  // 获取所有正在进行的会话
  function getLoadingConversations() {
    return streamResponseManager.getLoadingConversations()
  }

  // 停止指定会话的流式响应
  async function stopConversation(conversationId: string) {
    return streamResponseManager.stopConversation(conversationId)
  }

  // 检查会话是否正在加载
  function isConversationLoading(conversationId: string) {
    return streamResponseManager.isLoading(conversationId)
  }

  // 组件卸载时清理
  onBeforeUnmount(() => {
    // 这里不清理所有状态，因为用户可能只是切换路由
    // 实际的清理在窗口关闭时进行
  })

  // 监听窗口关闭事件
  const handleBeforeUnload = (event: BeforeUnloadEvent) => {
    const loadingConversations = getLoadingConversations()
    if (loadingConversations.length > 0) {
      // 如果有正在进行的会话，询问用户是否确认离开
      const message = `当前有 ${loadingConversations.length} 个会话正在进行中，确定要离开吗？`
      event.preventDefault()
      event.returnValue = message
      return message
    }
  }

  // 监听窗口卸载事件（实际关闭时清理）
  const handleUnload = () => {
    cleanupAllStates()
  }

  // 添加事件监听器
  if (typeof window !== 'undefined') {
    window.addEventListener('beforeunload', handleBeforeUnload)
    window.addEventListener('unload', handleUnload)
  }

  // 移除事件监听器
  onUnmounted(() => {
    if (typeof window !== 'undefined') {
      window.removeEventListener('beforeunload', handleBeforeUnload)
      window.removeEventListener('unload', handleUnload)
    }
  })

  return {
    cleanupAllStates,
    cleanupConversationState,
    getLoadingConversations,
    stopConversation,
    isConversationLoading,
  }
}
