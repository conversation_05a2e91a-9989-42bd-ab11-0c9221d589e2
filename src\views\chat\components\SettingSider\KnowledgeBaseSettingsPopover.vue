<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { Close } from '@element-plus/icons-vue'
import ms from '@/utils/message'
import {
  getBizAiModelAvailableModels,
  postBizConversationUpdateConversation,
  updateBizGuestConversation,
} from '@/api'
import { useChatStore, useSettingStore } from '@/store'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { usePublicChat } from '../../hooks/usePublicChat'

const props = defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
}>()

const { isMobile } = useBasicLayout()

// 设置参数
// resolved 从chatStore获取
const maxResults = ref(3)
const minScore = ref(0.5)
const rerank = ref(false)
const rerankModelId = ref<number>()
const modelOptions = ref<{ label: string; value: number }[]>([])

// 获取设置store
const settingStore = useSettingStore()
const chatStore = useChatStore()

const { isPublicChat } = usePublicChat()
// 参考 src\views\knowledge-base\common\search-test.vue
const cancelSetting = () => {
  emit('update:visible', false)
}
const saveSetting = async () => {
  // 保存设置的逻辑
  // resolved 调用postBizChatUpdate，并更新到chatStore中
  if (!chatStore.active) {
    console.error('没有活动的对话')
    return
  }

  try {
    const params: any = {
      id: chatStore.active,
      maxResults: maxResults.value,
      minScore: minScore.value,
    }

    // 如果启用了重排序，添加重排序模型ID
    if (rerank.value && rerankModelId.value) {
      params.rerankModelId = rerankModelId.value
    } else {
      // 不设置该属性
      // params.rerankModelId = undefined
    }
    const postConversationUpdateConversation = isPublicChat
      ? updateBizGuestConversation
      : postBizConversationUpdateConversation

    // 调用接口更新会话设置
    await postConversationUpdateConversation(params)

    // 更新chatStore中的会话信息
    const updateData: Partial<Chat.ConversationVo> = {
      maxResults: maxResults.value,
      minScore: minScore.value,
    }

    if (rerank.value && rerankModelId.value) {
      updateData.rerankModelId = rerankModelId.value
    }

    chatStore.updateHistory(chatStore.active, updateData)
    ms.success('操作成功')
    emit('update:visible', false)
  } catch (error) {
    console.error('更新知识库设置失败:', error)
  }
}

// 获取默认模型和可用模型列表
const fetchModels = async () => {
  try {
    // 从store中获取默认重排序模型
    const { rerankModelId: defaultRerankModelId } = (await settingStore.fetchDefaultModel()) || {}

    // 获取重排序模型列表
    const { data: rerankModels } = await getBizAiModelAvailableModels({ type: 'rerank' })

    if (rerankModels && rerankModels.length > 0) {
      modelOptions.value = rerankModels.map(model => ({
        label: model.displayName || model.name,
        value: model.id,
      }))

      // 设置默认选中的模型
      if (!rerankModelId.value) {
        if (defaultRerankModelId) {
          rerankModelId.value = defaultRerankModelId
        } else if (modelOptions.value.length > 0) {
          rerankModelId.value = modelOptions.value[0].value
        }
      }
    }
  } catch (error) {
    console.error('获取模型信息失败:', error)
  }
}

// 初始化设置数据
const initSettingsFromChatStore = () => {
  if (!chatStore.active) return

  const currentChat = chatStore.getChatHistoryByCurrentActive
  // console.log('currentChat', currentChat)

  if (currentChat) {
    // 设置最大召回数
    if (currentChat.maxResults !== undefined && currentChat.maxResults !== null) {
      maxResults.value = currentChat.maxResults
    }

    // 设置最小匹配度
    if (currentChat.minScore !== undefined && currentChat.minScore !== null) {
      minScore.value = currentChat.minScore
    }

    // 设置重排序开关和模型
    if (currentChat.rerankModelId !== undefined && currentChat.rerankModelId !== null) {
      rerank.value = true
      rerankModelId.value = currentChat.rerankModelId
    } else {
      rerank.value = false
      rerankModelId.value = undefined
      // console.log('rerankModelId.value-1', rerankModelId.value)
    }
  }
  if (!isPublicChat) {
    fetchModels()
  }
  // console.log('rerankModelId.value-2', rerankModelId.value)
}

// 监听对话变化
watch(
  () => chatStore.active,
  () => {
    // console.log('initSettingsFromChatStore-1')
    initSettingsFromChatStore()
  },
)

// 监听弹窗可见性变化
watch(
  () => props.visible,
  newValue => {
    if (newValue) {
      // console.log('initSettingsFromChatStore-2')
      initSettingsFromChatStore()
    }
  },
)

onMounted(() => {
  // console.log('initSettingsFromChatStore-3')
  initSettingsFromChatStore()
})
</script>

<template>
  <el-popover
    :visible="visible"
    trigger="click"
    :placement="isMobile ? 'bottom-start' : 'left-end'"
    :width="isMobile ? '90%' : '360'"
    popper-class="kb-settings-popover-popper"
    @hide="$emit('update:visible', false)"
  >
    <template #reference>
      <slot></slot>
    </template>

    <div class="kb-settings-popover">
      <div class="kb-settings-header">
        <span class="title g-family">知识库设置</span>
        <div class="header-right">
          <el-icon class="close-icon" @click="$emit('update:visible', false)">
            <Close />
          </el-icon>
        </div>
      </div>

      <!-- 设置选项 -->
      <div class="settings-list">
        <!-- resolved 此item布局改为上下排列 -->
        <div class="search-text-config-item vertical">
          <div class="config-label-row">
            <span class="search-text-config-label">最大召回数</span>
          </div>
          <div class="config-control-row">
            <div class="slider">
              <el-slider v-model="maxResults" :min="0" :max="10" />
            </div>
            <el-input-number
              v-model="maxResults"
              class="slider-input"
              :min="0"
              :max="10"
              controls-position="right"
            />
          </div>
        </div>

        <!-- resolved 此item布局改为上下排列 -->
        <div class="search-text-config-item vertical">
          <div class="config-label-row">
            <span class="search-text-config-label">最小匹配度</span>
          </div>
          <div class="config-control-row">
            <el-slider v-model="minScore" class="slider" :min="0.01" :max="0.99" :step="0.01" />
            <el-input-number
              v-model="minScore"
              class="slider-input"
              :min="0.01"
              :max="0.99"
              :step="0.01"
              controls-position="right"
            />
          </div>
        </div>

        <div class="search-text-config-item flex justify-between">
          <div class="w-[143px]">
            <span class="search-text-config-label">结果重排</span>
          </div>
          <el-switch v-model="rerank" />
        </div>

        <!-- resolved 此item布局改为上下排列 -->
        <div v-if="rerank" class="search-text-config-item vertical">
          <div class="config-label-row">
            <span class="search-text-config-label">重排序模型</span>
          </div>
          <div class="config-control-row full-width">
            <el-select v-model="rerankModelId" placeholder="请选择重排序模型" class="model-select">
              <template #prefix>
                <img class="size-[20px]" src="@/assets/knowledge/model-icon.png" alt="" />
              </template>
              <el-option
                v-for="option in modelOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="footer flex justify-between">
        <el-button plain class="w-[50%]" type="primary" size="large" @click="cancelSetting">
          取消
        </el-button>
        <el-button type="primary" class="w-[50%]" size="large" @click="saveSetting">
          保存
        </el-button>
      </div>
    </div>
  </el-popover>
</template>

<style scoped lang="less">
.kb-settings-popover {
  padding: 0;
  border-radius: 8px;
  background: #fff;
}

.kb-settings-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.kb-settings-header .title {
  font-size: 18px;
  font-weight: 500;
  color: #303133;
}

.header-right {
  display: flex;
  align-items: center;
}

.close-icon {
  cursor: pointer;
  font-size: 16px;
  color: #909399;
  &:hover {
    color: #606266;
  }
}

.settings-list {
  padding: 16px;
  max-height: 360px;
  overflow-y: auto;
}

.search-text-config-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }

  &.vertical {
    flex-direction: column;
    align-items: flex-start;
  }
}

.config-label-row {
  margin-bottom: 8px;
  width: 100%;
}

.config-control-row {
  display: flex;
  align-items: center;
  width: 100%;

  &.full-width {
    display: block;
  }
}

.search-text-config-label {
  font-size: 14px;
  color: #303133;
}

.slider {
  flex: 1;
  margin-right: 16px;
}

.slider-input {
  width: 100px;
}

.model-select {
  width: 100%;
}

.slider-input {
  width: 120px;
  height: 44px;
  background: #ffffff;
  margin-left: 24px;

  :deep(.el-input__inner) {
    text-align: left;
  }
  :deep(.el-input-number__decrease),
  :deep(.el-input-number__increase) {
    --el-input-number-controls-height: 20px;
    --el-border-radius-base: 8px;
    background: #ffffff;
  }
}

.footer {
  padding: 16px;
  border-top: 1px solid #e4e7ed;
}

.dark {
  .kb-settings-popover {
    background: #1f1f1f;
  }

  .kb-settings-header {
    border-bottom: 1px solid #2d2d2d;
  }

  .kb-settings-header .title {
    color: #e5eaf3;
  }

  .close-icon {
    color: #a3a6ad;
    &:hover {
      color: #e5eaf3;
    }
  }

  .search-text-config-label {
    color: #e5eaf3;
  }

  .footer {
    border-top: 1px solid #2d2d2d;
  }
}
</style>

<style>
.kb-settings-popover-popper {
  padding: 0 !important;
}
</style>
