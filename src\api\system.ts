import { deleteReq, get, post, put } from '@/utils/request'
import '@/typings/api.d.ts'
import '@/typings/system.d.ts'
import { encrypt as rsaEncrypt } from '@/components/common/LoginDialog/utils/jsencrypt'
import {
  encryptBase64,
  encryptWithAes,
  generateAesKey,
} from '@/components/common/LoginDialog/utils/crypto'

/** =====个人中心===== */
// "name": "个人信息",
// "method": "get",
// "path": "/system/user/profile",
export function getSystemUserProfile<T>() {
  return get<T>({
    url: `/system/user/profile`,
  })
}

// "name": "修改用户信息",
// "method": "put",
// "path": "/system/user/profile",
export function putSystemUserProfile<T>(data: System.ProfileBo) {
  return put<T>({
    url: `/system/user/profile`,
    data,
  })
}

// "name": "重置密码",
// "method": "put",
// "path": "/system/user/profile/updatePwd",
export function putSystemUserProfileUpdatePwd<T>(data: {
  password: string
  confirmPassword: string
}) {
  return put<T>({
    url: `/system/user/profile/updatePwd`,
    data,
    headers: {
      'Content-Type': 'application/json;charset=utf-8',
      isEncrypt: true,
    },
  })
}

// "name": "更换手机号",
// "method": "put",
// "path": "/system/user/profile/updatePhone",
export function putSystemUserProfileUpdatePhone<T>(data: { phonenumber: string }) {
  return put<T>({
    url: `/system/user/profile/updatePhone`,
    data,
  })
}

// "name": "退出团队", // 暂不开发
// "method": "post",
// "path": "/system/user/profile/quit",
export function postSystemUserProfileQuit<T>() {
  return post<T>({
    url: `/system/user/profile/quit`,
  })
}

// "name": "头像上传",
// "method": "post",
// "path": "/system/user/profile/avatar",
export function postSystemUserProfileAvatar<T>(data: FormData) {
  console.log('postSystemUserProfileAvatar', data)
  return post<T>({
    url: `/system/user/profile/avatar`,
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

// "name": "退出登录",
// "method": "post",
// "path": "/auth/logout",
export function postAuthLogout<T>() {
  return post<T>({
    url: `/auth/logout`,
  })
}

// 验证密码（接口加密）
// post
// /confirm/pwd
// resolved 参考src\components\common\LoginDialog\utils\api.ts的登录逻辑，对本接口进行加密
export function postConfirmPwd<T>(data: { password: string }) {
  return post<T>({
    url: `/confirm/pwd`,
    data,
    headers: {
      'Content-Type': 'application/json;charset=utf-8',
      isEncrypt: true,
    },
  })
}

// 验证手机号
// get
// /confirm/phone
export function getConfirmPhone<T>(data: { phonenumber?: string; smsCode: string }) {
  return get<T>({
    url: `/confirm/phone`,
    data,
  })
}

/** =====成员邀请流程===== */
// 批量设置部门
// /system/user/changeDept
// post
export function postSystemUserChangeDept<T>(data: { userIds: string[]; deptId: string }) {
  return post<T>({
    url: `/system/user/changeDept?userIds=${data.userIds}&deptId=${data.deptId}`,
    data,
  })
}

// 获取邀请码
// /system/invitation/code
// get
export function getSystemInvitationCode<T>() {
  return get<System.InvitationCodeVO>({
    url: `/system/invitation/code`,
  })
}

// 更新邀请码有效期
// /system/invitation/updateCode/{id}
// post
export function postSystemInvitationUpdateCode(data: { id: string; day: string }) {
  return post<System.InvitationCodeVO>({
    url: `/system/invitation/updateCode/${data.id}?day=${data.day}`,
  })
}
