import { ss } from '@/utils/storage'

const LOCAL_NAME = 'settingsStorage'

export interface DefaultModelState {
  id?: number | null
  chatModelId?: number | null
  embeddingModelId?: number | null
  rerankModelId?: number | null
  summaryModelId?: number | null
  lastFetchTime: number
}

export interface SettingsState {
  systemMessage: string
  temperature: number
  top_p: number
  typewriterEffect: boolean // 打字机效果开关
  defaultModel: DefaultModelState // 默认模型配置
}

export function defaultSetting(): SettingsState {
  return {
    systemMessage:
      "You are ChatGPT, a large language model trained by OpenAI. Follow the user's instructions carefully. Respond using markdown.",
    temperature: 0.8,
    top_p: 0.7,
    typewriterEffect: true, // 默认启用打字机效果
    defaultModel: {
      id: null,
      chatModelId: null,
      embeddingModelId: null,
      rerankModelId: null,
      lastFetchTime: 0,
    },
  }
}

export function getLocalState(): SettingsState {
  const localSetting: SettingsState | undefined = ss.get(LOCAL_NAME)
  return { ...defaultSetting(), ...localSetting }
}

export function setLocalState(setting: SettingsState): void {
  ss.set(LOCAL_NAME, setting)
}

export function removeLocalState() {
  ss.remove(LOCAL_NAME)
}
