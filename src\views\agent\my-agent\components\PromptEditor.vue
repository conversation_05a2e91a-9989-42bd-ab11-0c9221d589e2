<script setup lang="ts">
import { nextTick, ref, watch } from 'vue'
import OptimizePopover from './OptimizePopover.vue'
import MarkdownEditor from '@/components/MarkdownEditor.vue'

const props = defineProps<{
  modelValue: string
  disabled: boolean
}>()

const emit = defineEmits(['update:modelValue'])

// 优化相关变量
const optimizeVisible = ref(false)

// 优化按钮点击事件
const handleOpenOptimize = () => {
  if (!props.modelValue.trim()) {
    ElMessage.warning('请输入需要优化的提示词')
    return
  }
  optimizeVisible.value = true
}

// 处理优化后的提示词
const handleUseOptimized = (optimizedText: string) => {
  emit('update:modelValue', optimizedText)
}

watch(
  () => props.modelValue,
  async newVal => {
    // console.log('modelValue', newVal)
    await nextTick()
  },
  { immediate: true, deep: true },
)
</script>

<template>
  <div class="tab-header">
    <div class="title">提示词</div>
    <!-- 提示词优化弹窗 -->
    <!-- resolved 需求:点击外部遮罩层不要关闭popover -->
    <OptimizePopover
      v-model:visible="optimizeVisible"
      :model-value="modelValue"
      @use-optimized="handleUseOptimized"
    >
      <div v-if="!disabled" class="optimize-button" @click="handleOpenOptimize">
        <img src="@/assets/agent/optimize.svg" alt="optimize" />
        <span class="ml-1">优化</span>
      </div>
    </OptimizePopover>
  </div>
  <div class="tab-content mt-[20px]">
    <MarkdownEditor
      :disabled="disabled"
      :model-value="modelValue"
      placeholder="请输入提示词"
      :height="600"
      @update:model-value="val => emit('update:modelValue', val)"
    />
  </div>
</template>

<style lang="less" scoped>
.optimize-button {
  cursor: pointer;
  width: 80px;
  height: 32px;
  background: #e9e9fe;
  border-radius: 4px;
  font-size: 14px;
  color: #4865e8;
  line-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
