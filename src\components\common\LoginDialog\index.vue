<!-- 弃用 -->
<!-- <script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import { Icon } from '@iconify/vue'
import { getCodeImg, getTenantList } from './utils/api'
import type { LoginData, TenantVO } from './utils/api'
import { useUserStore } from '@/store/modules/user'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['update:modelValue', 'login-success'])
const userStore = useUserStore()
const loading = ref(false)
const codeUrl = ref('')
const tenantEnabled = ref(true)
const tenantList = ref<TenantVO[]>([])
const showPassword = ref(false)

const loginForm = reactive({
  tenantId: '000000',
  username: 'admin',
  password: 'admin123',
  code: '',
  uuid: '',
})

const errors = reactive({
  tenantId: '',
  username: '',
  password: '',
  code: '',
})

// 获取验证码
const getCode = async () => {
  const res = await getCodeImg()
  const { data } = res
  codeUrl.value = `data:image/gif;base64,${data.img}`
  loginForm.uuid = data.uuid
}

// 获取租户列表
const initTenantList = async () => {
  const res = await getTenantList()
  const { data } = res
  tenantEnabled.value = data.tenantEnabled === undefined ? true : data.tenantEnabled
  if (tenantEnabled.value) {
    tenantList.value = data.voList
    if (tenantList.value != null && tenantList.value.length !== 0)
      loginForm.tenantId = tenantList.value[0].tenantId
  }
}

// 表单重置
const reset = () => {
  loginForm.username = ''
  loginForm.password = ''
  loginForm.code = ''
  loginForm.uuid = ''
  if (tenantList.value && tenantList.value.length > 0)
    loginForm.tenantId = tenantList.value[0].tenantId
  else
    loginForm.tenantId = '000000'

  Object.keys(errors).forEach((key) => {
    errors[key] = ''
  })
}

// 表单验证
const validateForm = () => {
  let isValid = true

  if (tenantEnabled.value && !loginForm.tenantId) {
    errors.tenantId = '请选择租户'
    isValid = false
  }

  if (!loginForm.username) {
    errors.username = '请输入用户名'
    isValid = false
  }

  if (!loginForm.password) {
    errors.password = '请输入密码'
    isValid = false
  }

  if (!loginForm.code) {
    errors.code = '请输入验证码'
    isValid = false
  }

  return isValid
}

// 登录方法
const handleLogin = async () => {
  if (!validateForm())
    return

  loading.value = true
  try {
    const data: LoginData = {
      tenantId: loginForm.tenantId,
      username: loginForm.username,
      password: loginForm.password,
      code: loginForm.code,
      uuid: loginForm.uuid,
    }
    console.log('LoginData', data)

    // 使用userStore进行登录
    await userStore.login(data)
    // 获取用户信息
    await userStore.getInfo()

    // 发送登录成功事件
    emit('login-success', {
      token: userStore.token,
      userInfo: {
        userId: userStore.userId,
        userName: userStore.name,
        nickName: userStore.nickname,
        avatar: userStore.avatar,
        roles: userStore.roles,
        permissions: userStore.permissions,
      },
    })
    userStore.setShowLogin(false)
    emit('update:modelValue', false)
    reset()
    window.location.reload()
  }
  catch (error: any) {
    console.error('登录失败:', error)
    // alert(error.message || '登录失败，请重试')
    getCode()
  }
  finally {
    loading.value = false
  }
}

const handleOverlayClick = (e: MouseEvent) => {
  // 允许点击遮罩层关闭
  // useUserStore().setShowLogin(false)
  // emit('update:modelValue', false)
}

const handleClose = () => {
  useUserStore().setShowLogin(false)
  // emit('update:modelValue', false)
  reset()
}

onMounted(() => {
  getCode()
  initTenantList()
})
</script>

<template>
  <Teleport to="body">
    <div v-if="modelValue" class="modal-overlay" @click="handleOverlayClick">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h2>用户登录</h2>
          <button class="close-button" @click="handleClose">
            &times;
          </button>
        </div>

        <form class="login-form" @submit.prevent="handleLogin">
          <!-- 租户选择 -->
          <div v-if="tenantEnabled" class="form-group">
            <label>公司名称</label>
            <select v-model="loginForm.tenantId" class="form-control">
              <option v-for="item in tenantList" :key="item.tenantId" :value="item.tenantId">
                {{ item.companyName }}
              </option>
            </select>
            <span v-if="errors.tenantId" class="error-message">{{ errors.tenantId }}</span>
          </div>

          <!-- 用户名 -->
          <div class="form-group">
            <label>用户名</label>
            <div class="input-with-icon">
              <Icon icon="mdi:account" class="input-icon" />
              <input v-model="loginForm.username" type="text" class="form-control" placeholder="请输入用户名" autocomplete="off">
            </div>

            <span v-if="errors.username" class="error-message">{{ errors.username }}</span>
          </div>

          <!-- 密码 -->
          <div class="form-group">
            <label>密码</label>
            <div class="input-with-icon">
              <Icon icon="mdi:lock" class="input-icon" />
              <input
                v-model="loginForm.password"
                :type="showPassword ? 'text' : 'password'"
                class="form-control"
                placeholder="请输入密码"
                autocomplete="off"
              >
              <button type="button" class="toggle-password" @click="showPassword = !showPassword">
                <Icon :icon="showPassword ? 'mdi:eye-off' : 'mdi:eye'" />
              </button>
            </div>

            <span v-if="errors.password" class="error-message">{{ errors.password }}</span>
          </div>

          <!-- 验证码 -->
          <div class="form-group">
            <label>验证码</label>
            <div class="captcha-group">
              <div class="input-with-icon">
                <Icon icon="mdi:shield" class="input-icon" />
                <input v-model="loginForm.code" type="text" class="form-control" placeholder="请输入验证码" autocomplete="off">
              </div>

              <img :src="codeUrl" class="captcha-image" alt="验证码" @click="getCode">

            </div>
            <span v-if="errors.code" class="error-message">{{ errors.code }}</span>
          </div>

          <div class="form-actions">
            <button type="submit" class="btn btn-primary" :disabled="loading">
              {{ loading ? '登录中...' : '登录' }}
            </button>
            <button type="button" class="btn btn-default" @click="handleClose">
              取消
            </button>
          </div>
        </form>
      </div>
    </div>
  </Teleport>
</template>

<style lang="less" scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 400px;
  max-width: 90%;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  animation: modal-in 0.3s ease-out;
}

.modal-header {
  padding: 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h2 {
    margin: 0;
    font-size: 18px;
    color: #333;
  }

  .close-button {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
    padding: 0;
    line-height: 1;

    &:hover {
      color: #666;
    }
  }
}

.login-form {
  padding: 20px;
}

.form-group {
  margin-bottom: 20px;

  label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-size: 14px;
  }
}

.input-with-icon {
  position: relative;

  .input-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    font-size: 20px;
  }

  input {
    padding-left: 35px;
  }

  .toggle-password {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      color: #666;
    }

    .iconify {
      font-size: 20px;
    }
  }
}

.form-control {
  width: 100%;
  height: 40px;
  padding: 8px 12px;
  font-size: 14px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  transition: border-color 0.2s;

  &:focus {
    border-color: #409eff;
    outline: none;
  }

  &::placeholder {
    color: #999;
  }
}

select.form-control {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23999' d='M6 8L2 4h8z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  padding-right: 30px;
}

.captcha-group {
  display: flex;
  gap: 12px;

  .input-with-icon {
    flex: 1;
  }

  .captcha-image {
    height: 40px;
    border-radius: 4px;
    cursor: pointer;
  }
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 24px;
}

.btn {
  min-width: 120px;
  height: 40px;
  padding: 0 20px;
  font-size: 14px;
  border-radius: 4px;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.3s;

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
}

.btn-primary {
  background: #409eff;
  color: white;
  border-color: #409eff;

  &:hover:not(:disabled) {
    background: #66b1ff;
    border-color: #66b1ff;
  }
}

.btn-default {
  background: white;
  border-color: #dcdfe6;
  color: #606266;

  &:hover {
    color: #409eff;
    border-color: #c6e2ff;
    background-color: #ecf5ff;
  }
}

.error-message {
  display: block;
  margin-top: 4px;
  color: #f56c6c;
  font-size: 12px;
}

@keyframes modal-in {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style> -->
