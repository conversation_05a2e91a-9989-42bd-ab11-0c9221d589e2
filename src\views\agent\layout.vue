<script setup lang="ts">
import { <PERSON>Button, NMenu } from 'naive-ui'
import { useRoute, useRouter } from 'vue-router'
import { computed, h, onMounted, provide, ref, watch } from 'vue'
import { SvgIcon } from '@/components/common'
import robotAvatar from '@/assets/DEFAULT_ROBOT_AVATAR.png'
import agentStore from '@/assets/agent/agent-store.svg'
import { getBizAgentAgentStoreList, getBizAgentCategoryList } from '@/api/agent'
import ms from '@/utils/message'

const router = useRouter()
const route = useRoute()
const collapsed = ref(false)

// 提供一个响应式引用来存储header-actions的内容
const headerActions = ref(null)
provide('headerActions', headerActions)

// 我的助手总数
const myAgentTotal = ref(0)

// 获取我的助手总数
const loadMyAgentTotal = async () => {
  try {
    const res = await getBizAgentAgentStoreList<API.ResponseRows<Agent.AgentVo>>({})
    if (res.code === 200) {
      myAgentTotal.value = res.total
    }
  } catch (error) {
    ms.error('Failed to get my agent total')
  }
}

// 根据路由信息动态计算活跃菜单项
const activeKey = computed(() => {
  // 如果当前路由是agent-store，且有categoryId参数
  if (route.name === 'agent-store' && route.query.categoryId) {
    return `category-${route.query.categoryId}`
  }
  // 否则返回路由名称
  return route.name as string
})

// 获取分类列表
const categoryList = ref<Agent.AgentCategoryVo[]>([])
const loadCategoryList = async () => {
  try {
    const res = await getBizAgentCategoryList<Agent.AgentCategoryVo[]>()
    if (res.code === 200) {
      categoryList.value = res.data || []
    }
  } catch (error) {
    console.error('获取分类列表失败:', error)
  }
}

// 计算面包屑路径
// resolved title是"新增助手"，那么"编辑助手"的情况怎么办
const breadcrumbs = computed(() => {
  const paths = route.path.split('/').filter(Boolean)
  const result: string[] = []

  // 从路由匹配记录中获取每一级的标题
  route.matched.forEach((record, index) => {
    if (index > 0) {
      // 跳过第一个（根路由）
      let title = record.meta.title as string

      // 如果路径包含编辑操作且有id参数，将"新增助手"改为"编辑助手"
      if (route.path.includes('/agent-operation') && route.query.id) {
        if (title === '新增助手') {
          title = '编辑助手'
        }
      }

      result.push(title)
    }
  })

  return result
})

// 监听路由变化，在agent-operation路径下自动收起侧边栏
watch(
  () => route.path,
  newPath => {
    if (newPath.includes('/agent/agent-operation')) {
      collapsed.value = true
    } else if (newPath.includes('/agent')) {
      collapsed.value = false
    }
  },
  { immediate: true },
)

// 初始化时加载分类列表和我的助手总数
onMounted(() => {
  loadCategoryList()
  loadMyAgentTotal()
  if (route.path.includes('/agent/agent-operation')) {
    collapsed.value = true
  }
})

// 控制菜单展开状态
const expandedKeys = ref(['agent-store'])

const menuOptions = computed(() => [
  {
    label: '我的助手',
    key: 'my-agent',
    icon: renderImg(robotAvatar),
  },
  {
    label: () => h('div', { onClick: () => router.push('/agent/agent-store') }, '企业助手'),
    // label: '企业助手',
    key: 'agent-store',
    // resolved 点击icon去折叠展开未成功
    // todo 要点击右侧箭头去出发而不是左侧icon...
    icon: renderImg(agentStore),
    children: [
      ...categoryList.value.map(category => ({
        label: category.name,
        key: `category-${category.id}`,
      })),
    ],
  },
])

function renderImg(url: string) {
  return () =>
    h(
      'div',
      {
        style: {
          display: 'inline-flex',
          alignItems: 'center',
          marginRight: '16px',
          width: '32px',
          height: '32px',
          flexShrink: '0',
        },
      },
      [
        h('img', {
          src: url,
          style: {
            width: '32px',
            height: '32px',
            borderRadius: '50%',
            flexShrink: '0',
          },
        }),
      ],
    )
}

function renderIcon(icon: string) {
  return () =>
    h(
      'div',
      {
        style: {
          display: 'inline-flex',
          alignItems: 'center',
          marginRight: '16px',
        },
      },
      [h(SvgIcon, { icon })],
    )
}

function handleUpdateValue(key: string, item: any) {
  // 检查是否是分类点击
  if (key.startsWith('category-')) {
    const categoryId = key.replace('category-', '')
    const targetPath = `/agent/agent-store?categoryId=${categoryId}`
    router.push(targetPath)
    return
  }

  // 检查路由是否存在
  const route = router.resolve(`/agent/${key}`)
  if (route.matched.length === 0) {
    ms.warning('该功能暂未开放')
    return
  }
  router.push(`/agent/${key}`)
}
</script>

<template>
  <div class="agent-container">
    <div class="agent-sider-wrapper" :class="{ collapsed }">
      <div class="agent-sider h-full flex flex-col">
        <div class="top-con">
          <div class="left-box">企业助手</div>
          <div class="right-box">
            <span class="t1">{{ myAgentTotal }}</span>
            <span class="t2">/</span>
            <span class="t3"><span style="font-size: 1.5em" class="px-1">∞</span></span>
          </div>
        </div>
        <div
          class="flex-1 ex-n-menu-box mr-[-8px] pr-[8px] overflow-y-auto max-h-[calc(100vh-130px)]"
        >
          <NMenu
            :options="menuOptions"
            :value="activeKey"
            :default-value="activeKey"
            :expanded-keys="expandedKeys"
            @update:value="handleUpdateValue"
          />
        </div>
      </div>
    </div>
    <div
      class="agent-content"
      :class="{ 'full-width': collapsed, 'agent-content-collapsed': collapsed }"
    >
      <div class="my-breadcrumb">
        <!-- resolved 按src\views\settings\index.vue的breadcrumbs改造 -->
        <div class="flex justify-between items-center">
          <div>{{ breadcrumbs.join(' / ') }}</div>
          <component :is="headerActions" v-if="headerActions" />
        </div>
      </div>
      <div class="agent-content-body">
        <router-view />
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.agent-container {
  height: 100%;
  display: flex;
  overflow-x: hidden;
}

.agent-sider-wrapper {
  width: 240px;
  overflow: hidden;
  transition: all 0.3s ease;

  &.collapsed {
    width: 0;
  }
}

.agent-sider {
  width: 240px;
  background: #fff;
  padding: 8px;
  flex-shrink: 0;

  .top-con {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid #dadde8;
    margin-bottom: 16px;
    .left-box {
      font-size: 16px;
      color: #30343a;
      line-height: 22px;
    }
    .right-box {
      font-size: 24px;
      color: #30343a;
      line-height: 33px;
      .t1 {
        font-weight: bold;
      }
      .t2 {
        padding: 0 4px;
      }
      .t3 {
        font-size: 16px;
      }
    }
  }
}

.agent-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-left: 1px solid #e5e7eb;
  background: #f7f8fa;
  transition: all 0.3s ease;
  margin-left: 0;
}

.agent-content-collapsed {
  border-left: none;
}

// .agent-header {
//   padding: 16px 24px;
//   border-bottom: 1px solid #e5e7eb;
//   display: flex;
//   justify-content: space-between;
//   align-items: center;
// }

// .agent-title {
//   font-size: 20px;
// }

.agent-content-body {
  flex: 1;
  overflow: auto;
}

.btn-create {
  width: 100%;
  height: 40px;
  border-radius: 8px;
  border: 1px solid #dadde8;
}
</style>
