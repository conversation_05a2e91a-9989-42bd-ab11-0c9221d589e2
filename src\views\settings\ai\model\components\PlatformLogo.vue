<script setup lang="ts">
import { getProviderLogo } from '@/utils/platforms'
defineOptions({
  name: 'PlatformLogo',
})

const props = withDefaults(
  defineProps<{
    /**
     * 平台名称
     */
    platformName?: string
    /**
     * logo 地址
     */
    logoSrc?: string
    /**
     * logo 大小
     */
    size?: number
    loading?: boolean
  }>(),
  {
    size: 80,
    loading: false,
  },
)

const customLogo = computed(() => {
  if (props.logoSrc) {
    return props.logoSrc
  }
  return getProviderLogo(props.platformName || '')
})

/**
 * 字体大小
 */
const fontSize = computed(() => {
  return props.size / 2
})

/**
 * 获取平台的第一个字符
 */
function getInitials() {
  return props.platformName?.charAt(0).toUpperCase() || 'P'
}

/**
 * 生成平台logo背景颜色
 */
const backgroundColor = computed(() => {
  // 使用字符的Unicode值作为随机种子
  const seed = getInitials().charCodeAt(0)

  // 使用简单的线性同余生成器创建伪随机数
  const a = 1664525
  const c = **********
  const m = Math.pow(2, 32)

  // 生成三个伪随机数作为RGB值
  let r = (a * seed + c) % m
  let g = (a * r + c) % m
  let b = (a * g + c) % m

  // 将伪随机数转换为0-255范围内的整数
  r = Math.floor((r / m) * 256)
  g = Math.floor((g / m) * 256)
  b = Math.floor((b / m) * 256)

  // 返回十六进制颜色字符串
  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`
})
</script>

<template>
  <div
    class="inline-block rounded-lg overflow-hidden align-middle"
    :style="{ width: size + 'px', height: size + 'px' }"
    v-loading="loading"
  >
    <img v-if="customLogo" class="w-full h-full" :src="customLogo" alt="" />
    <div
      v-else
      class="platform-logo-letter"
      :style="{
        backgroundColor: backgroundColor,
        fontSize: fontSize + 'px',
        lineHeight: size + 'px',
      }"
    >
      {{ getInitials() }}
    </div>
  </div>
</template>

<style scoped>
.platform-logo-letter {
  width: 100%;
  height: 100%;
  color: #fff;
  text-align: center;
  font-weight: 600;
}
</style>
