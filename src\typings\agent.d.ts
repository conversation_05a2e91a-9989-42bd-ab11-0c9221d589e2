declare namespace Agent {
  /**
   * AgentVo
   */
  interface AgentVo {
    /**
     * 背景色
     */
    backgroundColor?: string
    /**
     * 类型id 查询用
     */
    categoryId?: string
    /**
     * 类型id列表
     */
    categoryIdList?: string[]
    /**
     * 描述
     */
    description?: string
    /**
     * 图标
     */
    emoji?: string
    id?: string
    /**
     * 是否私人助手
     */
    isPrivate?: boolean
    /**
     * 模型id
     */
    modelId?: number | string
    /**
     * 名称
     */
    name?: string
    /**
     * 提示词
     */
    prompt?: string
    /**
     * 状态 -1-私人助手 0-审核中 1-审核通过 2-审核拒绝
     */
    status?: number | string
    [property: string]: any
  }

  /**
   * AgentCategoryVo
   */
  interface AgentCategoryVo {
    /**
     * 助手数量
     */
    agentNum?: number
    /**
     * id
     */
    id?: string
    /**
     * 类型
     */
    name?: string
    /**
     * 排序号
     */
    orderNum?: number
    [property: string]: any
  }

  /**
   * AgentEventRecordVo
   */
  interface AgentEventRecordVo {
    createTime?: Date
    createUserName?: string
    /**
     * 描述
     */
    description?: string
    /**
     * id
     */
    id?: string
    agentId?: string
    /**
     * 状态 -1-私人助手 0-审核中 1-审核通过 2-审核拒绝
     */
    status?: number
    /**
     * 版本id
     */
    versionId?: number
    [property: string]: any
  }

  // /biz/agent/agentStoreList
  interface AgentStoreListRequest {
    /**
     * 背景色
     */
    backgroundColor?: string
    /**
     * 类型id 查询用
     */
    categoryId?: number
    /**
     * 类型id列表
     */
    categoryIdList?: number[]
    /**
     * 描述
     */
    description?: string
    /**
     * 图标
     */
    emoji?: string
    firstNum?: number
    id?: string
    /**
     * 排序的方向desc或者asc
     */
    isAsc?: string
    /**
     * 是否私人助手
     */
    isPrivate?: boolean
    keyword?: string
    /**
     * 模型id
     */
    modelId?: number | string
    /**
     * 名称
     */
    name?: string
    /**
     * 排序列
     */
    orderByColumn?: string
    /**
     * 当前页数
     */
    pageNum?: number
    /**
     * 分页大小
     */
    pageSize?: number
    /**
     * 提示词
     */
    prompt?: string
    /**
     * 状态 -1-私人助手 0-审核中 1-审核通过 2-审核拒绝
     */
    status?: number
    [property: string]: any
  }

  interface AgentStatusCountVo {
    /**
     * 数量
     */
    count?: number
    /**
     * 状态 -1-私人助手 0-审核中 1-审核通过 2-审核拒绝
     */
    status?: number
    [property: string]: any
  }
}
