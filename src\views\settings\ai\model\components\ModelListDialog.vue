<script setup lang="ts">
import { getModelLogo } from '@/utils/models'
import { modelTypeList } from '../constant/index'
import { deleteBizAiModel, getBizAiPlatformModels, postBizAiModelAdd } from '@/api/model'
import { AIModelVo } from '@/api'
import { vAutoAnimate } from '@formkit/auto-animate/vue'

defineOptions({
  name: 'ModelListDialog',
})
const emits = defineEmits(['updateModelList'])
const dialogVisible = defineModel({ default: false })
const props = defineProps<{
  platformId?: string | number
  textModelList: AIModelVo[]
  embeddingModelList: AIModelVo[]
  rerankModelList: AIModelVo[]
}>()

type ModelItem = AIModelVo & {
  /**
   * 是否是已存在的模型
   */
  presence: boolean
  operate: 'add' | 'delete' | null
}

type ModelList = [ModelItem[], ModelItem[]]
type ModleType = 'text' | 'embedding' | 'rerank'

const textModelList = ref<ModelList>([[], []])

const embeddingModelList = ref<ModelList>([[], []])

const rerankModelList = ref<ModelList>([[], []])

/**
 * 搜索功能组合函数
 * @returns 包含搜索关键词和过滤后平台数据的响应式对象
 */
function useSearch() {
  const search = ref<string>('')
  // 移除空格并转换为小写
  const searchTerm = computed(() => search.value.trim().toLowerCase())
  const filter = (items: ModelItem[]) =>
    items.filter(
      item =>
        item.displayName.toLowerCase().includes(searchTerm.value) ||
        item.name.toLowerCase().includes(searchTerm.value),
    )

  // 搜素后的 大语言模型列表
  const filteredTextModelList = computed<ModelList>(() => {
    if (!searchTerm.value) {
      return textModelList.value // 如果搜索词为空，返回所有平台
    }
    const [existList, newList] = textModelList.value

    return [filter(existList), filter(newList)]
  })

  // 搜索后的 向量模型列表
  const filteredEmbeddingModelList = computed<ModelList>(() => {
    if (!searchTerm.value) {
      return embeddingModelList.value // 如果搜索词为空，返回所有平台
    }
    const [existList, newList] = embeddingModelList.value

    return [filter(existList), filter(newList)]
  })

  // 搜索后的 重排模型列表
  const filteredRarankModelList = computed<ModelList>(() => {
    if (!searchTerm.value) {
      return rerankModelList.value // 如果搜索词为空，返回所有平台
    }
    const [existList, newList] = rerankModelList.value

    return [filter(existList), filter(newList)]
  })

  return {
    search,
    filteredTextModelList,
    filteredEmbeddingModelList,
    filteredRarankModelList,
  }
}

const { search, filteredTextModelList, filteredEmbeddingModelList, filteredRarankModelList } =
  useSearch()

const modelTypeListWithModelList = computed(() => {
  return modelTypeList.map(item => {
    let list: ModelList = [[], []]
    switch (item.value) {
      case 'text':
        list = filteredTextModelList.value
        break
      case 'embedding':
        list = filteredEmbeddingModelList.value
        break
      case 'rerank':
        list = filteredRarankModelList.value
        break
      default:
        break
    }

    return {
      ...item,
      list,
      total: list[0].length + list[1].length,
    }
  })
})

const loading = ref(false)

const active = ref<ModleType>('text')

const modelLIstByActive = computed(() => {
  switch (active.value) {
    case 'text':
      return textModelList.value
    case 'embedding':
      return embeddingModelList.value
    case 'rerank':
      return rerankModelList.value
    default:
      return []
  }
})

interface PlatformModel {
  id: string
  name: string
  ownedBy: string
  type: string
  platform: string
  displayName: string
  capabilities: string[]
}
function handlePlatformModelList(modelist: PlatformModel[], type: ModleType) {
  let existModelList = []
  switch (type) {
    case 'text':
      existModelList = props.textModelList
      break
    case 'embedding':
      existModelList = props.embeddingModelList
      break
    case 'rerank':
      existModelList = props.rerankModelList
      break
  }
  return modelist
    .filter(item => item.type === type)
    .map(item => {
      return {
        displayName: item.displayName,
        id: item.id,
        name: item.name,
        type: type,
        platformId: props.platformId,
        capabilities: item.capabilities,
      }
    })
    .filter(item => !existModelList.some(model => model.name === item.name))
    .map(item => ({
      ...item,
      presence: false,
      operate: null,
    }))
}

/**
 * 获取平台可用列表
 */
function getPlatformModelList() {
  if (!props.platformId) return
  loading.value = true
  getBizAiPlatformModels<PlatformModel[]>(props.platformId)
    .then(res => {
      // 分类
      const modelList = res.data || []

      const platformTextModelList = handlePlatformModelList(modelList, 'text')
      const existTextModelList = props.textModelList.map(item => ({
        ...item,
        presence: true,
        operate: null,
      }))
      textModelList.value = [existTextModelList, platformTextModelList]

      const platformEmbeddingModelList = handlePlatformModelList(modelList, 'embedding')
      const existEmbeddingModelList = props.embeddingModelList.map(item => ({
        ...item,
        presence: true,
        operate: null,
      }))
      embeddingModelList.value = [existEmbeddingModelList, platformEmbeddingModelList]

      const platformRerankModelList = handlePlatformModelList(modelList, 'rerank')
      const existRarankModelList = props.rerankModelList.map(item => ({
        ...item,
        presence: true,
        operate: null,
      }))
      rerankModelList.value = [existRarankModelList, platformRerankModelList]
    })
    .finally(() => {
      loading.value = false
    })
}

watch(dialogVisible, val => {
  if (val) {
    getPlatformModelList()
  }
})

/**
 * 减少模型
 */
function handleNotWant(name: string) {
  if (!modelLIstByActive.value) {
    return
  }
  const [existList, newList] = modelLIstByActive.value
  const index = existList.findIndex(item => item.name === name)
  const subModelList = existList.splice(index, 1)
  subModelList.forEach(item => {
    item.operate = 'delete'
  })
  newList.unshift(...subModelList)
}

/**
 * 增加模型
 */
function handleGetWant(name: string) {
  if (!modelLIstByActive.value) {
    return
  }
  const [existList, newList] = modelLIstByActive.value
  const index = newList.findIndex(item => item.name === name)
  const addModelList = newList.splice(index, 1)
  addModelList.forEach(item => {
    item.operate = 'add'
  })

  existList.push(...addModelList)
}

function handleBeforeClose(done: () => void) {
  if (!submitLoading.value) {
    done()
  }
}
/**
 * 关闭弹窗
 */
function handleClose() {
  search.value = ''
  textModelList.value = [[], []]
  embeddingModelList.value = [[], []]
  rerankModelList.value = [[], []]
  active.value = 'text'
}

const submitLoading = ref(false)
/**
 * 保存模型
 */
async function handleSaveModelList() {
  try {
    // 处理所有模型类型（text/embedding/rerank）
    const allOperations = modelTypeListWithModelList.value.flatMap(tab => {
      const [existList, newList] = tab.list
      return {
        type: tab.value,
        toAdd: existList.filter(m => m.operate === 'add' && !m.presence),
        toDelete: newList.filter(m => m.operate === 'delete' && m.presence),
      }
    })

    const allPromise = allOperations.flatMap(({ type, toAdd, toDelete }) => [
      ...toDelete.map(m => deleteBizAiModel(m.id)),
      ...toAdd.map(m =>
        postBizAiModelAdd({
          platformId: props.platformId,
          type: type,
          name: m.name,
          displayName: m.displayName,
          capabilities: m.capabilities || [],
        }),
      ),
    ])

    submitLoading.value = true

    // 执行批量操作
    const allResults = await Promise.allSettled(allPromise)

    // 处理操作结果
    const failedResults = allResults.filter(r => r.status === 'rejected' || r.value.code !== 200)
    if (failedResults.length > 0) {
      const errorMsg = `部分操作失败 (${failedResults.length}/${allResults.length})`
      throw new Error(errorMsg)
    }

    ElMessage.success('保存成功')
    dialogVisible.value = false
    emits('updateModelList')
  } catch (err) {
    // ElMessage.error(`保存失败: ${err.message}`)
    console.error('保存异常:', err)
  } finally {
    submitLoading.value = false
  }
}
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    width="600"
    :close-on-click-modal="false"
    :beforeClose="handleBeforeClose"
    @close="handleClose"
  >
    <template #header>
      <span class="text-[24px] g-family-medium">模型列表</span>
    </template>
    <!-- 搜索框 -->
    <el-form class="mb-[15px]">
      <el-input v-model="search" placeholder="搜索模型" clearable style="width: 100%">
        <template #suffix>
          <el-icon>
            <Search />
          </el-icon>
        </template>
      </el-input>
    </el-form>

    <el-tabs v-model="active">
      <el-tab-pane
        v-for="item in modelTypeListWithModelList"
        :label="`${item.label}（${item.total}）`"
        :name="item.value"
      >
        <div class="h-[405px] overflow-auto" v-loading="loading">
          <div v-auto-animate>
            <div
              v-for="existModel in item.list[0]"
              :key="existModel.name"
              class="model-list-item minus"
            >
              <div class="felx-1 model-item">
                <img
                  class="size-10 inline-block mr-2 rounded-lg"
                  :src="getModelLogo(existModel.name)"
                  alt=""
                />
                <span class="text-[16px] g-family-medium truncate">{{
                  existModel.displayName
                }}</span>
                <template v-if="active === 'text'">
                  <el-tooltip
                    v-if="existModel.capabilities?.includes('reasoning')"
                    effect="dark"
                    placement="top"
                    content="推理"
                  >
                    <img
                      class="inline-block w-[32px] h-[20px] mr-2"
                      src="@/assets/model/tuili.svg"
                      alt=""
                    />
                  </el-tooltip>

                  <el-tooltip
                    v-if="existModel.capabilities?.includes('vision')"
                    effect="dark"
                    placement="top"
                    content="视觉"
                  >
                    <img
                      class="inline-block w-[32px] h-[20px] mr-2"
                      src="@/assets/model/shijue.svg"
                      alt=""
                    />
                  </el-tooltip>

                  <el-tooltip
                    v-if="existModel.capabilities?.includes('toolCalling')"
                    effect="dark"
                    placement="top"
                    content="工具"
                  >
                    <img
                      class="inline-block w-[32px] h-[20px] mr-2"
                      src="@/assets/model/gongju.svg"
                      alt=""
                    />
                  </el-tooltip>
                </template>
              </div>
              <el-icon class="cursor-pointer" size="20" @click="handleNotWant(existModel.name)"
                ><Minus
              /></el-icon>
            </div>

            <div v-for="newModel in item.list[1]" :key="newModel.name" class="model-list-item">
              <div class="felx-1 model-item">
                <img
                  class="size-10 inline-block rounded-lg"
                  :src="getModelLogo(newModel.name)"
                  alt=""
                />
                <span class="mr-3 text-[16px] g-family-medium truncate">{{
                  newModel.displayName
                }}</span>

                <template v-if="active === 'text'">
                  <el-tooltip
                    v-if="newModel.capabilities?.includes('reasoning')"
                    effect="dark"
                    placement="top"
                    content="推理"
                  >
                    <img
                      class="inline-block w-[32px] h-[20px] mr-2"
                      src="@/assets/model/tuili.svg"
                      alt=""
                    />
                  </el-tooltip>

                  <el-tooltip
                    v-if="newModel.capabilities?.includes('vision')"
                    effect="dark"
                    placement="top"
                    content="视觉"
                  >
                    <img
                      class="inline-block w-[32px] h-[20px] mr-2"
                      src="@/assets/model/shijue.svg"
                      alt=""
                    />
                  </el-tooltip>

                  <el-tooltip
                    v-if="newModel.capabilities?.includes('toolCalling')"
                    effect="dark"
                    placement="top"
                    content="工具"
                  >
                    <img
                      class="inline-block w-[32px] h-[20px] mr-2"
                      src="@/assets/model/gongju.svg"
                      alt=""
                    />
                  </el-tooltip>
                </template>
              </div>
              <el-icon class="cursor-pointer" size="20" @click="handleGetWant(newModel.name)"
                ><Plus
              /></el-icon>
            </div>
          </div>

          <el-empty v-if="!item.total" />
        </div>
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <div class="dialog-footer">
        <el-button :disabled="submitLoading" @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSaveModelList">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<style lang="less" scoped>
.model-list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  padding: 0.5rem;

  .model-item {
    display: grid;
    grid-template-columns: 40px 1fr auto repeat(3, auto);
    gap: 8px;
    align-items: center;
  }

  &.minus {
    background-color: rgba(230, 234, 244, 0.35);
  }
}
</style>
