<script setup lang="ts">
// 骨架屏组件，用于在加载消息时显示
</script>

<template>
  <div class="message-skeleton">
    <div class="flex items-start gap-3">
      <div class="flex-shrink-0">
        <div class="w-10 h-10 rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
      </div>
      <div class="flex-1">
        <div class="flex items-center gap-2 mb-2">
          <div class="h-4 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
          <div class="h-4 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
        </div>
        <div class="space-y-2">
          <div class="h-4 w-full bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
          <div class="h-4 w-3/4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
          <div class="h-4 w-1/2 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.message-skeleton {
  width: 80%;
  padding: 1rem;
  margin-bottom: 1rem;
  border-radius: 0.5rem;
  background-color: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10px);

  .dark & {
    background-color: rgba(0, 0, 0, 0.5);
  }
}
</style>
