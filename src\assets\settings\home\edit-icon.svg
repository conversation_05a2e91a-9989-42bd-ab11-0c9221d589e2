<?xml version="1.0" encoding="UTF-8"?>
<svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>矩形</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="设置-首页" transform="translate(-1098.000000, -267.000000)" fill-rule="nonzero">
            <g id="编辑备份" transform="translate(1098.000000, 267.000000)">
                <rect id="矩形" x="0" y="0" width="40" height="40" rx="4"></rect>
                <path d="M30.8037427,7.80157981 C31.0513336,7.54491494 31.4181878,7.44188625 31.7632033,7.53212136 C32.1082187,7.62235648 32.3776567,7.89180064 32.4678842,8.2368183 C32.5581116,8.58183596 32.4550749,8.94868812 32.1984047,9.19627351 L18.2504901,23.1445051 C18.0012971,23.3936982 17.6380905,23.4910192 17.2976866,23.3998082 C16.9572827,23.3085972 16.691397,23.0427113 16.6001861,22.7023072 C16.5089751,22.3619031 16.6062961,21.9986963 16.855489,21.7495032 L30.8037119,7.80188804 L30.8037427,7.80157981 Z M30.5146818,18.0335975 C30.5146818,17.4888535 30.9562838,17.0472512 31.5010275,17.0472512 C32.0457711,17.0472512 32.4873732,17.4888535 32.4873732,18.0335975 L32.4873732,28.8834071 C32.4873732,30.8807584 30.8681326,32.5 28.8707825,32.5 L11.1165906,32.5 C9.11924059,32.5 7.5,30.8807584 7.5,28.8834071 L7.5,11.1291732 C7.5,9.13182194 9.11924059,7.51258033 11.1165906,7.51258033 L21.6376012,7.51258033 C22.1823449,7.51258033 22.6239469,7.95418262 22.6239469,8.49892666 C22.6239469,9.04367069 22.1823449,9.48527298 21.6376012,9.48527298 L11.1165906,9.48527298 C10.2086902,9.48527298 9.47269141,10.2212722 9.47269141,11.1291732 L9.47269141,28.8834071 C9.47269141,29.7913081 10.2086902,30.5273074 11.1165906,30.5273074 L28.8708133,30.5273074 C29.7787138,30.5273074 30.5147126,29.7913081 30.5147126,28.8834071 L30.5147126,18.0335975 L30.5146818,18.0335975 Z" id="形状" fill="#4865E8"></path>
            </g>
        </g>
    </g>
</svg>