<!doctype html>
<html>
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta content="yes" name="apple-mobile-web-app-capable" />
    <link rel="apple-touch-icon" href="/favicon.ico" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover"
    />
    <meta name="screen-orientation" content="portrait" />
    <meta name="x5-orientation" content="portrait" />
    <meta name="full-screen" content="yes" />
    <meta name="x5-fullscreen" content="true" />
    <meta name="360-fullscreen" content="true" />
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <title>Ease AI</title>
  </head>

  <body class="dark:bg-black">
    <div id="app">
      <style>
        .loading-wrap {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 100vh;
        }

        .balls {
          width: 4em;
          display: flex;
          flex-flow: row nowrap;
          align-items: center;
          justify-content: space-between;
        }

        .balls div {
          width: 0.8em;
          height: 0.8em;
          border-radius: 50%;
          background-color: #4c5cec;
        }

        .balls div:nth-of-type(1) {
          transform: translateX(-100%);
          animation: left-swing 0.5s ease-in alternate infinite;
        }

        .balls div:nth-of-type(3) {
          transform: translateX(-95%);
          animation: right-swing 0.5s ease-out alternate infinite;
        }

        @keyframes left-swing {
          50%,
          100% {
            transform: translateX(95%);
          }
        }

        @keyframes right-swing {
          50% {
            transform: translateX(-95%);
          }

          100% {
            transform: translateX(100%);
          }
        }

        @media (prefers-color-scheme: dark) {
          body {
            background: #121212;
          }
        }
      </style>
      <div class="loading-wrap">
        <div class="balls">
          <div></div>
          <div></div>
          <div></div>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
