<script setup lang="ts">
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'
import { Icon } from '@iconify/vue'
import { ElButton, ElDialog, ElIcon, ElMessage } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
import CryptoJS from 'crypto-js'
import {
  type CaptchaGetResult,
  checkCaptchaVerification,
  getCaptcha,
} from '../LoginDialog/utils/api'

interface Props {
  visible?: boolean
  width?: number
  height?: number
  sliderText?: string
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'success', data: any): void
  (e: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  width: 400,
  height: 200,
  sliderText: '拖动滑块完成拼图',
})

const emit = defineEmits<Emits>()

// 计算属性处理双向绑定
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value),
})

// 响应式数据
const loading = ref(false)
const refreshLoading = ref(false)
const canvasWidth = ref(props.width)
const canvasHeight = ref(props.height)
const blockSize = ref(60) // 拼图块显示宽度
const jigsawWidth = ref(0) // 拼图块实际宽度
const jigsawHeight = ref(0) // 拼图块实际高度
const blockLeft = ref(0)
const sliderProgress = ref(0)
const btnLeft = ref(0) // 添加滑块按钮位置变量
const isSliding = ref(false)
const verifyResult = ref<'success' | 'fail' | ''>('')

// DOM引用
const canvasRef = ref<HTMLCanvasElement>()
const blockRef = ref<HTMLCanvasElement>()
const sliderTrackRef = ref<HTMLDivElement>()

// 验证数据
const captchaData = ref<CaptchaGetResult['repData']>()
const startX = ref(0)
const startTime = ref(0)
const trail = ref<{ x: number; y: number; t: number }[]>([])

// AES加密函数
const aesEncrypt = (word: string, keyWord = 'XwKsGlMcdPMEhR1B'): string => {
  const key = CryptoJS.enc.Utf8.parse(keyWord)
  const srcs = CryptoJS.enc.Utf8.parse(word)
  const encrypted = CryptoJS.AES.encrypt(srcs, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7,
  })
  return encrypted.toString()
}

// 添加性能优化变量
let animationFrameId: number | null = null
const isAnimating = ref(false)

// 绘制验证
const drawCaptcha = async () => {
  console.log('drawCaptcha called')
  console.log('canvasRef.value', canvasRef.value)
  console.log('blockRef.value', blockRef.value)
  console.log('captchaData.value exists', !!captchaData.value)

  if (!captchaData.value) {
    console.log('No captcha data')
    return
  }

  // 等待DOM更新
  await nextTick()

  console.log('After nextTick - canvasRef.value', canvasRef.value)
  console.log('After nextTick - blockRef.value', blockRef.value)

  if (!canvasRef.value || !blockRef.value) {
    console.log('Canvas elements not ready, retrying...')
    // 如果canvas还没准备好，等待一下再试
    setTimeout(() => drawCaptcha(), 100)
    return
  }

  const canvas = canvasRef.value
  const blockCanvas = blockRef.value
  const ctx = canvas.getContext('2d')
  const blockCtx = blockCanvas.getContext('2d')

  if (!ctx || !blockCtx) {
    console.log('Failed to get canvas context')
    return
  }

  console.log('Canvas contexts ready, drawing...')

  // 清空画布
  ctx.clearRect(0, 0, canvasWidth.value, canvasHeight.value)
  blockCtx.clearRect(0, 0, blockSize.value, canvasHeight.value)

  // 绘制背景图
  const bgImg = new Image()
  bgImg.crossOrigin = 'anonymous'
  bgImg.onload = () => {
    console.log('Background image loaded')
    console.log('Background image size:', bgImg.width, 'x', bgImg.height)

    // 确保图像按照canvas的尺寸进行缩放
    ctx.drawImage(bgImg, 0, 0, canvasWidth.value, canvasHeight.value)

    // 计算缩放比例
    const scaleX = canvasWidth.value / bgImg.width
    const scaleY = canvasHeight.value / bgImg.height

    // 绘制拼图块
    const jigsawImg = new Image()
    jigsawImg.crossOrigin = 'anonymous'
    jigsawImg.onload = () => {
      console.log('Jigsaw image loaded')
      console.log('Jigsaw image size:', jigsawImg.width, 'x', jigsawImg.height)

      // 保存拼图块原始尺寸
      jigsawWidth.value = jigsawImg.width
      jigsawHeight.value = jigsawImg.height

      // 按照与背景图相同的缩放比例缩放拼图块
      const scaledJigsawWidth = jigsawImg.width * scaleX
      const scaledJigsawHeight = jigsawImg.height * scaleY

      // 调整blockSize以匹配缩放后的拼图宽度
      blockSize.value = scaledJigsawWidth

      // 重新设置拼图块canvas的尺寸
      blockCanvas.width = blockSize.value

      // 绘制拼图块
      blockCtx.drawImage(jigsawImg, 0, 0, blockSize.value, canvasHeight.value)

      // 设置拼图块初始位置
      blockLeft.value = 0
      btnLeft.value = 0
      sliderProgress.value = 0
      verifyResult.value = ''

      console.log('Scaled jigsaw size:', blockSize.value, 'x', canvasHeight.value)
    }
    jigsawImg.onerror = error => {
      console.error('Failed to load jigsaw image:', error)
    }
    jigsawImg.src = `data:image/png;base64,${captchaData.value!.jigsawImageBase64}`
  }
  bgImg.onerror = error => {
    console.error('Failed to load background image:', error)
  }
  bgImg.src = `data:image/png;base64,${captchaData.value.originalImageBase64}`
}

const clientId = import.meta.env.VITE_APP_CLIENT_ID

// 初始化验证
const initCaptcha = async () => {
  try {
    loading.value = true
    const response = await getCaptcha({
      captchaType: 'blockPuzzle',
      clientUid: clientId,
    })
    if (response.data.success) {
      console.log('response', response)
      captchaData.value = response.data.repData
      // 等待DOM更新完成后再绘制
      loading.value = false
      await nextTick()
      await drawCaptcha()
    } else {
      ElMessage.error('获取验证失败')
      loading.value = false
    }
  } catch (error) {
    console.error('获取验证失败:', error)
    ElMessage.error('获取验证失败，请重试')
    loading.value = false
  }
}

// 开始滑动
const startSliding = (clientX: number) => {
  isSliding.value = true
  startX.value = clientX
  startTime.value = Date.now()
  trail.value = []
}

// 优化后的更新滑块位置函数
const updateSliderPosition = (clientX: number) => {
  if (!isSliding.value || !sliderTrackRef.value) return

  // 如果已经在动画中，取消之前的动画帧
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId)
  }

  // 使用 requestAnimationFrame 优化性能
  animationFrameId = requestAnimationFrame(() => {
    if (!isSliding.value || !sliderTrackRef.value) return

    const deltaX = clientX - startX.value
    const sliderBtnWidth = 38 // 滑块按钮宽度
    const maxDistance = sliderTrackRef.value.offsetWidth - sliderBtnWidth // 可滑动的最大距离

    // 计算滑块按钮的左侧位置（相对于滑动轨道）
    const newBtnLeft = Math.max(0, Math.min(deltaX, maxDistance))

    // 批量更新位置，减少响应式触发次数
    btnLeft.value = newBtnLeft
    sliderProgress.value = newBtnLeft + sliderBtnWidth

    // 计算拼图块位置
    const maxBlockMove = canvasWidth.value - blockSize.value
    blockLeft.value = (newBtnLeft / maxDistance) * maxBlockMove

    // 降低轨迹记录频率，避免过于频繁的数组操作
    const now = Date.now()
    if (trail.value.length === 0 || now - trail.value[trail.value.length - 1].t > 16) {
      // 约60fps
      trail.value.push({
        x: newBtnLeft,
        y: 0,
        t: now - startTime.value,
      })
    }

    animationFrameId = null
  })
}

// 重置验证
const resetCaptcha = () => {
  verifyResult.value = ''
  sliderProgress.value = 0
  btnLeft.value = 0
  blockLeft.value = 0
  trail.value = []
}

// 停止滑动并验证
const stopSliding = async () => {
  if (!isSliding.value || !captchaData.value) return

  // 清理动画帧
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId)
    animationFrameId = null
  }

  isSliding.value = false

  try {
    // 按照参考组件的方式计算距离
    // 先获取实际滑动的像素距离，然后按照标准比例310px进行缩放
    let moveLeftDistance = blockLeft.value
    moveLeftDistance = (moveLeftDistance * 310) / canvasWidth.value

    // 确保x坐标使用四舍五入取整，更准确地匹配服务器期望的位置
    // resolved 参考 VerifySlide.vue 的 pointJson 处理方式
    const pointJson = captchaData.value.secretKey
      ? aesEncrypt(
          JSON.stringify({ x: Math.round(moveLeftDistance), y: 5 }),
          captchaData.value.secretKey,
        )
      : JSON.stringify({ x: Math.round(moveLeftDistance), y: 5 })

    console.log('Sending verification with coordinates:', pointJson)

    // resolved 按src\components\common\LoginDialog\utils\api.ts的CaptchaCheckRequest补充一下checkCaptchaVerification的入参
    const response = await checkCaptchaVerification({
      captchaType: 'blockPuzzle',
      pointJson,
      token: captchaData.value.token,
      clientUid: clientId,
      ts: Date.now().toString(),
    })

    if (response.data.success) {
      verifyResult.value = 'success'
      setTimeout(() => {
        // 组合返回的验证数据，包含坐标、token和secretKey信息
        const successData = {
          ...response.data,
          x: Math.round(moveLeftDistance),
          y: 5,
          token: captchaData.value!.token,
          secretKey: captchaData.value!.secretKey,
        }
        emit('success', successData)
      }, 500)
    } else {
      verifyResult.value = 'fail'
      setTimeout(() => {
        resetCaptcha()
        // 验证失败后重新获取新的验证码
        initCaptcha()
      }, 1000)
    }
  } catch (error) {
    console.error('验证失败:', error)
    verifyResult.value = 'fail'
    setTimeout(() => {
      resetCaptcha()
      // 验证失败后重新获取新的验证码
      initCaptcha()
    }, 1000)
  }
}

// 鼠标事件处理函数声明
const handleMouseMove = (e: MouseEvent) => {
  updateSliderPosition(e.clientX)
}

const handleMouseUp = () => {
  stopSliding()
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
}

// 触摸事件处理函数声明
const handleTouchMove = (e: TouchEvent) => {
  e.preventDefault()
  updateSliderPosition(e.touches[0].clientX)
}

const handleTouchEnd = () => {
  stopSliding()
  document.removeEventListener('touchmove', handleTouchMove)
  document.removeEventListener('touchend', handleTouchEnd)
}

// 鼠标事件处理
const handleMouseDown = (e: MouseEvent) => {
  if (verifyResult.value) return
  startSliding(e.clientX)
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

// 触摸事件处理
const handleTouchStart = (e: TouchEvent) => {
  if (verifyResult.value) return
  e.preventDefault()
  startSliding(e.touches[0].clientX)
  document.addEventListener('touchmove', handleTouchMove, { passive: false })
  document.addEventListener('touchend', handleTouchEnd)
}

// 刷新验证
const handleRefresh = () => {
  refreshLoading.value = true
  resetCaptcha()
  setTimeout(() => {
    initCaptcha().finally(() => {
      refreshLoading.value = false
    })
  }, 300)
}

// 关闭验证
const handleClose = () => {
  dialogVisible.value = false
  emit('close')
}

// 监听visible变化
watch(
  () => props.visible,
  async newValue => {
    if (newValue) {
      resetCaptcha()
      // 确保DOM完全渲染后再初始化
      await nextTick()
      // 增加延迟时间，确保DOM完全准备好
      setTimeout(() => {
        initCaptcha()
      }, 100)
    }
  },
  { immediate: true }, // 添加immediate选项，确保首次渲染时也能正确执行
)

// 监听canvas尺寸变化，重新绘制
watch([canvasWidth, canvasHeight, blockSize], async () => {
  if (props.visible && captchaData.value) {
    await nextTick()
    drawCaptcha()
  }
})

// 组件挂载时初始化
onMounted(() => {
  // 组件挂载时不再主动初始化，由watch处理
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  // 清理动画帧
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId)
    animationFrameId = null
  }

  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
  document.removeEventListener('touchmove', handleTouchMove)
  document.removeEventListener('touchend', handleTouchEnd)
})
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    title="安全验证"
    width="440px"
    align-center
    destroy-on-close
    @close="handleClose"
  >
    <div class="slide-captcha-content">
      <div v-if="loading" class="captcha-loading">
        <ElIcon class="is-loading">
          <Loading />
        </ElIcon>
        <span>加载中...</span>
      </div>

      <div v-else class="captcha-main">
        <!-- 主图区域 -->
        <div class="captcha-image-container">
          <canvas
            ref="canvasRef"
            :width="canvasWidth"
            :height="canvasHeight"
            class="captcha-canvas"
          ></canvas>
          <!-- 拼图小块 -->
          <canvas
            ref="blockRef"
            :width="blockSize"
            :height="canvasHeight"
            class="captcha-block"
            :style="{ left: `${blockLeft}px` }"
          ></canvas>
        </div>

        <!-- 滑动条区域 -->
        <div class="captcha-slider">
          <div ref="sliderTrackRef" class="slider-track">
            <div class="slider-track-bg"></div>
            <div class="slider-progress" :style="{ width: `${sliderProgress}px` }"></div>
            <div
              class="slider-btn"
              :style="{ left: `${btnLeft}px` }"
              :class="{
                'slider-btn-success': verifyResult === 'success',
                'slider-btn-fail': verifyResult === 'fail',
                'slider-btn-active': isSliding,
              }"
              @mousedown="handleMouseDown"
              @touchstart="handleTouchStart"
            >
              <Icon
                v-if="verifyResult === 'success'"
                icon="material-symbols:check"
                class="slider-icon success-icon"
              />
              <Icon
                v-else-if="verifyResult === 'fail'"
                icon="material-symbols:close"
                class="slider-icon fail-icon"
              />
              <Icon v-else icon="material-symbols:arrow-forward-ios" class="slider-icon" />
            </div>
          </div>
          <div class="slider-text">
            <span v-if="verifyResult === 'success'" class="success-text">验证成功</span>
            <span v-else-if="verifyResult === 'fail'" class="fail-text">验证失败，请重试</span>
            <span v-else>{{ sliderText }}</span>
          </div>
        </div>

        <!-- 刷新按钮 -->
        <div class="captcha-refresh">
          <!-- resolved 这里用div代替ElButton -->
          <div
            class="refresh-btn"
            :class="{ 'is-loading': refreshLoading }"
            @click="!refreshLoading && handleRefresh()"
          >
            <Icon v-if="refreshLoading" icon="eos-icons:loading" class="loading-icon" />
            <Icon v-else icon="material-symbols:refresh" />
          </div>
        </div>
      </div>
    </div>
  </ElDialog>
</template>

<style scoped lang="less">
.slide-captcha-content {
  padding: 0;
  display: flex;
  justify-content: center;
}

.captcha-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 240px;
  color: #666;

  .is-loading {
    font-size: 24px;
    margin-bottom: 8px;
  }
}

.captcha-main {
  position: relative;
}

.captcha-image-container {
  position: relative;
  margin-bottom: 20px;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.captcha-canvas {
  display: block;
  background: #f5f5f5;
}

.captcha-block {
  position: absolute;
  top: 0;
  transition: none;
  pointer-events: none;
  will-change: transform;
  transform: translateZ(0);
}

.captcha-slider {
  position: relative;
  margin-bottom: 16px;
}

.slider-track {
  position: relative;
  height: 40px;
  border-radius: 20px;
  overflow: hidden;
  cursor: pointer;
}

.slider-track-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #f7f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 20px;
}

.slider-progress {
  position: absolute;
  top: 1px;
  left: 1px;
  bottom: 1px;
  background: linear-gradient(90deg, #4865e8 0%, #5c7cfa 100%);
  border-radius: 19px;
  will-change: width;
}

.slider-btn {
  position: absolute;
  top: 1px;
  width: 38px;
  height: 38px;
  background: white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  cursor: grab;
  display: flex;
  align-items: center;
  justify-content: center;
  will-change: transform, left;
  transform: translateZ(0);

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  &.slider-btn-active {
    cursor: grabbing;
    transform: scale(1.05) translateZ(0);
  }

  &.slider-btn-success {
    background: #67c23a;
    cursor: default;

    .success-icon {
      color: white;
    }
  }

  &.slider-btn-fail {
    background: #f56c6c;
    cursor: default;

    .fail-icon {
      color: white;
    }
  }
}

.slider-icon {
  font-size: 16px;
  color: #4865e8;
}

.slider-text {
  text-align: center;
  margin-top: 12px;
  font-size: 14px;

  .success-text {
    color: #67c23a;
  }

  .fail-text {
    color: #f56c6c;
  }
}

.captcha-refresh {
  position: absolute;
  top: 8px;
  right: 8px;

  .refresh-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: white;
    }

    &.is-loading {
      cursor: not-allowed;

      .loading-icon {
        animation: rotate 1s linear infinite;
      }
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
