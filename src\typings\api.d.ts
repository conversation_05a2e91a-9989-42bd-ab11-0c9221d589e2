declare namespace API {
  // 通用响应格式
  interface Response<T = any> {
    code: number
    msg: string
    data: T
  }

	/**
	 * SysOssUploadVo，上传对象信息
	 */
	interface SysOssUploadVo {
    /**
     * 文件名
     */
    fileName: string;
    /**
     * 对象存储主键
     */
    ossId: string;
    /**
     * URL地址
     */
    url: string;
	}

	/**
	 * 上传文件接口响应类型
	 */
	type UploadFileResponse = Response<SysOssUploadVo>
}

declare namespace API {
  // 分页列表通用响应格式
  interface ResponseRows<T = any> {
    code: number
    msg: string
    rows: T[]
    total: number
  }
}
