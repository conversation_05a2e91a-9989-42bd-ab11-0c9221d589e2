const https = require('https')
const fs = require('fs')
const path = require('path')

// 目标文件路径
const EMOJI_LIST_FILE = path.resolve(__dirname, '../src/data/emoji-list.json')

// 确保目录存在
const dir = path.dirname(EMOJI_LIST_FILE)
if (!fs.existsSync(dir)) {
  fs.mkdirSync(dir, { recursive: true })
}

// 从emoji-datasource-apple获取表情列表
const url = 'https://raw.githubusercontent.com/iamcal/emoji-data/master/emoji.json'

console.log('开始获取emoji列表...')

https
  .get(url, res => {
    let data = ''

    res.on('data', chunk => {
      data += chunk
    })

    res.on('end', () => {
      try {
        const emojis = JSON.parse(data)

        // 处理表情数据
        const processedEmojis = emojis.map(emoji => ({
          name: emoji.name,
          code: emoji.unified.toLowerCase(),
          category: emoji.category,
          subcategory: emoji.subcategory,
        }))

        // 按分类组织表情
        const categorizedEmojis = {
          smileys: processedEmojis.filter(e => e.category === 'Smileys & Emotion'),
          people: processedEmojis.filter(e => e.category === 'People & Body'),
          animals: processedEmojis.filter(e => e.category === 'Animals & Nature'),
          food: processedEmojis.filter(e => e.category === 'Food & Drink'),
          objects: processedEmojis.filter(e => e.category === 'Objects'),
          symbols: processedEmojis.filter(e => e.category === 'Symbols'),
        }

        // 写入文件
        fs.writeFileSync(EMOJI_LIST_FILE, JSON.stringify(categorizedEmojis, null, 2))
        console.log('emoji列表获取完成！')
      } catch (error) {
        console.error('处理emoji数据时出错:', error)
      }
    })
  })
  .on('error', err => {
    console.error('获取emoji列表时出错:', err)
  })
