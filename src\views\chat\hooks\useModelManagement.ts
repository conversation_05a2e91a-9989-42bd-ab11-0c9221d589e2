import { ref } from 'vue'
import { useChatStore } from '@/store'
import { getBizAiModelAvailableModels, postBizConversationUpdateConversation, updateBizGuestConversation } from '@/api'
import { usePublicChat } from './usePublicChat';

export function useModelManagement(conversationId: string) {
  const modelList = ref<{ label: string; value: string; modelId: string; capabilities: string[]; pinned: 0 | 1; platformName: string; }[]>([])
  const currentModel = ref<string>('')
  const chatStore = useChatStore()
  const { isPublicChat } = usePublicChat()

  // 获取可用模型列表
  async function fetchModelList(conversationDetail: Chat.ConversationVo | null) {
    try {
      // 从会话详情中获取当前modelId
      const { modelId, modelDisplayName, modelName } = conversationDetail || {}
      const currentModelId = String(modelId)

      if (isPublicChat) {
        modelList.value = [{
          label: modelDisplayName || '默认模型',
          value: modelName || '',
          modelId: currentModelId || '',
          capabilities: [],
          pinned: 0,
          platformName: '',
        }]
      } else {
        const { data } = await getBizAiModelAvailableModels()
        modelList.value = data.map(item => ({
          label: item.displayName,
          value: item.name,
          modelId: item.id,
          capabilities: item.capabilities || [],
          pinned: item.pinned || 0,
          platformName: item.platformName || '',
        }))


      }


      // 检查modelId是否存在于可用模型列表中
      const modelExists =
        currentModelId && modelList.value.some(model => model.modelId === currentModelId)

      if (modelExists && currentModelId) {
        // 如果模型存在，设置为当前模型
        const model = modelList.value.find(m => m.modelId === currentModelId)
        if (model) {
          currentModel.value = model.value
          // 确保store中的状态与当前组件保持同步
          chatStore.setModelId(model.modelId)
        }
      }
    } catch (error) {
      // 设置一个默认模型,保证页面可以正常渲染
      modelList.value = [
        {
          label: 'Default Model',
          value: 'default-model',
          modelId: 0,
          icon: 'ri:ai-generate',
        },
      ]
      currentModel.value = 'default-model'
      chatStore.setModelId(0)
    }
  }

  // 处理模型变更
  function handleModelChange(value: string) {
    currentModel.value = value
    // 更新 modelId
    const selectedModel = modelList.value.find(model => model.value === value)
    if (selectedModel) {
      chatStore.setModelId(selectedModel.modelId)
      const postConversationUpdateConversation = isPublicChat
        ? updateBizGuestConversation
        : postBizConversationUpdateConversation
      // 调用接口更新会话的模型ID
      postConversationUpdateConversation({
        id: conversationId,
        modelId: selectedModel.modelId,
      }).catch(error => {
        console.error('更新会话模型失败:', error)
      })
    }
  }

  return {
    modelList,
    currentModel,
    fetchModelList,
    handleModelChange,
  }
}
