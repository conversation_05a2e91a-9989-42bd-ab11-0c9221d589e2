<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { onMounted, ref, watch } from 'vue'
import { getSystemInvitationCode, postSystemInvitationUpdateCode } from '@/api/system'

// 定义接口，避免命名空间问题
interface InvitationCodeVO {
  code?: string
  day?: number
  endDay?: Date
  id?: number
  shareUserId?: number
  shareUserName?: string
  startDay?: Date
  tenantName?: string
  url?: string
  [property: string]: any
}

// 对话框属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:visible'])

// 链接有效期选项
const validityOptions = [
  { label: '链接1天有效', value: '1' },
  { label: '链接7天有效', value: '7' },
  { label: '链接14天有效', value: '14' },
]

// 邀请码信息
const invitationInfo = ref({
  id: '',
  code: '',
  day: 0,
  endDay: '',
  startDay: '',
  shareUserId: 0,
  shareUserName: '',
  tenantName: '',
  url: '',
})

// 选中的有效期
const selectedValidity = ref('1')

// 初始化获取邀请码
const fetchInvitationCode = async () => {
  try {
    const response = await getSystemInvitationCode<InvitationCodeVO>()
    if (response && response.data) {
      invitationInfo.value = {
        id: String(response.data.id || ''),
        code: response.data.code || '',
        day: response.data.day || 0,
        endDay: response.data.endDay ? String(response.data.endDay) : '',
        startDay: response.data.startDay ? String(response.data.startDay) : '',
        shareUserId: response.data.shareUserId || 0,
        shareUserName: response.data.shareUserName || '',
        tenantName: response.data.tenantName || '',
        // resolved url是${当前网站域名}/invite/${code}
        url: `${window.location.origin}/#/invite?sid=${response.data.code || ''}`,
      }
    }
  } catch (error) {
    console.error('获取邀请码失败:', error)
    // ElMessage.error('获取邀请码失败')
  }
}

// 更新邀请码有效期
const updateValidity = async () => {
  try {
    // resolved postSystemInvitationUpdateCode会返回跟getSystemInvitationCode一样格式的数据，不用再重新获取最新邀请信息
    const response = await postSystemInvitationUpdateCode({
      id: invitationInfo.value.id,
      day: selectedValidity.value,
    })
    if (response && response.data) {
      invitationInfo.value = {
        id: String(response.data.id || ''),
        code: response.data.code || '',
        day: response.data.day || 0,
        endDay: response.data.endDay ? String(response.data.endDay) : '',
        startDay: response.data.startDay ? String(response.data.startDay) : '',
        shareUserId: response.data.shareUserId || 0,
        shareUserName: response.data.shareUserName || '',
        tenantName: response.data.tenantName || '',
        url: `${window.location.origin}/#/invite?sid=${response.data.code || ''}`,
      }
    }
    ElMessage.success('更新有效期成功')
  } catch (error) {
    console.error('更新有效期失败:', error)
    // ElMessage.error('更新有效期失败')
  }
}

// 备选复制方案
const fallbackCopyTextToClipboard = (text: string) => {
  const textArea = document.createElement('textarea')
  textArea.value = text
  // 确保textarea不可见
  textArea.style.position = 'fixed'
  textArea.style.left = '-999999px'
  textArea.style.top = '-999999px'
  document.body.appendChild(textArea)
  textArea.focus()
  textArea.select()

  try {
    const successful = document.execCommand('copy')
    if (successful) {
      ElMessage.success('链接已复制到剪贴板')
    } else {
      ElMessage.error('复制失败，请手动复制')
    }
  } catch (err) {
    console.error('复制失败:', err)
    ElMessage.error('复制失败，请手动复制')
  }

  document.body.removeChild(textArea)
}

// 获取完整的邀请信息文本
const getFullInviteText = () => {
  return `${invitationInfo.value.url}
分享人: ${invitationInfo.value.shareUserName}
团队名称: ${invitationInfo.value.tenantName}
链接有效期: ${invitationInfo.value.startDay} ~ ${invitationInfo.value.endDay}`
}

// 复制链接
const copyLink = () => {
  if (!invitationInfo.value.url) {
    ElMessage.warning('邀请链接不存在')
    return
  }

  const fullText = getFullInviteText()

  // 尝试使用现代Clipboard API
  if (navigator.clipboard && navigator.clipboard.writeText) {
    navigator.clipboard
      .writeText(fullText)
      .then(() => {
        ElMessage.success('链接已复制到剪贴板')
      })
      .catch(err => {
        console.error('复制失败:', err)
        // 如果现代API失败，尝试使用备选方案
        fallbackCopyTextToClipboard(fullText)
      })
  } else {
    // 浏览器不支持clipboard API，使用备选方案
    fallbackCopyTextToClipboard(fullText)
  }
}

// 关闭对话框
const closeDialog = () => {
  emit('update:visible', false)
}

// 组件挂载时获取邀请码
onMounted(() => {
  if (props.visible) {
    fetchInvitationCode()
  }
})

// 监听visible变化，当打开对话框时获取邀请码
watch(
  () => props.visible,
  newVal => {
    if (newVal) {
      fetchInvitationCode()
    }
  },
)
</script>

<template>
  <el-dialog
    :model-value="visible"
    title="邀请成员"
    width="1000px"
    destroy-on-close
    @update:model-value="closeDialog"
  >
    <div class="invite-dialog-content">
      <div class="validity-selector">
        <el-select
          v-model="selectedValidity"
          placeholder="选择有效期"
          style="width: 100%"
          @change="updateValidity"
        >
          <el-option
            v-for="item in validityOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
      <div class="invite-link-box">
        <!-- resolved copyLink应该复制这里面的文本而不是只有url -->
        <div class="link-info !text-[14px] text-center">
          <div class="link-url">{{ invitationInfo.url }}</div>
          <div class="link-meta">
            <span>分享人: {{ invitationInfo.shareUserName }}</span>
            <span>团队名称: {{ invitationInfo.tenantName }}</span>
            <span>链接有效期: {{ invitationInfo.startDay }} ~ {{ invitationInfo.endDay }}</span>
          </div>
        </div>
      </div>
      <div class="dialog-footer">
        <el-button type="primary" class="copy-btn" @click="copyLink"> 复制链接 </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<style lang="less" scoped>
.invite-dialog-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.invite-link-box {
  border: 1px solid #e6eaf4;
  border-radius: 8px;
  padding: 15px;
  background-color: #f9fafc;
}

.link-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.link-url {
  word-break: break-all;
  color: #333;
}

.link-meta {
  display: flex;
  flex-direction: column;
  gap: 5px;
  color: #646a73;
}

.validity-selector {
  margin-top: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

.copy-btn {
  width: 100%;
}
</style>
