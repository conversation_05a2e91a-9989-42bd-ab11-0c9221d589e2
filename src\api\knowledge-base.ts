import { deleteReq, get, post } from '@/utils/request'
import '@/typings/api.d.ts'
import '@/typings/knowledge-base.d.ts'
// "/biz/kbItem/remove": {
// "post": {
// "summary": "删除",
export function postBizKbItemRemove<T>(id: number) {
	return post<T>({
		url: `/biz/kbItem/remove?id=${id}`
	})
}

// "/biz/kbItem/enable": {
// "post": {
// "summary": "启用/禁用",
export function postBizKbItemEnable<T>(data: {
	enabled: boolean;
	kbItemIdList: string[];
}) {
	return post<T>({
		url: '/biz/kbItem/enable',
		data,
	})
}

// "/biz/kb/uploadItem": {
// "post": {
// "summary": "上传文件",
export function postBizKbUploadItem<T>(data: KnowledgeBase.KnowledgeBaseItemBo) {
	return post<T>({
		url: '/biz/kb/uploadItem',
		data,
	})
}

// "/biz/kb/updateKnowledgeBase": {
// "post": {
// "summary": "更新知识库",
export function postBizKbUpdateKnowledgeBase<T>(data: KnowledgeBase.KnowledgeBaseBo) {
	return post<T>({
		url: '/biz/kb/updateKnowledgeBase',
		data,
	})
}

// "/biz/kb/searchTest": {
// "post": {
// "summary": "检索测试",
export function postBizKbSearchTest<T>(data: KnowledgeBase.SearchTestBo) {
	return post<T>({
		url: '/biz/kb/searchTest',
		data,
	})
}

// "/biz/kb/remove": {
// "post": {
// "summary": "删除",
export function postBizKbRemove<T>(id: number | string) {
	return post<T>({
		url: `/biz/kb/remove?id=${id}`,
	})
}

// "/biz/kb/enable": {
// "post": {
// "summary": "启用/禁用",
export function postBizKbEnable<T>(id: number | string) {
	return post<T>({
		url: `/biz/kb/enable?id=${id}`
	})
}

// "/biz/kb/createKnowledgeBase": {
// "post": {
// "summary": "新增知识库",
export function postBizKbCreateKnowledgeBase<T>(data: KnowledgeBase.AddKbParams) {
	return post<T>({
		url: '/biz/kb/createKnowledgeBase',
		data,
	})
}

// "/biz/kbItem/splitList": {
// "get": {
// "summary": "获取分片信息",
export function getBizKbItemSplitList<T>(data: { kbItemId: string; enabled?: boolean }) {
	return get<T>({
		url: '/biz/kbItem/splitList',
		data,
	})
}

// "/biz/kbItem/previewSlice": {
// "get": {
// "summary": "预览切片",
export function getBizKbItemPreviewSlice<T>(data: KnowledgeBase.PreviewSliceParams) {
	return get<string[]>({
		url: '/biz/kbItem/previewSlice',
		data,
	})
}

// "/biz/kbItem/list": {
// "get": {
// "summary": "知识库内容列表",
export function getBizKbItemList<T>(data: KnowledgeBase.KbItemListParams) {
	return get<T>({
		url: '/biz/kbItem/list',
		data,
	})
}

// "/biz/kb/shareList": {
// "get": {
// "summary": "共享知识库",
export function getBizKbShareList<T>(data: KnowledgeBase.KbListParams) {
	return get<T>({
		url: '/biz/kb/shareList',
		data,
	})
}

// "/biz/kb/myList": {
// "get": {
// "summary": "我的知识库",
export function getBizKbMyList<T>(data: KnowledgeBase.KbListParams) {
	return get<T>({
		url: '/biz/kb/myList',
		data,
	})
}

// "/biz/kb/allList": {
// "get": {
// "summary": "所有知识库",
export function getBizKbAllList<T>(data: KnowledgeBase.KbListParams) {
	return get<T>({
		url: '/biz/kb/allList',
		data,
	})
}

// /biz/kb/detail
// 知识库详情
export function getBizKbDetail<T>(id: string) {
	return get<T>({
		url: `/biz/kb/detail`,
		data: {
			id
		}
	})
}

// /biz/kbItem/retry
// 知识库内容重试
export function postBizKbItemRetry<T>(data: {
	idList: string[];
}) {
	return post<T>({
		url: '/biz/kbItem/retry',
		data,
	})
}

// /biz/kbItem/enableSlice
// 启用/禁用切片
export function postBizKbItemEnableSlice<T>(data: {
	kbItemIdList: (string | number)[];
	embeddingIdList: (string | number)[];
	enabled: boolean;
}) {
	return post<T>({
		url: '/biz/kbItem/enableSlice',
		data,
	})
}

// /biz/kb/getModelBindKb/{modelId}
// 获取模型绑定知识库
export function getBizKbGetModelBindKb<T>(modelId: string) {
	return get<KnowledgeBase.KnowledgeBaseVo[]>({
		url: `/biz/kb/getModelBindKb/${modelId}`
	})
}