# 本地 Emoji 解决方案

## 背景

原项目使用了 `vue3-emoji-picker` 组件，该组件依赖于从CDN加载emoji图片（`https://cdn.jsdelivr.net/npm/emoji-datasource-apple/img/apple/64/`）。在中国区域或者某些网络环境下，这些图片可能无法正常加载，导致用户无法看到emoji。

## 解决方案

我们已经实现了一个本地emoji方案，通过以下步骤解决了依赖外部CDN的问题：

1. 创建了一个本地的 `EmojiPickerLocal` 组件，替代原有的 `vue3-emoji-picker`
2. 提供了一个脚本 `scripts/download-emojis.js` 用于下载常用的emoji图片到本地 `/public/static/emoji/` 目录
3. 在构建前自动执行下载脚本，确保emoji图片在打包时已经存在

## 如何使用

### 开发环境

在开发环境中，你可以手动下载emoji图片：

```bash
# 使用npm
npm run download-emojis

# 使用pnpm
pnpm download-emojis
```

### 生产环境

在执行构建命令时，prebuild钩子会自动下载emoji图片：

```bash
# 使用npm
npm run build

# 使用pnpm
pnpm build
```

## 自定义表情

如果需要添加或修改可用的表情，可以编辑以下文件：

1. `src/components/EmojiPickerLocal.vue` - 修改组件中的表情数据
2. `scripts/download-emojis.js` - 修改要下载的表情列表

## 表情格式

每个表情的数据格式如下：

```js
{ 
  name: '表情名称', 
  code: '1f600'  // unicode编码，对应图片文件名
}
```

## 离线使用

由于所有emoji图片都存储在项目的 `/public/static/emoji/` 目录下，用户不需要联网即可查看和使用表情。

## 故障排除

### 下载失败问题

如果emoji图片下载失败，脚本会自动创建1x1像素的透明占位图片，以确保应用程序不会因为缺少图片文件而出错。你可以:

1. 重新运行下载脚本: `pnpm download-emojis`
2. 手动下载缺失的图片并放入 `/public/static/emoji/` 目录
3. 检查网络连接和代理设置，确保能访问CDN

### 修改下载源

如果默认的CDN源无法访问，你可以在 `scripts/download-emojis.js` 文件中修改 `CDN_SOURCES` 数组，添加更多可用的CDN源。

## 注意事项

1. 如果需要使用更多的emoji，需要同时更新组件和下载脚本
2. 表情图片使用的是Apple设计风格的emoji
3. 当前方案只包含了最常用的表情，如需全部表情，可能会增加项目体积
4. 下载脚本包含多个CDN源和自动重试逻辑，以提高下载成功率
5. 如果遇到网络问题导致无法下载，可以考虑直接将emoji图片文件包含在项目源代码中

## 兼容性

此方案完全兼容原有的表情选择功能，只是将图片来源从CDN改为本地存储，不会影响用户体验。 