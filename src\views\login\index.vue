<script setup lang="ts">
import { onMounted, reactive, ref, watch } from 'vue'
import { Icon } from '@iconify/vue'
import { useRoute, useRouter } from 'vue-router'
import { ElButton, ElCheckbox, ElInput, ElMessage, ElMessageBox } from 'element-plus'
import CryptoJS from 'crypto-js'
import {
  chooseTenant,
  getChooseTenantList,
  getInvitationCode,
  getSmsCode,
  getTenantList,
} from '@/components/common/LoginDialog/utils/api'
// resolved 接入对应逻辑getSmsCode
import type {
  LoginData,
  TenantListVo,
  TenantVO,
  VerifyCodeResult,
} from '@/components/common/LoginDialog/utils/api'
import { encryptWithAes } from '@/components/common/LoginDialog/utils/crypto'
import { useUserStore } from '@/store/modules/user'
import { SvgIcon } from '@/components/common'
import SlideCaptcha from '@/components/common/SlideCaptcha/index.vue'
import { useCaptcha } from '@/hooks/useCaptcha'

const router = useRouter()
const route = useRoute()
const mode = route.path.includes('invite') ? 'invite' : 'login'
const modeStep = ref(1)
console.log('mode', mode)
const userStore = useUserStore()
const loading = ref(false)
const showPassword = ref(false)
const loginActiveTab = ref(mode === 'login' ? 'password' : 'sms') // 账号登录/短信登录切换

// AES加密函数 - 从VerifySlide.vue复制过来
const aesEncrypt = (word: string, keyWord = 'XwKsGlMcdPMEhR1B'): string => {
  const key = CryptoJS.enc.Utf8.parse(keyWord)
  const srcs = CryptoJS.enc.Utf8.parse(word)
  const encrypted = CryptoJS.AES.encrypt(srcs, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7,
  })
  return encrypted.toString()
}

// 滑块验证
const {
  showCaptcha,
  captchaData,
  checkNeedCaptcha,
  showCaptchaDialog,
  hideCaptchaDialog,
  resetCaptchaData,
} = useCaptcha()

// 已加入的企业列表
const joinedTenantList = ref<TenantListVo[]>([])
const selectedTenantId = ref('')

// 邀请码所属企业信息
const tenantInfo = ref<TenantListVo>({})

// 邀请码是否失效
const isInviteExpired = ref(false)

// 定义表单数据
interface LoginFormData {
  tenantId: string
  username?: string
  password?: string
  uuid: string
  agreeWithMe?: boolean
  phonenumber?: string
  smsCode?: string
  grantType?: string
  invitationCode?: string
}

const loginForm = reactive<LoginFormData>({
  tenantId: '',
  // resolved 判断是dev情况下，账号是admin，密码为WekRyMvEx8KSehMK，否则为空
  username: import.meta.env.MODE === 'development' && mode === 'login' ? 'admin' : '',
  password: import.meta.env.MODE === 'development' && mode === 'login' ? 'WekRyMvEx8KSehMK' : '',
  phonenumber: import.meta.env.MODE === 'development' && mode === 'login' ? '' : '',
  // username: import.meta.env.MODE === 'development' && mode === 'login' ? '' : '',
  // password: import.meta.env.MODE === 'development' && mode === 'login' ? '' : '',
  // phonenumber: import.meta.env.MODE === 'development' && mode === 'login' ? '' : '',
  smsCode: import.meta.env.MODE === 'development' && mode === 'login' ? '' : '',
  uuid: '',
  invitationCode: '',
  grantType: 'password',
  agreeWithMe: false,
})

// 定义错误信息，添加索引签名以支持动态属性访问
interface ErrorMessages {
  tenantId: string
  username: string
  password: string
  phonenumber: string
  smsCode: string
  [key: string]: string
}

const errors = reactive<ErrorMessages>({
  tenantId: '',
  username: '',
  password: '',
  phonenumber: '',
  smsCode: '',
})

// 忘记密码表单数据
interface ResetPasswordFormData {
  username: string
  mobile: string
  code: string
  newPassword: string
}

// 忘记密码表单
const resetPasswordForm = reactive<ResetPasswordFormData>({
  username: '',
  mobile: '',
  code: '',
  newPassword: '',
})

// 忘记密码表单错误信息
const resetPasswordErrors = reactive<Record<string, string>>({
  username: '',
  mobile: '',
  code: '',
  newPassword: '',
})

// 发送验证码倒计时
const countdown = ref(0)
const smsButtonText = ref('获取验证码')

// 滑块验证成功状态标记
const captchaSuccessContext = ref<'login' | 'sms' | null>(null)

// 表单验证
const validateForm = () => {
  let isValid = true
  if (mode === 'invite') {
    if (!loginForm.phonenumber) {
      ElMessage.warning('请输入手机号')
      isValid = false
    }
    if (modeStep.value === 2 && !loginForm.smsCode) {
      ElMessage.warning('请输入验证码')
      isValid = false
    }
  }

  if (mode === 'login') {
    if (loginActiveTab.value === 'password') {
      if (!loginForm.username) {
        ElMessage.warning('请输入用户名')
        isValid = false
      }
      if (modeStep.value === 2 && !loginForm.password) {
        ElMessage.warning('请输入密码')
        isValid = false
      }
    } else if (loginActiveTab.value === 'sms') {
      if (!loginForm.phonenumber) {
        ElMessage.warning('请输入手机号')
        isValid = false
      }
      if (!loginForm.smsCode) {
        ElMessage.warning('请输入验证码')
        isValid = false
      }
    }
  }

  return isValid
}

// 检查是否已勾选"用户协议与隐私政策"
const checkAgreement = (): Promise<boolean> => {
  return new Promise(resolve => {
    if (!loginForm.agreeWithMe) {
      ElMessageBox.confirm('您需要同意《用户协议与隐私政策》才能继续', '提示', {
        confirmButtonText: '同意并继续',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          // 自动勾选
          loginForm.agreeWithMe = true
          resolve(true)
        })
        .catch(() => {
          resolve(false)
        })
    } else {
      // 已勾选，直接返回true
      resolve(true)
    }
  })
}

// 执行登录逻辑
const performLogin = async (skipCaptchaCheck = false) => {
  loading.value = true
  try {
    // resolved 参考src\.temp\verifition\Verify\VerifySlide.vue的var captchaVerification = secretKey.value ? aesEncrypt(backToken.value+'---'+JSON.stringify({x:moveLeftDistance,y:5.0}),secretKey.value):backToken.value+'---'+JSON.stringify({x:moveLeftDistance,y:5.0})
    // 生成captchaVerification - 完全按照VerifySlide.vue的实现
    let captchaVerification = ''
    if (captchaData.value) {
      const { secretKey, token, x } = captchaData.value
      // 完全按照VerifySlide.vue的格式：固定y坐标为5.0，使用aesEncrypt函数
      captchaVerification = secretKey
        ? aesEncrypt(`${token}---${JSON.stringify({ x, y: 5.0 })}`, secretKey)
        : `${token}---${JSON.stringify({ x, y: 5.0 })}`
    }

    // 构造登录数据
    const data: LoginData = {
      tenantId: mode === 'invite' ? '' : loginForm.tenantId,
      grantType: loginActiveTab.value === 'password' ? 'password' : 'sms',
      username: loginActiveTab.value === 'password' ? loginForm.username || '' : '',
      password: loginActiveTab.value === 'password' ? loginForm.password || '' : '',
      phonenumber: loginActiveTab.value === 'sms' ? loginForm.phonenumber || '' : '',
      smsCode: loginActiveTab.value === 'sms' ? loginForm.smsCode || '' : '',
      code: '', // 验证码，暂时为空
      uuid: loginForm.uuid,
      invitationCode: mode === 'invite' ? (route.query.sid as string) : '',
      captchaVerification,
    }
    console.log('data', data)

    // 只有在未跳过验证检查时才执行验证逻辑
    if (!skipCaptchaCheck) {
      // 检查是否需要滑块验证
      const username =
        loginActiveTab.value === 'password' ? loginForm.username || '' : loginForm.phonenumber || ''
      const needCaptcha = await checkNeedCaptcha(username, data.tenantId)

      if (needCaptcha && !captchaData.value) {
        // 需要滑块验证但还没有验证，显示滑块
        captchaSuccessContext.value = 'login' // 设置上下文为登录场景
        showCaptchaDialog()
        loading.value = false
        return
      }
    }

    // 使用userStore进行登录
    await userStore.login(data)

    // 登录成功后清空验证数据
    resetCaptchaData()

    // 如果用户勾选了"记住我"，将状态保存到本地存储
    if (loginForm.agreeWithMe) {
      localStorage.setItem('agreeWithMeAgreed', 'true')
    }
    ElMessage.success('登录成功')
    // mode === 'invite' 登录成功后，跳转到首页
    // mode === 'login' 登录成功后，modeStep++
    if (mode === 'login') {
      // 获取已加入的企业
      try {
        const res = await getChooseTenantList()
        joinedTenantList.value = Array.isArray(res.data) ? res.data : []
        if (joinedTenantList.value.length > 0) {
          // 如果有已加入的企业，进入选择企业步骤
          selectedTenantId.value = joinedTenantList.value[0].tenantId || ''
          // resolved 造一个数据，joinedTenantList.value[1]，便于让我完成样式
          // if (joinedTenantList.value.length === 1) {
          //   joinedTenantList.value.push({
          //     tenantId: 'example-tenant-id-2',
          //     companyName: '示例企业名称',
          //     tenantName: '示例企业',
          //     domain: 'example.com',
          //   })
          // }
          // 从第二步到第三步
          modeStep.value++
        }
      } catch (error) {
        console.error('获取已加入企业失败:', error)
        // 获取失败也跳转到首页
        router.push('/')
      }
    } else {
      // 跳转到首页或重定向页面
      console.log('跳转到首页-mode-invite')
      // 获取用户信息
      await userStore.getInfo()
      // 检查是否有重定向参数
      const redirectPath = route.query.redirect as string
      if (redirectPath && redirectPath !== '/login') {
        router.push(redirectPath)
      } else {
        router.push('/')
      }
    }
  } catch (error: any) {
    console.error('登录失败:', error)
    ElMessage.error(error || '登录失败，请重试')
    // 登录失败时清空验证数据，确保下次登录可以重新验证
    resetCaptchaData()
  } finally {
    loading.value = false
  }
}

// 选择企业并进入系统
const handleChooseTenant = async () => {
  if (!selectedTenantId.value) {
    ElMessage.warning('请选择企业')
    return
  }

  loading.value = true
  try {
    await chooseTenant(selectedTenantId.value)
    ElMessage.success('企业选择成功')
    await userStore.getInfo()
    // 检查是否有重定向参数
    const redirectPath = route.query.redirect as string
    if (redirectPath && redirectPath !== '/login') {
      router.push(redirectPath)
    } else {
      router.push('/')
    }
  } catch (error: any) {
    console.error('选择企业失败:', error)
    ElMessage.error(error || '选择企业失败')
  } finally {
    loading.value = false
  }
}

const handleNextStep = async () => {
  // 检查是否已勾选"用户协议与隐私政策"
  if (!validateForm()) return
  if (mode === 'login') {
    if (modeStep.value === 1) {
      loginForm.phonenumber = loginForm.username
    }
  }
  const agreed = await checkAgreement()
  if (agreed) {
    modeStep.value++
  }
}

const handleBackStep = () => {
  loginActiveTab.value = 'password'
  modeStep.value--
}

// 登录方法
const handleLogin = async () => {
  if (!validateForm()) return
  // 检查是否已勾选"用户协议与隐私政策"
  const agreed = await checkAgreement()
  if (agreed) {
    // 已勾选，直接继续登录
    performLogin()
  }
}

// 切换登录方式
const switchTab = (tab: string) => {
  loginActiveTab.value = tab
  // 清除表单错误信息
  Object.keys(errors).forEach(key => {
    errors[key] = ''
  })
  Object.keys(resetPasswordErrors).forEach(key => {
    resetPasswordErrors[key] = ''
  })
}

// 处理忘记密码链接点击
const handleForgotPassword = () => {
  switchTab('reset-password')
}

// 发送验证码
// resolved 点击后，接口返回前，显示为"发送中"
const hasSendSmsCode = ref(false)
const isSendingSms = ref(false)
// 发送短信验证码的核心逻辑
const sendSmsCodeCore = async (skipCaptchaCheck = false) => {
  if (countdown.value > 0 || isSendingSms.value) return

  // 根据当前登录方式和步骤确定手机号
  let phoneNumber = ''
  if (loginActiveTab.value === 'sms') {
    phoneNumber = loginForm.phonenumber || ''
  } else if (loginActiveTab.value === 'reset-password') {
    phoneNumber = resetPasswordForm.mobile
  }
  console.log('phoneNumber', phoneNumber)
  // 验证手机号
  if (!phoneNumber) {
    ElMessage.warning('请输入手机号')
    return
  }

  // 手机号格式验证
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(phoneNumber)) {
    ElMessage.warning('请输入正确的手机号格式')
    return
  }

  isSendingSms.value = true
  smsButtonText.value = '发送中'

  try {
    // 只有在未跳过验证检查时才执行验证逻辑
    if (!skipCaptchaCheck) {
      // 检查是否需要滑块验证
      const needCaptcha = await checkNeedCaptcha(phoneNumber, loginForm.tenantId)
      if (needCaptcha && !captchaData.value) {
        // 需要滑块验证但还没有验证，显示滑块
        captchaSuccessContext.value = 'sms' // 设置上下文为发送短信场景
        showCaptchaDialog()
        return
      }
    }

    // 调用短信验证码接口
    await getSmsCode({ phonenumber: phoneNumber })
    hasSendSmsCode.value = true
    ElMessage.success('验证码已发送，请注意查收')
    // 启动倒计时
    countdown.value = 60
    const timer = setInterval(() => {
      countdown.value--
      smsButtonText.value = `${countdown.value}秒后重试`
      if (countdown.value <= 0) {
        clearInterval(timer)
        smsButtonText.value = '获取验证码'
      }
    }, 1000)
  } catch (error: any) {
    console.error('发送验证码失败:', error)
    ElMessage.error(error || '发送验证码失败，请重试')
    smsButtonText.value = '获取验证码'
    // 发送验证码失败时清空验证数据，确保下次可以重新验证
    resetCaptchaData()
  } finally {
    isSendingSms.value = false
  }
}

// 点击获取验证码按钮的处理函数
const sendSmsCode = () => {
  sendSmsCodeCore(false)
}

// 滑块验证成功后的处理
const handleCaptchaSuccess = (data: any) => {
  // 保存滑块验证数据，包含坐标和token信息
  captchaData.value = data
  hideCaptchaDialog()

  // 根据上下文执行对应的操作
  // resolved 滑块验证成功后直接执行操作，跳过重复的验证检查
  if (captchaSuccessContext.value === 'login') {
    // 登录场景：继续执行登录，跳过验证检查
    performLogin(true)
  } else if (captchaSuccessContext.value === 'sms') {
    // 发送短信场景：继续发送短信验证码，跳过验证检查
    sendSmsCodeCore(true)
  }

  // 重置上下文
  captchaSuccessContext.value = null
}

// 重置验证码状态的方法
const resetCaptchaState = () => {
  resetCaptchaData()
}

// 提交重置密码
const handleResetPassword = () => {
  // 表单验证
  let isValid = true

  if (!resetPasswordForm.username) {
    resetPasswordErrors.username = '请输入用户名'
    isValid = false
  }

  if (!resetPasswordForm.mobile) {
    resetPasswordErrors.mobile = '请输入手机号'
    isValid = false
  }

  if (!resetPasswordForm.code) {
    resetPasswordErrors.code = '请输入验证码'
    isValid = false
  }

  if (!resetPasswordForm.newPassword) {
    resetPasswordErrors.newPassword = '请输入新密码'
    isValid = false
  }

  if (!isValid) return

  // 这里应该调用重置密码的API

  // 模拟重置密码成功
  ElMessage.success('密码重置成功，请使用新密码登录')
  // 切换到登录页
  loginActiveTab.value = 'password'
}

onMounted(() => {
  // 如果已经登录，跳转到首页
  if (mode === 'login' && userStore.token && userStore.userId) {
    router.push('/')
    return
  } else if (mode === 'invite') {
    if (route.query.sid) {
      // resolved 使用getInvitationCode查询邀请码所属企业
      const inviteCode = route.query.sid as string
      loading.value = true
      getInvitationCode(inviteCode)
        .then(res => {
          if (res.data) {
            // 设置邀请码所属企业信息
            tenantInfo.value = res.data
            loginForm.tenantId = tenantInfo.value.tenantId || ''
            ElMessage.success(`您正在加入企业：${tenantInfo.value.tenantName}`)
          }
        })
        .catch(error => {
          console.error('获取邀请码信息失败:', error)
          isInviteExpired.value = true // 为了保险起见，也显示失效界面
        })
        .finally(() => {
          loading.value = false
        })
    } else {
      // 没有邀请码参数，显示失效界面
      isInviteExpired.value = true
    }
  }
  // 从本地存储中获取agreeWithMe状态
  const agreeWithMeAgreed = localStorage.getItem('agreeWithMeAgreed')
  if (agreeWithMeAgreed === 'true') {
    loginForm.agreeWithMe = true
  }

  // 强制禁止横屏
  const lockOrientation = () => {
    // 检查是否支持屏幕方向锁定
    if (screen.orientation && screen.orientation.lock) {
      screen.orientation.lock('portrait').catch(err => {
        console.log('屏幕方向锁定失败:', err)
      })
    }
    // 对于不支持orientation API的浏览器，添加CSS强制竖屏
    if (window.orientation !== undefined) {
      // 监听方向变化
      const handleOrientationChange = () => {
        if (Math.abs(window.orientation) === 90) {
          // 横屏时显示提示
          const orientationTip = document.getElementById('orientation-tip')
          if (orientationTip) {
            orientationTip.style.display = 'flex'
          }
        } else {
          // 竖屏时隐藏提示
          const orientationTip = document.getElementById('orientation-tip')
          if (orientationTip) {
            orientationTip.style.display = 'none'
          }
        }
      }

      window.addEventListener('orientationchange', handleOrientationChange)
      // 初始检查
      handleOrientationChange()
    }
  }

  lockOrientation()
})
</script>

<template>
  <div class="login-wrap">
    <div class="login-container">
      <div class="login-logo">
        <img src="@/assets/login/logo.png" alt="" />
      </div>
      <div class="login-box">
        <!-- 登录面板左侧 -->
        <div class="login-left">
          <img
            v-if="loginActiveTab === 'password'"
            src="@/assets/login/bg1.jpg"
            alt="登录背景"
            class="login-bg"
          />
          <img v-else src="@/assets/login/bg2.jpg" alt="登录背景" class="login-bg" />
        </div>
        <!-- ========================invite 模式======================== -->
        <!-- resolved sid对应的邀请链接可能会失效，失效的话展示对应的失效界面src\assets\login\expire.png -->
        <template v-if="mode === 'invite'">
          <!-- 邀请码失效界面 -->
          <div v-if="isInviteExpired" class="login-right expire-container">
            <div class="expire-content">
              <img src="@/assets/login/expire.png" alt="邀请链接已失效" class="expire-image" />
              <h2 class="expire-title">链接失效</h2>
              <p class="expire-description">邀请链接已过期，请更换链接再尝试</p>
            </div>
          </div>
          <div v-if="modeStep === 1 && !isInviteExpired" class="login-right">
            <div class="login-header">
              <h2>欢迎使用</h2>
            </div>
            <!-- resolved 邀请者 shareUserName -->
            <div class="text-[18px]">
              <span class="g-c mr-2">{{ tenantInfo?.shareUserName || '' }}</span
              >邀请您加入
            </div>
            <!-- resolved 公司名称 tenantName -->
            <div class="text-[20px] g-c mt-[24px] mb-[40px]">
              <i class="iconfont iconfont-c icon-qiye !text-[20px]"></i>
              {{ tenantInfo?.tenantName || '' }}
            </div>
            <form class="login-form">
              <!-- 用户名 -->
              <div class="form-group">
                <ElInput
                  v-model="loginForm.phonenumber"
                  :input-style="{ height: '64px', fontSize: '18px' }"
                  placeholder="请输入您的手机号码"
                  type="tel"
                  clearable
                >
                  <template #prefix>
                    <Icon icon="material-symbols:smartphone-outline" class="input-icon" />
                  </template>
                </ElInput>
                <span v-if="errors.phonenumber" class="error-message">{{
                  errors.phonenumber
                }}</span>
              </div>
              <div class="form-options">
                <ElCheckbox v-model="loginForm.agreeWithMe">
                  <span class="checkbox-text"
                    >阅读并同意<router-link to="/terms" class="link-text"
                      >《用户协议与隐私政策》</router-link
                    ></span
                  >
                </ElCheckbox>
              </div>
              <!-- 登录按钮 -->
              <div class="form-submit">
                <ElButton
                  type="primary"
                  :loading="loading"
                  class="login-button"
                  @click="handleNextStep"
                >
                  登录 / 注册
                </ElButton>
              </div>
            </form>
          </div>
          <div v-if="modeStep === 2 && !isInviteExpired" class="login-right">
            <div v-if="loginActiveTab !== 'reset-password'" class="login-header">
              <h2>欢迎使用</h2>
            </div>
            <div v-if="loginActiveTab !== 'reset-password'" class="login-tabs">
              <div
                class="login-tab"
                :class="{ active: loginActiveTab === 'sms' }"
                @click="switchTab('sms')"
              >
                短信登录
              </div>
            </div>
            <form v-if="loginActiveTab === 'sms'" class="login-form" @submit.prevent>
              <!-- 手机号 -->
              <div class="form-group">
                <ElInput
                  v-model="loginForm.phonenumber"
                  :input-style="{ height: '64px', fontSize: '18px' }"
                  placeholder="请输入手机号"
                  type="tel"
                  clearable
                >
                  <template #prefix>
                    <Icon icon="material-symbols:smartphone-outline" class="input-icon" />
                  </template>
                </ElInput>
                <span v-if="errors.phonenumber" class="error-message">{{
                  errors.phonenumber
                }}</span>
              </div>

              <!-- 验证码 -->
              <div class="form-group sms-group">
                <div class="sms-input-container">
                  <ElInput
                    v-model="loginForm.smsCode"
                    :disabled="!hasSendSmsCode"
                    :input-style="{ height: '64px', fontSize: '18px' }"
                    placeholder="请输入验证码"
                    inputmode="numeric"
                    clearable
                  >
                    <template #prefix>
                      <Icon icon="material-symbols:sms-outline" class="input-icon" />
                    </template>
                  </ElInput>
                </div>
                <ElButton
                  type="primary"
                  class="sms-button"
                  :disabled="countdown > 0 || isSendingSms"
                  @click="sendSmsCode"
                  >{{ smsButtonText }}</ElButton
                >
              </div>
              <span v-if="errors.smsCode" class="error-message">{{ errors.smsCode }}</span>

              <div class="form-options">
                <ElCheckbox v-model="loginForm.agreeWithMe">
                  <span class="checkbox-text"
                    >阅读并同意<router-link to="/terms" class="link-text"
                      >《用户协议与隐私政策》</router-link
                    ></span
                  >
                </ElCheckbox>
              </div>

              <!-- 登录按钮 -->
              <div class="form-submit">
                <ElButton type="primary" class="login-button" @click="handleLogin"
                  >登录 / 注册</ElButton
                >
              </div>
            </form>
          </div>
        </template>
        <!-- ========================login 模式======================== -->
        <template v-if="mode === 'login'">
          <div v-if="modeStep === 1" class="login-right">
            <div v-if="loginActiveTab !== 'reset-password'" class="login-header">
              <h2>欢迎使用</h2>
            </div>
            <div
              v-if="loginActiveTab === 'reset-password'"
              class="login-header"
              style="color: #4865e8"
            >
              <h2>忘记密码</h2>
            </div>
            <div v-if="loginActiveTab !== 'reset-password'" class="login-tabs">
              <div
                class="login-tab"
                :class="{ active: loginActiveTab === 'password' }"
                @click="switchTab('password')"
              >
                账号登录
              </div>
              <div
                class="login-tab"
                :class="{ active: loginActiveTab === 'sms' }"
                @click="switchTab('sms')"
              >
                短信登录
              </div>
            </div>

            <form v-if="loginActiveTab === 'password'" class="login-form">
              <!-- 用户名 -->
              <div class="form-group">
                <ElInput
                  v-model="loginForm.username"
                  :input-style="{ height: '64px', fontSize: '18px' }"
                  placeholder="请输入账号"
                  clearable
                >
                  <template #prefix>
                    <Icon icon="material-symbols:smartphone-outline" class="input-icon" />
                  </template>
                </ElInput>
                <span v-if="errors.username" class="error-message">{{ errors.username }}</span>
              </div>

              <!-- 密码 -->
              <div class="form-group">
                <ElInput
                  v-model="loginForm.password"
                  :input-style="{ height: '64px', fontSize: '18px' }"
                  :type="showPassword ? 'text' : 'password'"
                  placeholder="请输入密码"
                  clearable
                  show-password
                >
                  <template #prefix>
                    <Icon icon="material-symbols:lock-outline" class="input-icon" />
                  </template>
                </ElInput>
                <span v-if="errors.password" class="error-message">{{ errors.password }}</span>
              </div>

              <div class="form-options">
                <ElCheckbox v-model="loginForm.agreeWithMe">
                  <!-- resolved 创建一个"用户协议与隐私政策"页面 -->
                  <!-- resolved 点击登录时候，如果此处为勾选，则弹框提示，确认后替用户勾选并继续登录流程 -->
                  <span class="checkbox-text"
                    >阅读并同意<router-link to="/terms" class="link-text"
                      >《用户协议与隐私政策》</router-link
                    ></span
                  >
                </ElCheckbox>
                <a href="#" class="forgot-password" @click.prevent="handleForgotPassword"
                  >忘记密码</a
                >
              </div>

              <!-- 登录按钮 -->
              <div class="form-submit">
                <ElButton
                  type="primary"
                  :loading="loading"
                  class="login-button"
                  @click="handleLogin"
                >
                  登录
                </ElButton>
              </div>
            </form>

            <form v-if="loginActiveTab === 'sms'" class="login-form" @submit.prevent>
              <!-- 手机号 -->
              <div class="form-group">
                <ElInput
                  v-model="loginForm.phonenumber"
                  :input-style="{ height: '64px', fontSize: '18px' }"
                  placeholder="请输入手机号"
                  type="tel"
                  clearable
                >
                  <template #prefix>
                    <Icon icon="material-symbols:smartphone-outline" class="input-icon" />
                  </template>
                </ElInput>
                <span v-if="errors.phonenumber" class="error-message">{{
                  errors.phonenumber
                }}</span>
              </div>

              <!-- 验证码 -->
              <div class="form-group sms-group">
                <div class="sms-input-container">
                  <ElInput
                    v-model="loginForm.smsCode"
                    :disabled="!hasSendSmsCode"
                    :input-style="{ height: '64px', fontSize: '18px' }"
                    placeholder="请输入验证码"
                    inputmode="numeric"
                    clearable
                  >
                    <template #prefix>
                      <Icon icon="material-symbols:sms-outline" class="input-icon" />
                    </template>
                  </ElInput>
                </div>
                <ElButton
                  type="primary"
                  class="sms-button"
                  :disabled="countdown > 0 || isSendingSms"
                  @click="sendSmsCode"
                  >{{ smsButtonText }}</ElButton
                >
              </div>
              <span v-if="errors.smsCode" class="error-message">{{ errors.smsCode }}</span>
              <div class="form-options">
                <ElCheckbox v-model="loginForm.agreeWithMe">
                  <span class="checkbox-text"
                    >阅读并同意<router-link to="/terms" class="link-text"
                      >《用户协议与隐私政策》</router-link
                    ></span
                  >
                </ElCheckbox>
              </div>

              <!-- 登录按钮 -->
              <div class="form-submit">
                <ElButton type="primary" class="login-button" @click="handleLogin">登录</ElButton>
              </div>
            </form>

            <form
              v-if="loginActiveTab === 'reset-password'"
              class="login-form"
              @submit.prevent="handleResetPassword"
            >
              <!-- 手机号 -->
              <div class="form-group">
                <ElInput
                  v-model="resetPasswordForm.mobile"
                  :input-style="{ height: '64px', fontSize: '18px' }"
                  placeholder="请输入手机号"
                  type="tel"
                  clearable
                >
                  <template #prefix>
                    <Icon icon="material-symbols:smartphone-outline" class="input-icon" />
                  </template>
                </ElInput>
                <span v-if="resetPasswordErrors.mobile" class="error-message">{{
                  resetPasswordErrors.mobile
                }}</span>
              </div>

              <!-- 验证码 -->
              <div class="form-group sms-group">
                <div class="sms-input-container">
                  <ElInput
                    v-model="resetPasswordForm.code"
                    :disabled="!hasSendSmsCode"
                    :input-style="{ height: '64px', fontSize: '18px' }"
                    placeholder="请输入验证码"
                    inputmode="numeric"
                    clearable
                  >
                    <template #prefix>
                      <Icon icon="material-symbols:sms-outline" class="input-icon" />
                    </template>
                  </ElInput>
                  <span v-if="resetPasswordErrors.code" class="error-message">{{
                    resetPasswordErrors.code
                  }}</span>
                </div>
                <ElButton
                  type="primary"
                  class="sms-button"
                  :disabled="countdown > 0 || isSendingSms"
                  @click="sendSmsCode"
                >
                  {{ smsButtonText }}
                </ElButton>
              </div>
              <span v-if="resetPasswordErrors.code" class="error-message">{{
                resetPasswordErrors.code
              }}</span>

              <!-- 新密码 -->
              <div class="form-group">
                <ElInput
                  v-model="resetPasswordForm.newPassword"
                  :input-style="{ height: '64px', fontSize: '18px' }"
                  type="password"
                  placeholder="请输入新密码"
                  show-password
                >
                  <template #prefix>
                    <Icon icon="material-symbols:lock-outline" class="input-icon" />
                  </template>
                </ElInput>
                <span v-if="resetPasswordErrors.newPassword" class="error-message">{{
                  resetPasswordErrors.newPassword
                }}</span>
              </div>
              <!-- 重置密码按钮 -->
              <div class="form-submit">
                <ElButton type="primary" class="login-button" @click="handleResetPassword">
                  重置密码
                </ElButton>
                <div class="back-to-login">
                  <a href="#" @click.prevent="switchTab('password')">返回登录</a>
                </div>
              </div>
            </form>
          </div>
          <div v-if="modeStep === 2" class="login-right">
            <div class="login-header">
              <h2>欢迎使用</h2>
            </div>
            <div class="phone-icon text-[20px] flex items-center">
              <i class="iconfont icon-shoujihao iconfont-c !text-[20px]"></i>
              <!-- 手机号 -->
              <span class="ml-2 g-c">{{
                loginActiveTab === 'password' ? loginForm.username : loginForm.phonenumber
              }}</span>
            </div>
            <p class="subtitle mt-4 mb-1">欢迎回来，请选择你的企业：</p>
            <div class="tenant-list">
              <!-- todo 优化样式 -->
              <div
                v-for="tenant in joinedTenantList"
                :key="tenant.tenantId"
                class="tenant-item"
                :class="{ active: selectedTenantId === tenant.tenantId }"
                @click="selectedTenantId = tenant.tenantId || ''"
              >
                <div class="tenant-info">
                  <div class="tenant-name g-c !text-[16px] flex items-center">
                    <i class="iconfont iconfont-c icon-qiye !text-[18px]"></i>
                    <span class="flex-1 ml-2">{{ tenant.companyName }}</span>
                    <i
                      v-show="selectedTenantId === tenant.tenantId"
                      class="iconfont iconfont-c icon-xuanze !text-[18px]"
                    ></i>
                  </div>
                </div>
              </div>
            </div>

            <div class="form-submit">
              <ElButton
                type="primary"
                :loading="loading"
                class="login-button"
                @click="handleChooseTenant"
              >
                开始使用
              </ElButton>
            </div>
          </div>
        </template>
      </div>
    </div>

    <!-- 滑块验证组件 -->
    <SlideCaptcha
      v-model:visible="showCaptcha"
      @success="handleCaptchaSuccess"
      @close="hideCaptchaDialog"
    />

    <!-- 横屏提示 -->
    <div id="orientation-tip" class="orientation-tip">
      <div class="orientation-content">
        <div class="phone-icon">📱</div>
        <p>为了更好的体验，请将手机竖屏使用</p>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.login-wrap {
  background: linear-gradient(135deg, #f9f9ff 0%, #ebe9fc 100%);
}
.login-container {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: url('@/assets/login/bg.png') no-repeat center center / 100% 100%;
}

.login-logo {
  width: 291px;
  height: 64px;
  position: absolute;
  top: 64px;
  left: 64px;
}

.login-box {
  display: flex;
  width: 1200px;
  height: 600px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  background: #fff;
}

.login-left {
  width: 572px;
  position: relative;
  overflow: hidden;
  background: #f0f2ff;
}

.login-bg {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.login-right {
  flex: 1;
  padding: 50px 64px;
  display: flex;
  flex-direction: column;
}

.login-header {
  margin-bottom: 40px;
  text-align: center;
}

.login-header h2 {
  font-size: 32px;
  font-weight: normal;
}

.login-tabs {
  display: flex;
  margin-bottom: 30px;
}

.login-tab {
  flex: 1;
  text-align: center;
  padding: 0 20px 10px;
  cursor: pointer;
  font-size: 18px;
  color: #666;
  position: relative;
}

.login-tab.active {
  color: #4865e8;
  font-weight: 500;
}

.login-tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 20%;
  width: 60%;
  height: 2px;
  background-color: #4865e8;
}

.login-form {
  flex: 1;
}

.form-group {
  margin-bottom: 24px;
}

.input-icon {
  color: #999;
  font-size: 20px;
}

.sms-group {
  display: flex;
  gap: 10px;
}

.sms-input-container {
  flex: 1;
}

.sms-button {
  width: 120px;
  white-space: nowrap;
  height: 64px !important;
  font-size: 18px;
}

.login-button {
  width: 100%;
  height: 64px !important;
  font-size: 18px;
}

.error-message {
  display: block;
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  font-size: 18px;
}

.checkbox-text {
  color: #666;
}

.link-text {
  color: #4865e8;
  text-decoration: none;
}

.forgot-password {
  color: #4865e8;
  font-size: 14px;
  text-decoration: none;
}

.form-submit {
  margin-top: 30px;
}

.back-to-login {
  text-align: center;
  margin-top: 16px;
}

.back-to-login a {
  color: #4865e8;
  text-decoration: none;
  font-size: 18px;
}

.tenant-list {
  margin-top: 20px;
  max-height: 160px;
  padding-right: 8px;
  overflow-y: auto;
}

.tenant-item {
  display: flex;
  align-items: center;
  height: 44px;
  padding: 0 16px;
  border-radius: 8px;
  margin-bottom: 12px;
  cursor: pointer;
  border: 1px solid #e0e0e0;
  transition: all 0.3s ease;

  &:hover {
    border-color: #4865e8;
    background-color: #f9f9ff;
  }

  &.active {
    border-color: #4865e8;
    background-color: #f0f2ff;
  }
}

.tenant-logo {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #4865e8;
  color: white;
  border-radius: 8px;
  margin-right: 16px;

  :deep(svg) {
    font-size: 24px;
  }
}

.tenant-info {
  flex: 1;
}

.tenant-name {
  font-size: 16px;
  font-weight: 500;
}

.tenant-domain {
  font-size: 14px;
  color: #666;
}

// 邀请码失效界面样式
.expire-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.expire-content {
  text-align: center;
  max-width: 400px;
}

.expire-image {
  width: 120px;
  height: auto;
  margin: 0 auto 24px auto;
  display: block;
}

.expire-title {
  font-size: 20px;
  font-weight: 500;
  color: #4865e8;
  margin-bottom: 16px;
}

.expire-description {
  font-size: 16px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 40px;
}

.expire-actions {
  display: flex;
  justify-content: center;
}

.expire-button {
  width: 200px;
  height: 48px !important;
  font-size: 16px;
}
</style>

<style lang="less" scoped>
@media (max-width: 768px) {
  .login-container {
    min-height: 100vh;
    padding: 0;
    background-size: cover;
  }

  .login-logo {
    width: 200px;
    height: 44px;
    top: 20px;
    left: 20px;
  }

  .login-box {
    flex-direction: column;
    width: 100%;
    height: auto;
    max-width: 100%;
    margin: 0;
    border-radius: 0;
    box-shadow: none;
    min-height: 100vh;
  }

  .login-left {
    display: none;
  }

  .login-right {
    padding: 20px;
    padding-top: 80px; // 为logo留出空间
    min-height: calc(100vh - 40px);
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
  }

  .login-header h2 {
    margin-top: 20px;
    font-size: 28px;
    margin-bottom: 10px;
  }

  .login-tabs {
    margin-bottom: 20px;
  }

  .login-tab {
    font-size: 16px;
    padding: 0 15px 8px;
  }

  .form-group {
    margin-bottom: 20px;
  }

  .sms-group {
    flex-direction: column;
    gap: 12px;
  }

  .sms-button {
    width: 100%;
    height: 48px !important;
    font-size: 16px;
  }

  .login-button {
    height: 48px !important;
    font-size: 16px;
  }

  .form-options {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    font-size: 14px;
  }

  .checkbox-text {
    font-size: 14px;
  }

  .forgot-password {
    font-size: 14px;
    align-self: flex-end;
  }

  .tenant-list {
    max-height: 200px;
    margin-top: 16px;
  }

  .tenant-item {
    height: 56px;
    padding: 0 12px;
    margin-bottom: 8px;
  }

  .tenant-name {
    font-size: 14px;
  }

  .expire-container {
    padding: 20px;
    min-height: calc(100vh - 40px);
  }

  .expire-content {
    max-width: 100%;
  }

  .expire-image {
    width: 100px;
    margin-bottom: 20px;
  }

  .expire-title {
    font-size: 18px;
    margin-bottom: 12px;
  }

  .expire-description {
    font-size: 14px;
    margin-bottom: 30px;
  }

  .expire-button {
    width: 100%;
    height: 48px !important;
    font-size: 16px;
  }

  // 手机端特定样式调整
  .text-\[18px\] {
    font-size: 16px !important;
  }

  .text-\[20px\] {
    font-size: 18px !important;
  }

  .phone-icon {
    font-size: 16px !important;
  }

  .subtitle {
    font-size: 14px;
  }

  // 输入框样式调整
  :deep(.el-input .el-input__wrapper) {
    height: 48px !important;
  }

  :deep(.el-input .el-input__inner) {
    font-size: 16px !important;
  }

  // checkbox样式调整
  :deep(.el-checkbox) {
    .el-checkbox__label {
      font-size: 14px !important;
    }
  }
}

// 防止横竖屏切换时的布局闪烁
.login-container {
  transition: none;
}

// 优化滚动条在移动端的表现
.tenant-list {
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

// 防止在手机上缩放时布局错乱
@media (max-width: 768px) {
  * {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
  }

  .login-wrap {
    touch-action: manipulation;
  }

  // 优化按钮触摸体验
  .login-button,
  .sms-button {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }

  // 优化输入框在手机端的表现
  :deep(.el-input .el-input__wrapper) {
    -webkit-appearance: none;
    -webkit-tap-highlight-color: transparent;
  }

  // 优化checkbox的触摸区域
  :deep(.el-checkbox) {
    .el-checkbox__input {
      touch-action: manipulation;
    }
  }

  // 优化链接的触摸反馈
  .link-text,
  .forgot-password {
    -webkit-tap-highlight-color: rgba(72, 101, 232, 0.2);
    touch-action: manipulation;
  }

  // 优化滚动条在移动端的表现
  .tenant-list {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }
}

// 横屏提示样式
.orientation-tip {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 9999;
  display: none;
  justify-content: center;
  align-items: center;
  color: white;

  .orientation-content {
    text-align: center;
    padding: 20px;

    .phone-icon {
      font-size: 60px;
      margin-bottom: 20px;
      animation: rotate 2s ease-in-out infinite;
    }

    p {
      font-size: 18px;
      line-height: 1.5;
      margin: 0;
    }
  }
}

@keyframes rotate {
  0%,
  100% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(90deg);
  }
}
</style>
