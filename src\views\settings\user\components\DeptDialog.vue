<script setup lang="ts">
import { ref, watch } from 'vue'
import type { FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { addDept, listDept, updateDept } from '@/api/system/dept'
import type { DeptForm, DeptVO } from '@/api/system/dept/types'
import { handleTree } from '@/utils/ruoyi'

const props = defineProps<{
  visible: boolean
  title: string
  editingDept?: DeptVO | null
}>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

const form = ref<DeptForm>({
  parentId: '',
  deptName: '',
  orderNum: 0, // 默认排序
  status: '0', // 默认启用
})

const deptFormRef = ref<any>(null)
const deptOptions = ref<DeptVO[]>([])
const isEdit = ref(false)

// 表单验证规则
const rules: FormRules = {
  deptName: [
    { required: true, message: '请输入部门名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' },
  ],
  parentId: [{ required: true, message: '请选择上级部门', trigger: 'change' }],
}

// 重置表单
const resetForm = () => {
  form.value = {
    parentId: '',
    deptName: '',
    orderNum: 0,
    status: '0',
  }
  deptFormRef.value?.resetFields()
}

// 加载部门树数据
const loadDeptOptions = async () => {
  try {
    const res = await listDept()
    if (res.data) {
      // 使用handleTree处理数据，转换为树形结构
      deptOptions.value = handleTree<DeptVO>(res.data, 'deptId', 'parentId', 'children')
    }
  } catch (error) {
    console.error('获取部门树失败:', error)
  }
}

// 监听visible变化，当打开对话框时初始化表单数据
const dialogVisible = ref(props.visible)
watch(
  () => props.visible,
  async newVal => {
    dialogVisible.value = newVal
    if (newVal) {
      // 先重置表单，避免显示上次编辑的内容
      resetForm()
      // 加载部门数据
      await loadDeptOptions()
      // 判断是否是编辑模式
      isEdit.value = !!props.editingDept
      console.log('isEdit.value', isEdit.value)

      if (isEdit.value && props.editingDept) {
        // 编辑模式，填充表单数据
        form.value = {
          deptId: props.editingDept.deptId || props.editingDept.id,
          parentId: props.editingDept.parentId,
          deptName: props.editingDept.deptName,
          orderNum: props.editingDept.orderNum,
          status: props.editingDept.status,
        }
      }
    }
  },
)

// 监听dialogVisible变化，同步到父组件
watch(
  () => dialogVisible.value,
  newVal => {
    emit('update:visible', newVal)
  },
)

// 提交表单
const handleSubmit = async () => {
  try {
    await deptFormRef.value.validate()

    let res
    if (isEdit.value) {
      // 编辑模式
      res = await updateDept(form.value)
      ElMessage.success('更新部门成功')
    } else {
      // 新增模式
      res = await addDept(form.value)
      ElMessage.success('添加部门成功')
    }

    if (res.code === 200) {
      dialogVisible.value = false
      emit('success')
    }
  } catch (error) {
    if (error instanceof Error) {
      console.error(isEdit.value ? '更新部门失败:' : '添加部门失败:', error)
    }
  }
}

watch(
  () => dialogVisible.value,
  newVal => {
    if (!newVal) {
      // 关闭对话框时重置表单
      resetForm()
    }
  },
)
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    draggable
    :title="title"
    width="500px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <!-- <div>form-{{ form }}</div> -->
    <!-- <div>form.parentId-{{ form.parentId }}-{{ form.parentId !== '0' || form.parentId !== 0 }}</div> -->
    <el-form ref="deptFormRef" :model="form" :rules="rules" label-width="80px" label-position="top">
      <el-row>
        <el-col v-if="form.parentId !== 0" :span="24">
          <el-form-item label="上级部门" prop="parentId">
            <!-- resolved 默认展开第一级和第二级 -->
            <el-tree-select
              v-model="form.parentId"
              :data="deptOptions"
              :props="{ value: 'deptId', label: 'deptName', children: 'children' } as any"
              value-key="deptId"
              placeholder="选择上级部门"
              check-strictly
              default-expand-all
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="部门名称" prop="deptName">
            <el-input v-model="form.deptName" placeholder="请输入部门名称" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>
