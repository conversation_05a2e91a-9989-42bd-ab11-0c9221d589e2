<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules, Sort } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import Pagination from '@/components/common/Pagination/index.vue'
import {
  getBizKbMyList,
  postBizKbCreateKnowledgeBase,
  postBizKbEnable,
  postBizKbRemove,
} from '@/api/knowledge-base'
import { formatFileSize } from '@/utils/formatter'
import type { AIModelVo } from '@/api'
import { getBizAiModelAvailableModels } from '@/api'
import EnableSwitch from '@/components/common/EnableSwitch.vue'
// import { useKnowledgeBaseStore } from '@/store/modules/knowledge-base'
import '@/typings/knowledge-base.d.ts'
import AddIconSvg from '@/assets/knowledge/add-icon.svg?component'
import { useSettingStore } from '@/store'

const settingStore = useSettingStore()
// const knowledgeBaseStore = useKnowledgeBaseStore()
const router = useRouter()
const loading = ref(false)

const tableData = ref<KnowledgeBase.KnowledgeBaseVo[]>([])
const dialogVisible = ref(false)
const dialogTitle = ref('新增知识库')

const queryFormRef = ref<FormInstance>()
const total = ref(0)

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  title: '',
  orderByColumn: '',
  isAsc: 'asc',
})

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1
  getKnowledgeList()
}

/** 重置按钮操作 */
// const resetQuery = () => {
//   queryFormRef.value?.resetFields()
//   queryParams.value.pageNum = 1
//   handleQuery()
// }

const handleAdd = () => {
  dialogTitle.value = '创建知识库'
  dialogVisible.value = true
}

const handleLookUp = (kbId: number | string) => {
  router.push({
    name: 'dataset',
    params: {
      kbId,
    },
  })
}

const handleDelete = (row: KnowledgeBase.KnowledgeBaseVo) => {
  ElMessageBox.confirm('确认删除该知识库吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    if (row.id !== undefined) {
      postBizKbRemove(row.id)
        .then(res => {
          if (res.code == 200) {
            ElMessage.success('删除成功')
            if (tableData.value.length === 1 && queryParams.value.pageNum > 1) {
              queryParams.value.pageNum -= 1
            }
            getKnowledgeList()
          } else {
            ElMessage.error(`删除失败: ${res.msg}`)
          }
        })
        .catch((error: any) => {
          ElMessage.error(`删除失败: ${error.message}`)
        })
    } else {
      ElMessage.error('知识库 ID 未定义')
    }
  })
}

const models = ref<AIModelVo[]>([])

const getKnowledgeList = () => {
  loading.value = true
  getBizKbMyList<{ rows: KnowledgeBase.KnowledgeBaseVo[] }>({
    pageNum: queryParams.value.pageNum,
    pageSize: queryParams.value.pageSize,
    name: queryParams.value.title,
    orderByColumn: queryParams.value.orderByColumn,
    isAsc: queryParams.value.isAsc,
  })
    .then(res => {
      tableData.value = res.rows || []
      total.value = 'total' in res ? res.total : 0
      loading.value = false
    })
    .catch(() => {
      loading.value = false
    })
}

const knowledgeType = ref(1)
const formRef = ref<FormInstance>()
interface RuleForm {
  name: string
  introduction: string
  modelName: number
}
const rules = reactive<FormRules<RuleForm>>({
  name: [{ required: true, message: '请输入数据集名称', trigger: 'blur' }],
  modelName: [{ required: true, message: '请选择文本向量模型', trigger: 'change' }],
})
const ruleForm = reactive<Partial<RuleForm>>({
  name: '',
  introduction: '',
  modelName: undefined,
  // shareStatus: 3,
})
onMounted(() => {
  getKnowledgeList()
})
const submitForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.validate(valid => {
    if (valid) {
      postBizKbCreateKnowledgeBase<string>({
        name: ruleForm.name,
        description: ruleForm.introduction,
        modelId: ruleForm.modelName,
        type: knowledgeType.value,
        shareStatus: 0,
      }).then(res => {
        if (res.code === 200) {
          ElMessage.success('创建成功')
          dialogVisible.value = false
          handleLookUp(res.data)
        } else {
          ElMessage.error(res.msg)
        }
      })
      resetForm(formEl)
    } else {
      console.log('error submit!')
    }
  })
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  dialogVisible.value = false
  formEl.resetFields()
}

/**
 * 编辑时间排序
 */
function sortChange({ prop, order }: Sort) {
  let isAsc = ''
  if (order === 'ascending') {
    isAsc = 'asc'
  } else if (order === 'descending') {
    isAsc = 'desc'
  }

  queryParams.value.pageNum = 1
  queryParams.value.orderByColumn = prop
  queryParams.value.isAsc = isAsc
  getKnowledgeList()
}

function handleEnable(id: number) {
  return postBizKbEnable(id)
}

// 获取向量模型列表
function getEmbeddingModelList() {
  getBizAiModelAvailableModels({
    type: 'embedding',
  }).then(res => {
    if (res.code === 200) {
      const tempList = (res.data || []).map(item => ({
        ...item,
        label: item.displayName,
        value: `${item.id}`,
        modelName: item.name,
      }))
      models.value = tempList // .filter(t => t.status === 1)
    }
  })
}
watch(dialogVisible, val => {
  if (val) {
    getEmbeddingModelList()
  }
})
</script>

<template>
  <div class="my-container">
    <div class="flex justify-between">
      <div class="flex">
        <el-button class="ex-el-button-gray" type="primary" plain @click="handleAdd()">
          <i class="iconfont iconfont-c icon-xinzeng mr-2"></i>
          新增
        </el-button>
      </div>
      <div class="flex justify-end">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="text-right">
          <el-form-item label="" prop="title" class="!mr-0">
            <el-input
              v-model="queryParams.title"
              style="width: 400px"
              placeholder="请输入你需要搜索的内容"
              clearable
              @clear="handleQuery"
              @keyup.enter="handleQuery"
            >
              <template #suffix>
                <el-icon>
                  <Search />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>
        </el-form>
        <!-- <el-button class="ml-3" type="primary" plain @click="handleQuery"> 高级搜索 </el-button> -->
      </div>
    </div>

    <el-table v-loading="loading" :data="tableData" style="width: 100%" @sort-change="sortChange">
      <el-table-column type="index" label="序号" width="60" />
      <el-table-column prop="name" label="知识" min-width="200" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="knowledge-name-in-table" @click="handleLookUp(row.id)">
            <img
              class="inline-block w-[18px] h-[18px]"
              src="@/assets/knowledge/text-icon.png"
              alt=""
            />
            <div class="knowledge-name">
              {{ row.name }}
              <el-tooltip
                v-if="!row.modelExist"
                effect="dark"
                content="向量模型被删除，请重新设置向量模型。"
                placement="bottom-start"
              >
                <img class="has-error" src="@/assets/chat/error.svg" alt="" />
              </el-tooltip>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="type" label="类型" width="120">
        <template #default="{ row }">
          <span v-if="row.type === 1">文本</span>
          <span v-if="row.type === 2">图像</span>
        </template>
      </el-table-column>
      <el-table-column prop="size" label="文件大小" width="120">
        <template #default="{ row }">
          {{ formatFileSize(row.size) }}
        </template>
      </el-table-column>
      <el-table-column prop="userName" label="拥有者" width="120" />
      <el-table-column prop="shareStatus" label="共享" width="120">
        <!-- 分享状态 0-私人 1-全体共享 2-指定部门共享 3-指定成员共享 -->
        <template #default="{ row }">
          <span v-if="row.shareStatus === 0">私人</span>
          <span v-if="row.shareStatus === 1">全体共享</span>
          <span v-if="row.shareStatus === 2">指定部门共享</span>
          <span v-if="row.shareStatus === 3">指定成员共享</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="editTime"
        label="编辑时间"
        width="180"
        sortable
        :sort-orders="['ascending', 'descending']"
      />
      <el-table-column prop="enabled" label="启用" width="90">
        <template #default="{ row }">
          <EnableSwitch v-model="row.enabled" :handle-func-promise="() => handleEnable(row.id)" />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180" fixed="right">
        <template #default="{ row }">
          <el-button link type="primary" @click="handleLookUp(row.id)"> 查看 </el-button>
          <el-button link type="danger" @click="handleDelete(row)"> 删除 </el-button>
        </template>
      </el-table-column>
    </el-table>

    <Pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getKnowledgeList"
    />
  </div>
  <el-dialog
    v-model="dialogVisible"
    draggable
    :title="dialogTitle"
    width="1000"
    :close-on-click-modal="false"
  >
    <div class="knowledge-type">
      <label class="knowledge-type-radio">
        <input v-model="knowledgeType" type="radio" name="type" :value="1" />
        <img class="" src="@/assets/knowledge/text-icon.png" alt="" />
        文本
      </label>

      <label class="knowledge-type-radio">
        <input v-model="knowledgeType" type="radio" name="type" disabled :value="2" />
        <img class="" src="@/assets/knowledge/table-icon.png" alt="" />
        表格
      </label>
    </div>
    <el-form
      ref="formRef"
      :model="ruleForm"
      label-width="auto"
      :rules="rules"
      label-position="top"
      hide-required-asterisk
    >
      <el-form-item prop="name">
        <template #label>
          <span class="knowledge-label reuqire">名称</span>
        </template>
        <el-input
          v-model="ruleForm.name"
          show-word-limit
          maxlength="100"
          placeholder="请输入数据集名称"
        />
      </el-form-item>
      <el-form-item prop="introduction">
        <template #label>
          <span class="knowledge-label">知识库介绍</span>
        </template>
        <el-input
          v-model="ruleForm.introduction"
          placeholder="请输入数据集内容的描述"
          type="textarea"
          show-word-limit
          maxlength="2000"
          :autosize="{ minRows: 5, maxRows: 5 }"
        />
      </el-form-item>
      <el-form-item prop="modelName">
        <template #label>
          <span class="knowledge-label reuqire">文本向量模型</span>
          <el-tooltip placement="top" effect="light">
            <template #content>
              <div class="w-[300px] text-[14px]">
                文本向量模型可以将自然语言转成向量，用于进行语义检索。<br />
                注意，不同文本向量模型无法一起使用，选择完文本向量模型后将无法修改。
              </div>
            </template>
            <img
              class="size-[20px] inline-block ml-[8px] align-text-bottom"
              src="@/assets/knowledge/question-icon.png"
              alt=""
            />
          </el-tooltip>
        </template>
        <Selector
          class="model-selector"
          v-model="ruleForm.modelName"
          :opentionList="models"
          placeholder="请选择文本向量模型"
        >
        </Selector>
        <!-- <el-select v-model="ruleForm.modelName" placeholder="请选择文本向量模型">
          <template #prefix>
            <img class="size-[20px]" src="@/assets/knowledge/model-icon.png" alt="" />
          </template>
          <el-option
            v-for="item in models"
            :key="item.id"
            :label="item.displayName || item.name"
            :value="item.id"
          />
        </el-select>-->
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="resetForm(formRef)">取消</el-button>
        <el-button type="primary" @click="submitForm(formRef)">确认创建</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="less">
.my-container {
  padding: 20px;
}

:deep(.el-table) {
  --el-table-header-bg-color: #f7f8fa;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.knowledge-type {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  font-size: 16px;
  color: #30343a;
  height: 78px;
  gap: 24px;
  margin-bottom: 24px;

  &-radio {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    border: 1px solid #dadde8;

    position: relative;
    cursor: pointer;

    input[type='radio'] {
      position: absolute;
      opacity: 0;
    }

    &:has(input[type='radio']:checked) {
      background: rgba(72, 101, 232, 0.15);
    }

    &:has(input[disabled]) {
      background-color: #f7f8fa;
      cursor: not-allowed;
      opacity: 0.6;
    }

    > img {
      width: 24px;
      height: 24px;
      margin-right: 12px;
    }
  }
}

.knowledge-label {
  display: inline-block;
  color: #30343a;
  font-size: 16px;
  &.reuqire::after {
    color: var(--el-color-danger);
    content: '*';
    margin-left: 4px;
  }
}

.knowledge-name-in-table {
  display: flex;
  align-items: center;
  cursor: pointer;

  .knowledge-name {
    max-width: 100%;
    margin-left: 4px;
    overflow: hidden;
    white-space: nowrap;
    min-width: 0;
    text-overflow: ellipsis;

    position: relative;
    padding-right: 26px;
  }

  .has-error {
    width: 17px;
    height: 17px;
    position: absolute;
    right: 0;
    top: 2px;
  }
}

:deep(.model-selector) {
  height: 40px;
}
</style>
