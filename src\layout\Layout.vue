<script setup lang="ts">
import { useRoute } from 'vue-router'
import { computed } from 'vue'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { AppSider } from '@/components/common'

const route = useRoute()
const { isMobile } = useBasicLayout()
const isPublicChat = route.path.includes('/chat/public/')
</script>

<template>
  <div class="h-full flex">
    <!-- 左侧导航栏 -->
    <div v-if="!isMobile && !isPublicChat" class="left w-[82px] border-r dark:border-neutral-800">
      <AppSider />
    </div>

    <!-- 主内容区域 -->
    <div class="divider flex-1">
      <router-view />
    </div>
  </div>
</template>

<style lang="less" scoped>
.left {
  box-shadow: 2px 0px 4px 0px rgba(105, 139, 207, 0.2);
}
.divider {
  padding-left: 2px;
}
</style>
