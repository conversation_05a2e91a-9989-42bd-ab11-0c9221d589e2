<script setup lang="ts">
import { AIModelVo } from '@/api'
import type { FormInstance, FormRules } from 'element-plus'
import { getBizAiModelCheckModelStatus } from '@/api/model/index'

defineOptions({
  name: 'DetectionModelDialog',
})

const props = defineProps<{
  modelList: AIModelVo[]
}>()

const dialogVisible = defineModel({ default: false })
const detectionLoading = ref(false)
const ruleFormRef = ref<FormInstance>()
const form = reactive({
  id: '',
})
interface RuleForm {
  id: string
}
const rules = reactive<FormRules<RuleForm>>({
  id: [{ required: true, message: '请选择检测的模型', trigger: 'change' }],
})

const options = computed(() => {
  const list = props.modelList || []
  return list.map(item => {
    return {
      label: item.displayName || item.name,
      value: item.id,
    }
  })
})

function handleBeforeClose(done: () => void) {
  if (!detectionLoading.value) {
    done()
  }
}

function handleClose() {
  resetForm()
}

/**
 * 重置表单数据
 */
function resetForm() {
  if (ruleFormRef.value) {
    ruleFormRef.value.resetFields()
  }
}

/**
 * 进行检测
 */
function handleSubmit() {
  if (ruleFormRef.value) {
    ruleFormRef.value.validate(valid => {
      if (valid) {
        console.log('校验通过')
        const selectModel = props.modelList.find(model => model.id === form.id)
        if (!selectModel) {
          return
        }
        // const params = {
        //   id: selectModel.id,
        //   platform: 'openai',
        //   type: selectModel.type,
        //   baseUrl: selectModel.baseUrl,
        //   name: selectModel.name,
        //   displayName: selectModel.displayName,
        // }
        detectionLoading.value = true
        getBizAiModelCheckModelStatus({ id: selectModel.id })
          .then(response => {
            // const responseData = response.data // 获取响应数据
            const responseMsg = response.msg // 获取响应消息
            // // 根据 responseData 判断模型状态：1 或 '1' 表示成功，否则表示失败
            // const status = responseData === 1 || responseData === '1' ? 1 : 2

            if (response.code !== 200) {
              // todo： 需要知道详细的原因
              // ElMessage.error('检测失败')
              const msgText = responseMsg?.trim() || '{}'
              try {
                // 清理消息字符串
                let cleanedMsg = msgText
                if (cleanedMsg.startsWith('"') && cleanedMsg.endsWith('"')) {
                  cleanedMsg = cleanedMsg.substring(1, cleanedMsg.length - 1)
                }
                cleanedMsg = cleanedMsg.replace(/\\"/g, '"')
                // 尝试解析为JSON并格式化
                try {
                  const jsonObj = JSON.parse(cleanedMsg)
                  errorMsg.value = JSON.stringify(jsonObj, null, 2)
                } catch {
                  errorMsg.value = cleanedMsg
                }
                handleShowErrorMsg()
              } catch {
                errorMsg.value = msgText
                handleShowErrorMsg()
              }
            } else {
              ElMessage.success('检测成功')
            }
          })
          .catch(error => {
            errorMsg.value = error as Record<string, any>
            handleShowErrorMsg()
          })
          .finally(() => {
            detectionLoading.value = false
            dialogVisible.value = false
          })
      }
    })
  }
}

const errorMsg = ref({})
const errorMsgVisible = ref(false)
const handleShowErrorMsg = () => {
  errorMsgVisible.value = !errorMsgVisible.value
}
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    width="600"
    :close-on-click-modal="false"
    :beoforeClose="handleBeforeClose"
    @close="handleClose"
  >
    <template #header>
      <span class="text-[24px] g-family-medium">请选择要检测的模型</span>
    </template>
    <el-form
      ref="ruleFormRef"
      :model="form"
      :rules="rules"
      label-width="auto"
      hide-required-asterisk
      label-position="top"
    >
      <el-form-item prop="id">
        <el-select v-model="form.id" placeholder="请选择">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button :disabled="detectionLoading" @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="detectionLoading" @click="handleSubmit">
          {{ detectionLoading ? '检测中...' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 错误信息弹框 -->
  <div v-if="errorMsgVisible" class="error-popup" @click="handleShowErrorMsg">
    <div class="error-popup-content" @click.stop>
      <div class="error-popup-body">
        <div class="mb-3">
          <span class="font-bold">错误返回：</span>
        </div>
        <div class="mb-3">
          <pre class="error-json">{{ errorMsg }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.error-popup {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
}

.error-popup-content {
  overflow: hidden;
  margin: 20px;
  position: relative;
  z-index: 10000;
  width: 631px;
  min-height: 250px;
  background: #fff0f0;
  border-radius: 8px;
  // border: 1px solid #f25b37;
}

.error-popup-body {
  padding: 20px;
  color: #d43610;
  line-height: 1.6;

  .mb-3 {
    margin-bottom: 12px;
    word-break: break-all;
  }

  .font-bold {
    color: #d43610;
    font-weight: 500;
    display: inline-block;
    width: 80px;
  }

  .error-json {
    padding: 12px;
    border-radius: 4px;
    font-family: monospace;
    white-space: pre-wrap;
    margin: 0;
  }
}
</style>
