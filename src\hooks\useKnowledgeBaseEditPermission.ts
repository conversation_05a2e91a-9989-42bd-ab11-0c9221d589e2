import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useKnowledgeBaseStore } from '@/store/modules/knowledge-base'

/**
 * 用于确定知识库上下文中的编辑权限的组合式函数。
 * @param kbId - 知识库的ID。
 * @returns 一个计算属性 `canEditHere`，如果在当前上下文中允许编辑，则为 true。
 */
export function useKnowledgeBaseEditPermission(kbId: string | undefined | null) {
  const route = useRoute()
  const knowledgeBaseStore = useKnowledgeBaseStore()

  const canEditHere = computed(() => {
    // 如果未提供 kbId，则无法编辑
    if (!kbId) {
      return false
    }

    const currentRouteName = route.name
    // 我的知识库、设置中知识库：始终允许编辑
    if (currentRouteName === 'dataset' || currentRouteName === 'searchTest' ||
        currentRouteName === 'settings-dataset' || currentRouteName === 'settings-searchTest') {
      return true
    }
    // 共享知识库：检查权限
    if (currentRouteName === 'share-dataset' || currentRouteName === 'share-searchTest') {
      return knowledgeBaseStore.hasEditPermission(kbId)
    }
    // 默认：在其他上下文中不允许编辑
    return false
  })

  return canEditHere
}