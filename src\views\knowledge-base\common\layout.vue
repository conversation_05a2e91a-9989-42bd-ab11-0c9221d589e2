<script lang="ts" setup>
import dataSetPng from '@/assets/knowledge/dataset-icon.png'
import searchTestPng from '@/assets/knowledge/search-icon.png'
import configPng from '@/assets/knowledge/config-icon.png'
import { useKnowledgeBaseStore } from '@/store/modules/knowledge-base'
import { kbIdKey } from '@/utils/constants'
import { getBizKbDetail } from '@/api/knowledge-base'
const knowledgeStore = useKnowledgeBaseStore()
const route = useRoute()
const kbId = computed<string>(() => route.params.kbId as string)

provide(kbIdKey, kbId.value)
const router = useRouter()
interface MenuItem {
  icon: string
  menuText: string
  routeName: string
}

const configMenu = {
  icon: configPng,
  menuText: '配置',
  routeName: 'config',
}

/**
 * 根据路由判断 当前当前要使用的菜单
 */
const menuList = computed(() => {
  const isShareKb = route.path.includes('share-knowledge')
  const isSettingsKb = route.path.includes('settings-knowledge')
  if (isShareKb) {
    return [
      {
        icon: dataSetPng,
        menuText: '数据集',
        routeName: 'share-dataset',
      },
      {
        icon: searchTestPng,
        menuText: '检索测试',
        routeName: 'share-searchTest',
      },
    ]
  } else if (isSettingsKb) {
    return [
      {
        icon: dataSetPng,
        menuText: '数据集',
        routeName: 'settings-dataset',
      },
      {
        icon: searchTestPng,
        menuText: '检索测试',
        routeName: 'settings-searchTest',
      },
      configMenu,
    ]
  } else {
    return [
      {
        icon: dataSetPng,
        menuText: '数据集',
        routeName: 'dataset',
      },
      {
        icon: searchTestPng,
        menuText: '检索测试',
        routeName: 'searchTest',
      },
      {
        icon: configPng,
        menuText: '配置',
        routeName: 'config',
      },
    ]
  }
})

const checkedMenu = computed(() => route.name)

/**
 * 页面跳转
 */
function menuJump(menu: MenuItem) {
  if (menu.menuText === checkedMenu.value) {
    return
  }

  router.replace({
    name: menu.routeName,
    params: route.params,
  })
}

function onBack() {
  router.back()
}

/**
 * 是否点击过提示
 */
const hasClickedTip = ref(false)

onMounted(() => {
  getBizKbDetail<KnowledgeBase.KnowledgeBaseBo>(kbId.value).then(res => {
    if (res.code === 200) {
      const isShareKb = route.path.includes('share-knowledge')
      const isSettingsKb = route.path.includes('settings-knowledge')
      if (isShareKb) {
        knowledgeStore.setKnowledgeTitle(`共享知识库 / ${res.data.name}`)
      } else if (isSettingsKb) {
        knowledgeStore.setKnowledgeTitle(`知识库 / ${res.data.name}`)
      } else {
        knowledgeStore.setKnowledgeTitle(`我的知识库 / ${res.data.name}`)
      }

      // 如果非共享知识库 && 当前路径为 数据集 && 模型失效，弹出提示 && 未点击过提示
      if (
        !isShareKb &&
        route.path.includes('dataset') &&
        !res.data.modelExist &&
        !hasClickedTip.value
      ) {
        dialogVisible.value = true
      }
    }
  })
})

onUnmounted(() => {
  knowledgeStore.setKnowledgeTitle('')
})

const dialogVisible = ref(false)

/**
 * 跳转至配置页面
 */
function goSettings() {
  dialogVisible.value = false
  hasClickedTip.value = true
  menuJump(configMenu)
}
</script>

<template>
  <div class="flex h-full">
    <div class="p-[16px] flex flex-col h-full border-r">
      <div class="menu-slide">
        <div
          v-for="menu in menuList"
          :key="menu.menuText"
          class="menu-item"
          :class="{ checked: checkedMenu === menu.routeName }"
          @click="menuJump(menu)"
        >
          <img class="inline-block" :src="menu.icon" alt="" />
          {{ menu.menuText }}
        </div>
      </div>
      <div class="pt-[16px]">
        <button
          class="w-full h-[38px] text-center text-[#4865E8] rounded-[8px] border border-[#4865E8]"
          @click="onBack"
        >
          <i class="iconfont icon-fanhui"></i>
          返回
        </button>
      </div>
    </div>

    <div class="flex-1 min-w-0 h-full">
      <router-view />
    </div>
  </div>
  <el-dialog v-model="dialogVisible" width="500">
    <template #header>
      <span class="text-[24px] g-family-medium">需要配置向量模型</span>
    </template>

    <span
      >系统检测到当前知识库的向量模型已失效，这将影响语义搜索和问答功能。请选择一个新的模型，并重新构建索引。</span
    >
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">稍后处理</el-button>
        <el-button type="primary" @click="goSettings"> 前往设置 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="less" scoped>
.menu-slide {
  width: 248px;
  flex: 1;
  overflow: auto;
  max-height: calc(100vh - 152px);
}
.menu-item {
  color: #30343a;
  font-size: 16px;
  border-radius: 8px;
  height: 48px;
  padding: 12px;
  box-sizing: border-box;
  cursor: pointer;
  &.checked {
    background: #e6eaf4;
  }

  > img {
    width: 24px;
    height: 24px;
    margin-right: 8px;
    vertical-align: middle;
  }
}
</style>
