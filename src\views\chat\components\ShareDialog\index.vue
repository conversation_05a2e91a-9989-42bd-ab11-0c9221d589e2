<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { ElButton, ElCheckbox, ElDialog, ElIcon, ElMessage } from 'element-plus'
import { ArrowLeft, Download, Picture } from '@element-plus/icons-vue'
import { toPng } from 'html-to-image'
import type { CheckboxValueType } from 'element-plus'

interface ChatMessage {
  index: number
  text: string
  inversion: boolean
  dateTime?: string
}

interface Props {
  visible: boolean
  conversationId: string
  chatTitle?: string
  modelName?: string
  modelList?: Array<{ label: string; value: string; icon?: string }>
  chatMessages?: Array<ChatMessage>
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const dialogVisible = ref(false)
const imageUrl = ref('')
const isGenerating = ref(false)
const currentStep = ref<'select' | 'preview'>('select')
const selectedMessages = ref<number[]>([])

// 获取当前模型信息
const currentModelInfo = computed(() => {
  if (!props.modelList || !props.modelName) return null
  return props.modelList.find(model => model.value === props.modelName)
})

// 获取聊天消息列表
const chatMessages = computed(() => {
  return props.chatMessages || []
})

// 是否全选
const isAllSelected = computed(() => {
  return (
    chatMessages.value.length > 0 && selectedMessages.value.length === chatMessages.value.length
  )
})

// 是否部分选中
const isIndeterminate = computed(() => {
  return (
    selectedMessages.value.length > 0 && selectedMessages.value.length < chatMessages.value.length
  )
})

// 监听 visible 变化
watch(
  () => props.visible,
  newVal => {
    dialogVisible.value = newVal
    if (newVal) {
      // 弹窗打开时重置状态
      imageUrl.value = ''
      isGenerating.value = false
      currentStep.value = 'select'
      // 默认全选
      selectedMessages.value = chatMessages.value.map(msg => msg.index)
    }
  },
)

// 监听内部 dialogVisible 变化
watch(dialogVisible, newVal => {
  emit('update:visible', newVal)
})

const handleClose = () => {
  dialogVisible.value = false
}

// 返回选择步骤
const goBackToSelect = () => {
  currentStep.value = 'select'
  imageUrl.value = ''
}

// 全选/取消全选
const handleSelectAll = (checked: CheckboxValueType) => {
  if (checked) {
    selectedMessages.value = chatMessages.value.map(msg => msg.index)
  } else {
    selectedMessages.value = []
  }
}

// 处理单个消息选择
const handleMessageSelect = (messageIndex: number, checked: CheckboxValueType) => {
  if (checked) {
    if (!selectedMessages.value.includes(messageIndex)) {
      selectedMessages.value.push(messageIndex)
    }
  } else {
    const idx = selectedMessages.value.indexOf(messageIndex)
    if (idx > -1) {
      selectedMessages.value.splice(idx, 1)
    }
  }
}

// 计算选中消息的最大宽度
const calculateMaxMessageWidth = (): number => {
  const chatMessagesContainer = document.querySelector('#image-wrapper .chat-messages-container')
  if (!chatMessagesContainer) return 800

  let maxWidth = 0
  const messageElements = chatMessagesContainer.children

  for (let i = 0; i < messageElements.length; i++) {
    if (selectedMessages.value.includes(i)) {
      const messageElement = messageElements[i] as HTMLElement

      // 查找真正的消息内容元素 .message-content
      const messageReplyElement = messageElement.querySelector('.message-content') as HTMLElement

      if (messageReplyElement) {
        const rect = messageReplyElement.getBoundingClientRect()
        maxWidth = Math.max(maxWidth, rect.width)
        console.log(`消息 ${i} 的 .message-content 宽度:`, rect.width)
      } else {
        // 如果没有找到 .message-reply，则使用整个消息元素的宽度作为备选
        const rect = messageElement.getBoundingClientRect()
        maxWidth = Math.max(maxWidth, rect.width)
        console.log(`消息 ${i} 的整体宽度 (备选):`, rect.width)
      }
    }
  }

  return maxWidth
}

// 创建带会话信息的完整截图
const createScreenshotWithHeader = async (): Promise<string> => {
  const chatContainer = document.querySelector('#image-wrapper')
  if (!chatContainer) {
    throw new Error('找不到聊天记录容器')
  }

  const chatMessagesContainer = chatContainer.querySelector('.chat-messages-container')
  if (!chatMessagesContainer) {
    throw new Error('找不到聊天消息容器')
  }

  console.log('开始截图，选中的消息索引:', selectedMessages.value)

  // 保存原始样式并设置为960px
  const originalWidth = (chatContainer as HTMLElement).style.width
  const originalMaxWidth = (chatContainer as HTMLElement).style.maxWidth
  const originalMargin = (chatContainer as HTMLElement).style.margin
  const originalJustifyContent = (chatContainer as HTMLElement).style.justifyContent
  console.log('保存原始样式:', {
    originalWidth,
    originalMaxWidth,
    originalMargin,
    originalJustifyContent,
  })

  // 设置容器宽度为960px，避免4K显示器上右侧留白过大
  // 同时确保内容左对齐，避免截图时位置偏移
  ;(chatContainer as HTMLElement).style.width = '960px'
  ;(chatContainer as HTMLElement).style.maxWidth = '960px'
  ;(chatContainer as HTMLElement).style.margin = '0'
  ;(chatContainer as HTMLElement).style.justifyContent = 'flex-start'

  // 计算最大消息宽度
  const maxMessageWidth = calculateMaxMessageWidth()
  console.log('最大消息宽度:', maxMessageWidth)

  // 获取所有消息元素
  const messageElements = chatMessagesContainer.children
  const hiddenElements: HTMLElement[] = []
  // 定义样式备份数组
  const originalStyles: { element: HTMLElement; marginBottom: string; paddingBottom: string }[] = []

  // 查找并隐藏 action-container 元素，避免占位导致内容偏移
  const actionContainers = document.querySelectorAll('.action-container.flex')
  const actionContainerOriginalDisplays: { element: HTMLElement; originalDisplay: string }[] = []

  actionContainers.forEach(container => {
    const element = container as HTMLElement
    actionContainerOriginalDisplays.push({
      element,
      originalDisplay: element.style.display || '',
    })
    element.style.display = 'none'
  })
  console.log('隐藏了', actionContainers.length, '个 action-container 元素')

  try {
    // 隐藏未选中的消息
    for (let i = 0; i < messageElements.length; i++) {
      if (!selectedMessages.value.includes(i)) {
        const element = messageElements[i] as HTMLElement
        element.style.display = 'none'
        hiddenElements.push(element)
      }
    }

    // 临时调整消息元素的间距样式，确保截图时间距正确
    const messageElementsArray = Array.from(messageElements) as HTMLElement[]

    messageElementsArray.forEach((element, index) => {
      if (selectedMessages.value.includes(index)) {
        // 保存原始样式
        originalStyles.push({
          element,
          marginBottom: element.style.marginBottom,
          paddingBottom: element.style.paddingBottom,
        })
        // 设置更大的间距确保截图时显示正确
        element.style.marginBottom = '24px'
        // element.style.paddingBottom = '8px'
      }
    })

    // 等待一下确保DOM更新
    await new Promise(resolve => setTimeout(resolve, 100))

    // 获取聊天容器的实际尺寸
    const containerRect = chatContainer.getBoundingClientRect()
    console.log('聊天容器实际尺寸:', containerRect.width, 'x', containerRect.height)
    console.log('设备像素比:', window.devicePixelRatio)

    // 让出主线程，确保 loading 动画能够流畅显示
    await new Promise(resolve => requestAnimationFrame(() => setTimeout(resolve, 0)))

    // 直接截取聊天容器
    // resolved HTMLElement里对话的间距太小了，为什么
    const chatDataUrl = await toPng(chatContainer as HTMLElement, {
      quality: 1.0,
      pixelRatio: 2, // 二倍像素比，提供更高清晰度
      backgroundColor: '#F7F8FA',
      filter: node => {
        const element = node as HTMLElement
        if (element.classList) {
          // 过滤掉操作按钮等
          return (
            !element.classList.contains('operation-buttons') &&
            !element.classList.contains('stop-loading-btn') &&
            !element.classList.contains('scroll-to-bottom-btn')
          )
        }
        return true
      },
    })

    // 使用Canvas合成最终图片
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      if (!ctx) {
        reject(new Error('无法创建canvas上下文'))
        return
      }

      const img = new Image()
      img.onload = () => {
        const pixelRatio = 2 // 与toPng配置保持一致
        const headerHeight = 100
        const padding = 40

        // 由于pixelRatio为2，截图的实际尺寸是容器尺寸的2倍
        // 需要将图片尺寸换算回逻辑尺寸进行计算
        const logicalImgWidth = img.width / pixelRatio
        const logicalImgHeight = img.height / pixelRatio

        console.log('截图原始尺寸:', img.width, 'x', img.height)
        console.log('逻辑尺寸:', logicalImgWidth, 'x', logicalImgHeight)
        console.log('像素比:', pixelRatio)
        console.log('最大消息内容宽度:', maxMessageWidth)

        // 使用逻辑尺寸计算画布逻辑宽度，但最终Canvas要乘以pixelRatio保持高分辨率
        const logicalCanvasWidth = Math.min(logicalImgWidth + padding * 2, 1400) // 最大不超过1400px
        const finalCanvasWidth = logicalCanvasWidth * pixelRatio // Canvas实际尺寸

        // resolved 此处不应该缩放，而是裁剪右侧超出的部分
        const availableWidth = logicalCanvasWidth - padding * 2
        let drawWidth, drawHeight, contentX, sourceX, sourceY, sourceWidth, sourceHeight

        // todo 怎么优化呢？这样裁剪的时候，聊天内容完整了，但是本来居于聊天区域水平中间位置的"新的上下文"、时间，会无法正确居中
        if (logicalImgWidth > availableWidth) {
          // 需要裁剪右侧超出的部分
          drawWidth = availableWidth * pixelRatio // Canvas实际绘制尺寸
          drawHeight = logicalImgHeight * pixelRatio
          contentX = padding * pixelRatio

          // 从原图的左侧开始，裁剪到可用宽度
          sourceX = 0
          sourceY = 0
          sourceWidth = availableWidth * pixelRatio
          sourceHeight = img.height

          console.log('需要裁剪，逻辑图片宽度:', logicalImgWidth, '可用宽度:', availableWidth)
          console.log('Canvas实际绘制尺寸:', drawWidth, 'x', drawHeight)
          console.log('裁剪区域:', sourceWidth, 'x', sourceHeight)
        } else {
          // 不需要裁剪，居中显示
          drawWidth = logicalImgWidth * pixelRatio // Canvas实际绘制尺寸
          drawHeight = logicalImgHeight * pixelRatio
          contentX = ((logicalCanvasWidth - logicalImgWidth) / 2) * pixelRatio

          // 使用完整的原图
          sourceX = 0
          sourceY = 0
          sourceWidth = img.width
          sourceHeight = img.height

          console.log('不需要裁剪，居中显示')
          console.log('Canvas实际绘制尺寸:', drawWidth, 'x', drawHeight)
        }

        const logicalCanvasHeight = drawHeight / pixelRatio + headerHeight + padding * 2 + 40 // 40是时间戳区域
        const finalCanvasHeight = logicalCanvasHeight * pixelRatio

        // 设置最终的画布尺寸（高分辨率）
        canvas.width = finalCanvasWidth
        canvas.height = finalCanvasHeight

        // 缩放Canvas上下文以支持高分辨率
        ctx.scale(pixelRatio, pixelRatio)

        // 填充背景
        ctx.fillStyle = '#F7F8FA'
        ctx.fillRect(0, 0, logicalCanvasWidth, logicalCanvasHeight)

        // 绘制头部背景
        ctx.fillStyle = 'white'
        ctx.fillRect(padding, padding, logicalCanvasWidth - padding * 2, headerHeight - 20)

        // 绘制AI图标
        ctx.fillStyle = '#f0f0f0'
        ctx.fillRect(padding + 20, padding + 20, 40, 40)

        ctx.fillStyle = '#666'
        ctx.font = 'bold 14px Arial'
        ctx.textAlign = 'center'
        ctx.fillText('AI', padding + 40, padding + 45)

        // 绘制标题
        ctx.fillStyle = '#1a1a1a'
        ctx.font = '600 18px Arial'
        ctx.textAlign = 'left'
        ctx.fillText(props.chatTitle || '聊天记录', padding + 80, padding + 40)

        // 绘制选择信息
        ctx.fillStyle = '#999'
        ctx.font = '12px Arial'
        ctx.fillText(
          `已选择 ${selectedMessages.value.length}/${chatMessages.value.length} 条消息`,
          padding + 80,
          padding + 60,
        )

        // resolved 生成图中，右上角的模型名超出宽度了，这个宽度是固定的吗？
        // 绘制模型信息
        let modelText = currentModelInfo.value?.label || props.modelName || '未知模型'

        // 设置字体以便正确测量文本宽度
        ctx.font = '500 14px Arial'

        // 计算可用宽度（画布宽度 - 左右边距 - 标题区域预留宽度）
        const maxModelWidth = Math.min(200, logicalCanvasWidth - padding * 2 - 300) // 最大200px，确保不与标题重叠
        const fullTextWidth = ctx.measureText(modelText).width

        // 如果文本过长，进行截断处理
        if (fullTextWidth > maxModelWidth - 24) {
          // 24是内边距
          // 逐字符截断直到宽度合适
          while (
            ctx.measureText(`${modelText}...`).width > maxModelWidth - 24 &&
            modelText.length > 1
          ) {
            modelText = modelText.slice(0, -1)
          }
          if (modelText.length > 1) {
            modelText += '...'
          }
        }

        const modelWidth = Math.min(ctx.measureText(modelText).width + 24, maxModelWidth)

        // 绘制模型背景框
        ctx.fillStyle = '#f5f7fa'
        ctx.fillRect(logicalCanvasWidth - padding - modelWidth - 20, padding + 24, modelWidth, 32)

        // 绘制模型文本
        ctx.fillStyle = '#666'
        ctx.fillText(modelText, logicalCanvasWidth - padding - modelWidth - 8, padding + 44)

        // 绘制聊天内容（使用逻辑坐标，因为已经scale了）
        ctx.drawImage(
          img,
          sourceX,
          sourceY,
          sourceWidth,
          sourceHeight,
          contentX / pixelRatio,
          padding + headerHeight,
          drawWidth / pixelRatio,
          drawHeight / pixelRatio,
        )

        // 绘制时间戳
        ctx.fillStyle = '#999'
        ctx.font = '12px Arial'
        ctx.textAlign = 'center'
        ctx.fillText(
          `生成时间：${new Date().toLocaleString()}`,
          logicalCanvasWidth / 2,
          logicalCanvasHeight - 20,
        )

        resolve(canvas.toDataURL('image/png', 1.0))
      }

      img.onerror = () => reject(new Error('图片加载失败'))
      img.src = chatDataUrl
    })
  } finally {
    // 恢复容器原始样式
    ;(chatContainer as HTMLElement).style.width = originalWidth
    ;(chatContainer as HTMLElement).style.maxWidth = originalMaxWidth
    ;(chatContainer as HTMLElement).style.margin = originalMargin
    ;(chatContainer as HTMLElement).style.justifyContent = originalJustifyContent
    console.log('恢复容器原始样式:', {
      originalWidth,
      originalMaxWidth,
      originalMargin,
      originalJustifyContent,
    })

    // 恢复所有隐藏的消息
    hiddenElements.forEach(element => {
      element.style.display = ''
    })

    // 恢复所有修改的样式
    originalStyles.forEach(({ element, marginBottom, paddingBottom }) => {
      element.style.marginBottom = marginBottom
      element.style.paddingBottom = paddingBottom
    })

    // 恢复 action-container 元素的显示状态
    actionContainerOriginalDisplays.forEach(({ element, originalDisplay }) => {
      element.style.display = originalDisplay
    })
    console.log(
      '恢复了',
      actionContainerOriginalDisplays.length,
      '个 action-container 元素的显示状态',
    )

    console.log('截图完成，已恢复所有消息显示和容器样式')
  }
}

// 生成图片
const generateImage = async () => {
  try {
    isGenerating.value = true

    // 使用 setTimeout 延迟执行，让 loading 动画先渲染
    await new Promise(resolve => setTimeout(resolve, 50))

    const dataUrl = await createScreenshotWithHeader()
    imageUrl.value = dataUrl
    ElMessage.success('图片生成成功！')
  } catch (error) {
    console.error('生成图片失败:', error)
    ElMessage.error('生成图片失败，请稍后重试')
  } finally {
    isGenerating.value = false
  }
}

// 开始生成
const startGenerate = () => {
  if (selectedMessages.value.length === 0) {
    ElMessage.warning('请至少选择一条聊天记录')
    return
  }
  currentStep.value = 'preview'
  setTimeout(() => {
    generateImage()
  }, 100)
}

// 下载图片
const downloadImage = () => {
  if (!imageUrl.value) return

  try {
    const link = document.createElement('a')
    const timestamp = new Date().toLocaleString().replace(/[/:]/g, '-')
    const titlePart = props.chatTitle ? `_${props.chatTitle}` : ''
    const selectedCount = selectedMessages.value.length
    link.download = `聊天记录${titlePart}_${selectedCount}条消息_${timestamp}.png`
    link.href = imageUrl.value

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success('图片下载成功！')
  } catch (error) {
    console.error('下载失败:', error)
    ElMessage.error('下载失败，请稍后重试')
  }
}
</script>

<template>
  <!-- resolved 点击这个按钮时候，先出现复选框能让用户勾选需要分享的聊天记录，再对应生成图片 -->
  <ElDialog
    v-model="dialogVisible"
    :title="currentStep === 'select' ? '选择要分享的聊天记录' : '分享聊天记录'"
    width="1400px"
    :before-close="handleClose"
  >
    <!-- 步骤1: 选择消息 -->
    <div v-if="currentStep === 'select'" class="message-selection">
      <div class="selection-header">
        <ElCheckbox
          :model-value="isAllSelected"
          :indeterminate="isIndeterminate"
          @update:model-value="handleSelectAll"
        >
          全选
        </ElCheckbox>
        <div class="selected-count">已选择: {{ selectedMessages.length }} 条</div>
      </div>

      <div class="message-list">
        <div
          v-for="(message, index) in chatMessages"
          :key="index"
          class="message-item"
          :class="{ 'user-message': message.inversion, 'ai-message': !message.inversion }"
        >
          <ElCheckbox
            :model-value="selectedMessages.includes(message.index)"
            class="message-checkbox"
            @update:model-value="
              (checked: CheckboxValueType) => handleMessageSelect(message.index, checked)
            "
          />
          <div class="message-content">
            <div class="message-role">
              {{ message.inversion ? '我' : 'AI' }}
            </div>
            <div class="message-text">
              {{
                message.text.length > 100 ? `${message.text.substring(0, 100)}...` : message.text
              }}
            </div>
            <div v-if="message.dateTime" class="message-time">
              {{ message.dateTime }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 步骤2: 预览和生成 -->
    <div v-else-if="currentStep === 'preview'" class="share-dialog-content">
      <div v-if="imageUrl" class="image-preview">
        <img :src="imageUrl" alt="聊天记录截图" class="chat-image" />
      </div>

      <div v-else-if="isGenerating" class="generating-state">
        <div class="custom-loading">
          <div class="loading-spinner"></div>
          <p class="generating-text">正在生成图片...</p>
        </div>
      </div>

      <div v-else class="initial-state">
        <ElIcon class="share-icon"><Picture /></ElIcon>
        <p>正在准备生成 {{ selectedMessages.length }} 条消息的截图</p>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleClose">取消</ElButton>

        <template v-if="currentStep === 'select'">
          <ElButton type="primary" :disabled="selectedMessages.length === 0" @click="startGenerate">
            生成图片 ({{ selectedMessages.length }} 条)
          </ElButton>
        </template>

        <template v-else>
          <ElButton @click="goBackToSelect">
            <ElIcon class="mr-1"><ArrowLeft /></ElIcon>
            重新选择
          </ElButton>
          <ElButton v-if="!imageUrl && !isGenerating" type="primary" @click="generateImage">
            重新生成
          </ElButton>
          <ElButton v-if="imageUrl" type="primary" @click="downloadImage">
            <ElIcon class="mr-1"><Download /></ElIcon>
            下载图片
          </ElButton>
        </template>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped lang="less">
.message-selection {
  max-height: 500px;

  .selection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f5f7fa;
    border-radius: 8px;
    margin-bottom: 16px;

    .selected-count {
      color: #666;
      font-size: 14px;
    }
  }

  .message-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e4e7ed;
    border-radius: 8px;

    .message-item {
      display: flex;
      align-items: flex-start;
      padding: 12px;
      border-bottom: 1px solid #f0f0f0;
      transition: background-color 0.2s;

      &:hover {
        background: #f9f9f9;
      }

      &:last-child {
        border-bottom: none;
      }

      &.user-message {
        background: rgba(64, 158, 255, 0.05);
      }

      &.ai-message {
        // background: rgba(103, 194, 58, 0.05);
      }

      .message-checkbox {
        margin-right: 12px;
        margin-top: 4px;
      }

      .message-content {
        flex: 1;
        min-width: 0;

        .message-role {
          font-size: 12px;
          font-weight: 600;
          color: #4c5cec;
          margin-bottom: 4px;
        }

        .message-text {
          color: #333;
          font-size: 14px;
          line-height: 1.5;
          word-break: break-word;
          margin-bottom: 4px;
        }

        .message-time {
          color: #999;
          font-size: 12px;
        }
      }
    }
  }
}

.share-dialog-content {
  min-height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.image-preview {
  width: 100%;
  max-height: 400px;
  overflow: auto;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  background: #f5f7fa;

  .chat-image {
    width: 100%;
    height: auto;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.generating-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;

  .custom-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #4c5cec;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }

    .generating-text {
      margin-top: 16px;
      color: #666;
      font-size: 14px;
    }
  }
}

.initial-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;

  .share-icon {
    font-size: 48px;
    margin-bottom: 16px;
    color: #d0d0d0;
  }

  p {
    font-size: 14px;
    margin: 0;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.mr-1 {
  margin-right: 4px;
}
</style>
