import { nextTick, ref } from 'vue'

import { useChat } from './useChat'
import { getBizChatGetChatRecord, getBizConversationMessageSummary, getBizGuestChatRecord, getBizGuestConversationMessageSummary } from '@/api'
import { useChatStore, useSettingStore } from '@/store'
import { usePublicChat } from './usePublicChat'

export function useChatRecords(
  conversationId: string,
  scrollToBottom: () => void,
  maintainScrollPosition: (callback: () => Promise<void>) => Promise<void>,
) {
  const { addChat, prependChat, updateChatSome } = useChat()
  const { isPublicChat } = usePublicChat()
  const getChatGetChatRecord = isPublicChat ? getBizGuestChatRecord : getBizChatGetChatRecord
  const getConversationMessageSummary = isPublicChat ? getBizGuestConversationMessageSummary : getBizConversationMessageSummary

  const chatStore = useChatStore()
  const settingStore = useSettingStore()

  // 分页和加载状态
  const chatRecordPageNum = ref(1) // 当前页码
  const chatRecordPageSize = ref(20) // 每页记录数
  const hasMoreChatRecords = ref(false) // 是否有更多历史记录可加载
  const isLoadingMoreRecords = ref(false) // 是否正在加载历史记录
  const initialLoadComplete = ref(false) // 标记初始加载是否完成
  const latestChatId = ref<string | undefined>(undefined)

  // 获取聊天记录
  async function fetchChatRecord(loadMore = false, currentModel: string) {
    // 如果是初始加载，先设置初始加载状态为false
    if (!loadMore) {
      initialLoadComplete.value = false
    }

    // 如果是加载更多，则递增页码，否则重置为1
    if (loadMore) {
      chatRecordPageNum.value++
    } else {
      chatRecordPageNum.value = 1
      // 初始加载时直接清空当前对话记录
      chatStore.clearChatByConversationId(conversationId)
    }

    // 获取对话记录
    try {
      isLoadingMoreRecords.value = true
      // biz/chat/getChatRecord，统一使用reasoningContent输出推理
      // resolved 本接口之前返回的格式是content字段中包含<think>...</think>输出，现在返回的格式是reasoningContent字段输出推理
      const { rows: data, total } = await getChatGetChatRecord<{
        rows: Chat.ChatRecordVo[]
        total: number
      }>({
        orderByColumn: 'id',
        isAsc: 'desc',
        conversationId,
        pageNum: chatRecordPageNum.value,
        pageSize: chatRecordPageSize.value,
      })

      if (data && Array.isArray(data)) {
        // 检查是否有更多数据需要加载
        hasMoreChatRecords.value = chatRecordPageNum.value * chatRecordPageSize.value < total

        // 准备添加历史记录
        // 如果是初始加载，我们需要倒序数据以便最新的消息显示在底部
        // 如果是加载更多，则按原始顺序添加在前面
        const recordsToAdd = loadMore ? data : data.reverse()

        recordsToAdd.forEach(item => {
          // 处理图片信息
          let imageInfoList:
            | Array<{ imageUrl: string; ossId: string; fileName?: string }>
            | undefined

          console.log('处理聊天记录项:', {
            role: item.role,
            content: `${item.content?.substring(0, 50)}...`,
            imageInfo: item.imageInfo,
          })

          if (item.imageInfo) {
            console.log('原始 imageInfo:', item.imageInfo, '类型:', typeof item.imageInfo)

            // 处理不同格式的 imageInfo
            if (typeof item.imageInfo === 'string') {
              try {
                // 如果是字符串，尝试解析 JSON
                const parsedImageInfo = JSON.parse(item.imageInfo)
                console.log('解析后的图片信息:', parsedImageInfo)

                // 处理后端返回的格式: {"imageUrls":["url1", "url2"]}
                if (parsedImageInfo.imageUrls && Array.isArray(parsedImageInfo.imageUrls)) {
                  imageInfoList = parsedImageInfo.imageUrls.map((url: string, index: number) => ({
                    imageUrl: url,
                    ossId: `history-${Date.now()}-${index}`, // 历史记录可能没有 ossId，生成一个临时的
                    fileName: `历史图片${index + 1}`,
                  }))
                  console.log('设置 imageInfoList (从 imageUrls):', imageInfoList)
                }
                // 处理标准格式: [{imageUrl: "...", ossId: "..."}]
                else if (Array.isArray(parsedImageInfo)) {
                  imageInfoList = parsedImageInfo
                  console.log('设置 imageInfoList (标准格式):', imageInfoList)
                }
              } catch (error) {
                console.warn('解析图片信息失败:', error, '原始数据:', item.imageInfo)
              }
            } else if (Array.isArray(item.imageInfo)) {
              // 如果直接是数组
              imageInfoList = item.imageInfo
              console.log('设置 imageInfoList (直接数组):', imageInfoList)
            } else if (typeof item.imageInfo === 'object' && item.imageInfo !== null) {
              // 如果是对象，尝试转换为数组
              imageInfoList = [item.imageInfo]
              console.log('设置 imageInfoList (对象转数组):', imageInfoList)
            }
          }

          if (loadMore) {
            // 加载更多时，使用prependChat将消息添加到前面
            prependChat(conversationId, {
              dateTime: item.updateTime || new Date().toLocaleString(),
              text: item.content,
              inversion: item.role === 'user',
              error: false,
              loading: false,
              conversationOptions: {
                conversationId,
                chatId: item.chatId,
                id: item.id,
              },
              toolExecutionList: item.toolExecutionList,
              chatContentRecordList: item.chatContentRecordList,
              reasoningContent: item.reasoningContent,
              imageInfoList,
            })
          } else {
            // 初始加载时，使用addChat将消息添加到后面
            addChat(
              conversationId,
              {
                dateTime: item.updateTime || new Date().toLocaleString(),
                text: item.content,
                inversion: item.role === 'user',
                error: false,
                loading: false,
                conversationOptions: {
                  conversationId,
                  chatId: item.chatId,
                  id: item.id,
                },
                toolExecutionList: item.toolExecutionList,
                chatContentRecordList: item.chatContentRecordList,
                reasoningContent: item.reasoningContent,
                imageInfoList,
              },
              true,
            )
          }
        })

        // 仅在首次加载时设置最新的chatId
        if (!loadMore && data.length > 0) {
          const lastMessage = data[data.length - 1]
          latestChatId.value = lastMessage.chatId
        }
      }
    } catch (error) {
      console.error('获取对话记录失败:', error)
      if (!loadMore) {
        latestChatId.value = undefined
      }
    } finally {
      isLoadingMoreRecords.value = false
      // 初始加载完成
      if (!loadMore) {
        initialLoadComplete.value = true
        // 在初始加载完成后滚动到底部
        nextTick(() => {
          scrollToBottom()
        })
      }
    }
  }

  // 加载更多聊天记录
  async function loadMoreChatRecords(currentModel: string) {
    if (isLoadingMoreRecords.value || !hasMoreChatRecords.value) return

    // 直接使用useScroll钩子中提供的maintainScrollPosition方法
    await maintainScrollPosition(async () => {
      await fetchChatRecord(true, currentModel)
    })
  }

  // 获取摘要
  async function getSummary(conversationId: string) {
    try {
      if (!settingStore?.defaultModel?.summaryModelId) return
      const dataSources = chatStore.getChatByConversationId(conversationId)
      // 从最后一条消息中获取 chatId
      const lastMessage = dataSources[dataSources.length - 1]
      const chatId = lastMessage?.conversationOptions?.chatId

      if (!chatId) {
        console.error('获取摘要失败: 无 chatId')
        return
      }


      const { data } = await getConversationMessageSummary({
        chatId,
        conversationId,
      })

      if (data) {
        // 更新历史记录中的摘要
        const history = chatStore.history.find(
          item => item.conversationId.toString() === conversationId,
        )
        if (history) {
          chatStore.updateHistory(history.conversationId, {
            summary: data as string, // 直接使用返回的 data 作为摘要文本，但需要明确类型转换
          })
        }
      }
    } catch (error) {
      console.error('获取摘要失败:', error)
    }
  }

  return {
    chatRecordPageNum,
    chatRecordPageSize,
    hasMoreChatRecords,
    isLoadingMoreRecords,
    initialLoadComplete,
    latestChatId,
    fetchChatRecord,
    loadMoreChatRecords,
    getSummary,
  }
}
