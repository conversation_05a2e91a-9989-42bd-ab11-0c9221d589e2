<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import MarkdownEditor from '@/components/MarkdownEditor.vue'
import { postBizAgentPrompt } from '@/api/agent'
import { useUserStore } from '@/store/modules/user'

const props = defineProps<{
  visible: boolean
  modelValue: string
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'useOptimized', value: string): void
}>()

// 优化相关变量
const optimizeLoading = ref(false)
const inputPrompt = ref('')
const optimizedPrompt = ref('')
// 添加一个变量来标记是否完成
const isOptimizationDone = ref(false)

// 用于控制SSE流的变量
let controller: AbortController | null = null
let currentReader: ReadableStreamDefaultReader<Uint8Array> | null = null
let timeoutCheckInterval: number | null = null

// 处理流式响应
const coreStreamResponse = async (response: Response) => {
  const reader = response.body?.getReader()
  const decoder = new TextDecoder()
  let accumulatedContent = ''
  let hasError = false
  let lastMessageTime = Date.now()
  let isDone = false
  // 添加标记，用于判断是否收到了START事件
  let hasStartEvent = false
  // 添加START事件时间戳
  let startEventTime = 0

  // 重置完成状态
  isOptimizationDone.value = false

  if (!reader) throw new Error('Failed to get response reader')

  // 保存当前reader引用
  currentReader = reader

  // 创建超时检查定时器
  timeoutCheckInterval = window.setInterval(() => {
    const now = Date.now()
    // 如果收到START事件但超过29秒没有后续数据，判定为异常
    if (hasStartEvent && !accumulatedContent && now - startEventTime > 29000) {
      if (timeoutCheckInterval) clearInterval(timeoutCheckInterval)
      reader.cancel() // 取消读取流
      ElMessage.error('服务器响应异常，收到START事件后无内容返回')
      optimizeLoading.value = false
      return
    }
    // 常规超时检测
    if (now - lastMessageTime > 29000) {
      // 改为29秒超时，确保在nginx断开连接前处理
      if (timeoutCheckInterval) clearInterval(timeoutCheckInterval)
      reader.cancel() // 取消读取流
      ElMessage.error('服务器响应超时，请重试')
      optimizeLoading.value = false
    }
  }, 1000) // 每秒检查一次

  try {
    while (true) {
      try {
        const { done, value } = await reader.read()

        if (done) {
          if (timeoutCheckInterval) clearInterval(timeoutCheckInterval)
          break
        }

        // 更新最后消息时间
        lastMessageTime = Date.now()

        const chunk = decoder.decode(value)

        // 直接检查整个chunk是否包含错误信息
        if (chunk.includes('"code":500') || chunk.includes('"code": 500')) {
          try {
            const errorData = JSON.parse(chunk)
            if (errorData.code !== 200) {
              if (timeoutCheckInterval) clearInterval(timeoutCheckInterval)
              ElMessage.error(errorData.msg || '请求异常')
              optimizeLoading.value = false
              return
            }
          } catch (e) {
            // 尝试直接使用整个错误信息
            if (timeoutCheckInterval) clearInterval(timeoutCheckInterval)
            const errorMatch = chunk.match(/"msg"\s*:\s*"([^"]+)"/)
            const errorMsg = errorMatch ? errorMatch[1] : '服务器内部错误'
            ElMessage.error(errorMsg)
            optimizeLoading.value = false
            return
          }
        }
        // 可能存在401的情况 "code":401
        if (chunk.includes('"code":401') || chunk.includes('"code": 401')) {
          if (timeoutCheckInterval) clearInterval(timeoutCheckInterval)
          ElMessage.warning('未授权，请重新登录')
          optimizeLoading.value = false
          return
        }

        const lines = chunk.split('\n')

        for (const line of lines) {
          // 处理事件行
          if (line.startsWith('event:')) {
            const event = line.substring(6).trim()
            if (event === '[ERROR]') {
              hasError = true
              continue
            }
            // 检测START事件
            if (event === '[START]') {
              hasStartEvent = true
              startEventTime = Date.now() // 记录START事件的时间
              continue
            }
            // 检测DONE事件
            if (event === '[DONE]') {
              isDone = true
              // 设置优化完成状态
              isOptimizationDone.value = true
              continue
            }
            // 点击handleStop，会在sse数据流中返回[CLOSE]事件，这种情况需要处理中断数据流
            if (event === '[CLOSE]') {
              if (timeoutCheckInterval) clearInterval(timeoutCheckInterval)
              reader.cancel('用户取消') // 取消读取流
              optimizeLoading.value = false
              isDone = true // 标记为已完成
              console.log('收到[CLOSE]事件，已停止数据流')
              return // 直接返回，终止处理
            }
          } else if (line.startsWith('data:')) {
            const dataContent = line.substring(5).trim()
            if (!dataContent) continue

            try {
              // 如果是错误事件，dataContent 就是错误消息
              if (hasError) {
                if (timeoutCheckInterval) clearInterval(timeoutCheckInterval)
                ElMessage.error('请求异常')
                return
              }

              const data = JSON.parse(dataContent)

              // 如果已经收到DONE事件，只处理id信息，不再更新显示内容
              if (isDone) {
                // 使用完整的内容替换累加内容
                if (data.content) {
                  optimizedPrompt.value = data.content
                }
                // 设置优化完成状态
                isOptimizationDone.value = true
                continue
              }

              // 更新聊天内容
              if (data.content !== undefined) {
                accumulatedContent += data.content
                optimizedPrompt.value = accumulatedContent
              }

              if (data.isEnd) {
                optimizeLoading.value = false
                // 如果后端返回isEnd标志，也设置为完成状态
                isOptimizationDone.value = true
              }
            } catch (error) {
              console.error('解析SSE数据失败:', error)
            }
          }
        }
      } catch (error) {
        if (timeoutCheckInterval) clearInterval(timeoutCheckInterval)
        throw error
      }
    }
  } catch (error) {
    if (timeoutCheckInterval) clearInterval(timeoutCheckInterval)
    throw error
  }
}

// 开始优化按钮
// resolved 在优化的时候，点击取消按钮，要确认是否取消
const handleStartOptimize = async () => {
  try {
    optimizeLoading.value = true
    optimizedPrompt.value = ''
    inputPrompt.value = props.modelValue
    // 重置完成状态
    isOptimizationDone.value = false

    // 创建AbortController用于取消请求
    controller = new AbortController()

    // resolved 本接口已改为sse流式输出，需要修改
    // 使用fetch API发起SSE请求
    const token = useUserStore().token
    const response = await fetch(`${import.meta.env.VITE_APP_BASE_API}/biz/agent/prompt`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
        Clientid: import.meta.env.VITE_APP_CLIENT_ID,
      },
      body: JSON.stringify({
        userMessage: inputPrompt.value,
      }),
      signal: controller.signal,
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    await coreStreamResponse(response)
  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      console.log('请求被取消')
    } else {
      console.error('优化失败:', error)
      ElMessage.error('优化失败，请重试')
    }
  } finally {
    optimizeLoading.value = false
    controller = null
    currentReader = null
    if (timeoutCheckInterval) {
      clearInterval(timeoutCheckInterval)
      timeoutCheckInterval = null
    }
  }
}

// 取消优化请求
const cancelOptimizeRequest = () => {
  if (controller) {
    controller.abort()
  }
  if (currentReader) {
    currentReader.cancel('用户取消')
  }
  if (timeoutCheckInterval) {
    clearInterval(timeoutCheckInterval)
    timeoutCheckInterval = null
  }
}

// 重试方法
const handleRetry = () => {
  optimizedPrompt.value = ''
  // 重置完成状态
  isOptimizationDone.value = false
  // 重新触发优化
  // resolved 为什么鼠标移到这个方法上，会提示三遍呢
  handleStartOptimize()
}

// 使用优化后的提示词
const handleUseOptimized = () => {
  if (optimizedPrompt.value) {
    emit('useOptimized', optimizedPrompt.value)
    emit('update:visible', false)
  } else {
    ElMessage.warning('暂无优化内容可使用')
  }
}

// 取消优化
const handleCancelOptimize = () => {
  if (optimizeLoading.value) {
    ElMessageBox.confirm('正在优化中，确定要取消吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        // 取消请求
        cancelOptimizeRequest()
        emit('update:visible', false)
      })
      .catch(() => {
        // 用户点击取消按钮，不做任何操作
      })
  } else {
    emit('update:visible', false)
  }
}

// resolved 打开该组件时候，触发handleStartOptimize
watch(
  () => props.visible,
  newVal => {
    if (newVal && props.modelValue) {
      handleStartOptimize()
    }
  },
  { immediate: true },
)

// 在组件卸载时取消请求
watch(
  () => props.visible,
  newVal => {
    if (!newVal && (controller || currentReader)) {
      cancelOptimizeRequest()
    }
  },
)
</script>

<template>
  <el-popover
    :visible="visible"
    placement="bottom-start"
    :width="500"
    trigger="click"
    @hide="$emit('update:visible', false)"
  >
    <template #reference>
      <slot></slot>
    </template>
    <div class="optimize-popover">
      <div class="optimize-title flex justify-between items-center">
        <div class="title">提示词优化</div>
        <el-button
          v-if="optimizedPrompt"
          class="ex-el-button-gray"
          type="primary"
          plain
          @click="handleRetry"
          ><i class="iconfont icon-zhongzhi mr-1 iconfont-c"></i> 重试</el-button
        >
      </div>
      <div class="optimize-content">
        <!-- resolved 不再需要输入框，inputPrompt直接取自父组件的modelValue，打开判断modelValue有值的情况下直接执行优化请求，要求逻辑完善 -->
        <div class="output-area mt-4">
          <!-- resolved optimizedPrompt怎么没有累加显示，现在一直是覆盖显示，不正确 -->
          <MarkdownEditor
            placeholder="正在生成，请稍候..."
            disabled
            :model-value="optimizedPrompt"
            :height="400"
          />
        </div>
      </div>
      <div class="optimize-footer">
        <el-button @click="handleCancelOptimize">取消</el-button>
        <el-button
          v-if="isOptimizationDone && optimizedPrompt"
          type="primary"
          @click="handleUseOptimized"
        >
          使用
        </el-button>
        <el-button v-else type="primary" :loading="optimizeLoading" @click="handleStartOptimize">
          优化中
        </el-button>
      </div>
    </div>
  </el-popover>
</template>

<style lang="less" scoped>
.optimize-popover {
  .optimize-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
    color: #333;
  }

  .optimize-content {
    .area-title {
      font-size: 14px;
      margin-bottom: 8px;
      color: #606266;
    }
    .output-area {
      max-height: 400px;
      overflow-y: auto;
    }
  }

  .optimize-footer {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }
}
</style>
