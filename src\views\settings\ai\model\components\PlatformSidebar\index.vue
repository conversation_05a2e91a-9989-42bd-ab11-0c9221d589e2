<script setup lang="ts">
import { ref, computed } from 'vue'
import { VueDraggable } from 'vue-draggable-plus'
import PlatformItem from './PlatformItem.vue'
import { Platform } from '@/api/model-base/types'
import { deleteAiPlatform, batchUpdateAiPlatformSort } from '@/api/model-base'
import PlatformLogo from '../PlatformLogo.vue'
// @ts-ignore
import cloneDeep from 'lodash.clonedeep'

defineOptions({ name: 'PlatformSidebar' })
const emits = defineEmits(['open', 'updateInfo', 'editPlatform'])
const props = defineProps<{
  platforms: Platform[]
  loading: boolean
}>()

const currentPlatformId = defineModel<string>()
// 点击了操作按钮的 id
const operationId = ref<string | null>(null)
/**
 * 编辑平台
 */
function editPlatform(platform: Platform) {
  emits('editPlatform', platform)
}

/**
 * 删除平台
 */
async function deletePlatform(id: string) {
  try {
    await ElMessageBox.confirm('确定要删除该平台吗？', '删除平台', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    if (!id) {
      ElMessage.error('平台ID不能为空')
      return
    }

    const res = await deleteAiPlatform([id])
    if (res.code === 200) {
      ElMessage.success('删除成功')
      emits('updateInfo')
    } else {
      ElMessage.error(res.msg || '删除失败')
    }
  } catch (error) {
    if (error instanceof Error) {
      console.error('删除平台失败:', error)
      ElMessage.error(error.message || '删除失败')
    }
  }
}

/**
 * 搜索功能组合函数
 * @returns 包含搜索关键词和过滤后平台数据的响应式对象
 */
function useSearch() {
  const search = ref<string>('')

  // 过滤平台数据
  const filteredPlatforms = computed(() => {
    const platforms = props.platforms || []

    const searchTerm = search.value.trim().toLowerCase() // 移除空格并转换为小写

    if (!searchTerm) {
      return platforms // 如果搜索词为空，返回所有平台
    }
    return platforms.filter(p => p.platformName.toLowerCase().includes(searchTerm))
  })

  return {
    search,
    filteredPlatforms,
  }
}

const { search, filteredPlatforms } = useSearch()

// 分离已启用/未启用平台
const enabledPlatforms = computed(() => filteredPlatforms.value.filter(p => p.isEnabled))
const disabledPlatforms = computed(() => filteredPlatforms.value.filter(p => !p.isEnabled))

// 拖拽逻辑
// 是否进行 拖拽
function useDragSort() {
  const isDragging = ref(false)
  // 排序列表
  const sortList = ref<Platform[]>([])

  // 更新排序的 loading
  const updateLoading = ref(false)

  /**
   * 开始进行排序
   */
  function startSort() {
    isDragging.value = true
    sortList.value = cloneDeep(enabledPlatforms.value)
  }

  /**
   * 更新排序
   */
  function updateSort() {
    const sortInfoList = sortList.value.map((item, index) => ({
      id: item.id,
      sortOrder: index + 1,
    }))
    updateLoading.value = true
    batchUpdateAiPlatformSort({ sortList: sortInfoList })
      .then(res => {
        if (res.code === 200) {
          emits('updateInfo')
          isDragging.value = false
        } else {
          ElMessage.error(res.msg || '更新排序失败')
        }
      })
      .finally(() => {
        updateLoading.value = false
      })
  }

  /**
   * 关闭排序
   */
  function closeSort() {
    if (updateLoading.value) return
    isDragging.value = false
  }

  return {
    isDragging,
    sortList,
    updateLoading,
    startSort,
    closeSort,
    updateSort,
  }
}

const { isDragging, startSort, closeSort, updateLoading, sortList, updateSort } = useDragSort()
</script>

<template>
  <div class="model-platform-list">
    <el-form class="mb-[25px]">
      <el-input v-model="search" placeholder="搜索模型平台" clearable style="width: 100%">
        <template #suffix>
          <el-icon>
            <Search />
          </el-icon>
        </template>
      </el-input>
    </el-form>

    <div class="flex-1 min-h-0 overflow-auto" v-if="isDragging">
      <div>
        <div class="px-2 flex items-center justify-between mb-3">
          <span class="text-[#646A73] text-[16px]">已启用 </span>
          <img
            class="size-[24px] cursor-pointer"
            src="@/assets/settings/active-sort.svg"
            @click="closeSort"
            alt="排序"
          />
        </div>
        <VueDraggable ref="el" v-model="sortList" handle=".handle" :disabled="updateLoading">
          <div class="model-platform-item" v-for="platform in sortList" :key="platform.id">
            <div class="flex-1 truncate">
              <PlatformLogo
                :platformName="platform.platformName"
                :logoSrc="platform.icon"
                :size="40"
              />
              <span class="ml-2">{{ platform.platformName }}</span>
            </div>
            <img class="handle cursor-pointer" src="@/assets/settings/drag.svg" alt="" />
          </div>
        </VueDraggable>
      </div>
    </div>

    <div class="flex-1 min-h-0 overflow-auto" v-else>
      <!-- 搜索结果为空提示 -->
      <div v-if="search && filteredPlatforms.length === 0" class="empty-state text-center py-10">
        <el-empty description="未找到匹配的平台" :image-size="100" />
      </div>

      <!-- 平台列表 -->
      <div v-loading="loading" v-else>
        <!-- 已启用平台 -->
        <div v-if="enabledPlatforms.length > 0" class="mb-[25px]">
          <div class="px-2 flex items-center justify-between mb-3">
            <span class="text-[#646A73] text-[16px]">已启用 </span>
            <img
              class="size-[24px] cursor-pointer"
              src="@/assets/settings/sort.svg"
              alt="排序"
              @click="startSort"
            />
          </div>
          <div>
            <PlatformItem
              v-for="platform in enabledPlatforms"
              :key="platform.id"
              :platform="platform"
              :isActive="platform.id === currentPlatformId"
              :isSelected="operationId === platform.id"
              @visible-change="operationId = $event ? platform.id : null"
              @click="currentPlatformId = platform.id"
              @edit="editPlatform"
              @delete="deletePlatform"
            />
          </div>
        </div>

        <!-- 未启用平台 -->
        <div>
          <div class="px-2 flex items-center justify-between mb-3">
            <span class="text-[#646A73] text-[16px]">未启用 </span>
          </div>
          <div>
            <PlatformItem
              v-for="platform in disabledPlatforms"
              :key="platform.id"
              :platform="platform"
              :isActive="platform.id === currentPlatformId"
              :isSelected="operationId === platform.id"
              @visible-change="operationId = $event ? platform.id : null"
              @click="currentPlatformId = platform.id"
              @edit="editPlatform"
              @delete="deletePlatform"
            />
            <el-empty v-if="disabledPlatforms.length === 0" description="暂无数据" />
          </div>
        </div>
      </div>
    </div>

    <div v-if="isDragging">
      <el-button class="w-full" type="primary" :loading="updateLoading" @click="updateSort">
        <span class="text-[16px] g-family-medium"> 更新排序 </span>
      </el-button>
    </div>
    <div v-else>
      <el-button class="ex-el-button-gray w-full" type="primary" plain @click="emits('open')">
        <i class="iconfont iconfont-c icon-xinzeng mr-2"></i>
        <span class="text-[16px] g-family-medium"> 添加平台 </span>
      </el-button>
    </div>
  </div>
</template>

<style scoped lang="less">
.model-platform-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 24px 16px;
  border-right: 1px solid #dadde8;
}

.model-platform-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  padding-right: 16px;
  // cursor: pointer;
  border-radius: 8px;

  .normal-status {
    display: inline-block;
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 0.25rem;
    background-color: #1ebf60;
  }

  &:hover,
  &.active {
    background-color: #e6eaf4;
  }

  &:hover {
    .operate {
      display: block;
    }

    .normal-status {
      display: none;
    }
  }

  &.selected {
    .operate {
      display: block;
    }
    .normal-status {
      display: none;
    }
  }
}
</style>
