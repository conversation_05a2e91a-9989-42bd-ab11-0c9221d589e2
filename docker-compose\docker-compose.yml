version: '3'

services:
  app:
    container_name: chatgpt-web
    image: chenzhaoyu94/chatgpt-web # Always use latest, just pull the tag image again when updating
    ports:
      - 3002:3002
    environment:
      # pick one of two
      OPENAI_API_KEY:
      # pick one of two
      OPENAI_ACCESS_TOKEN:
      # API interface address, optional, available when OPENAI_API_KEY is set
      OPENAI_API_BASE_URL:
      # API model, optional, available when OPENAI_API_KEY is set
      OPENAI_API_MODEL:
      # reverse proxy, optional
      API_REVERSE_PROXY:
      # Access permission key, optional
      AUTH_SECRET_KEY:
      # The maximum number of requests per hour, optional, default unlimited
      MAX_REQUEST_PER_HOUR: 0
      # timeout in milliseconds, optional
      TIMEOUT_MS: 60000
      # Socks proxy, optional, works with SOCKS_PROXY_PORT
      SOCKS_PROXY_HOST:
      # Socks proxy port, optional, effective when combined with SOCKS_PROXY_HOST
      SOCKS_PROXY_PORT:
      # Socks proxy username, optional, effective when combined with SOCKS_PROXY_HOST & SOCKS_PROXY_PORT
      SOCKS_PROXY_USERNAME:
      # Socks proxy password, optional, effective when combined with SOCKS_PROXY_HOST & SOCKS_PROXY_PORT
      SOCKS_PROXY_PASSWORD:
      # HTTPS_PROXY proxy, optional
      HTTPS_PROXY:
  nginx:
    container_name: nginx
    image: nginx:alpine
    ports:
      - '80:80'
    expose:
      - '80'
    volumes:
      - ./nginx/html:/usr/share/nginx/html
      - ./nginx/nginx.conf:/etc/nginx/conf.d/default.conf
    links:
      - app
