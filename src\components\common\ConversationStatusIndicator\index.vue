<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { streamResponseManager } from '@/utils/streamResponseManager'
import { useChatStore } from '@/store'
import { SvgIcon } from '@/components/common'

const router = useRouter()
const chatStore = useChatStore()

// 正在加载的会话列表
const loadingConversations = ref<string[]>([])

// 计算是否应该显示指示器
const shouldShowIndicator = computed(() => {
  // 没有正在进行的会话时，不显示
  if (loadingConversations.value.length === 0) {
    return false
  }

  // 如果只有一个会话在进行，且当前就在这个会话中，则不显示
  if (
    loadingConversations.value.length === 1 &&
    loadingConversations.value[0] === chatStore.active
  ) {
    return false
  }

  // 其他情况都显示：多个会话在进行，或者当前不在唯一的进行中会话
  return true
})

// 定时更新加载状态
let updateTimer: NodeJS.Timeout | null = null

// 拖拽相关状态
const isDragging = ref(false)
const dragOffset = ref({ x: 0, y: 0 })
const position = ref({ x: 0, y: 0 }) // 初始位置将在mounted时设置为右下角
const containerRef = ref<HTMLElement>()
const hasSetDefaultPosition = ref(false) // 标记是否已设置默认位置

// 获取会话标题
function getConversationTitle(conversationId: string): string {
  const history = chatStore.history.find(h => h.conversationId === conversationId)
  return history?.title || '新对话'
}

// 导航到指定会话
// resolved src\views\chat\layout\LayoutSider\List.vue中对应会话高亮的逻辑是不是漏了
function navigateToConversation(conversationId: string) {
  // 先设置活跃会话状态，确保左侧列表正确高亮
  chatStore.setActive(conversationId)
}

// 更新加载状态
function updateLoadingStatus() {
  loadingConversations.value = streamResponseManager.getLoadingConversations()
}

// 拖拽开始
function handleDragStart(event: MouseEvent) {
  if (!containerRef.value) return

  isDragging.value = true
  const rect = containerRef.value.getBoundingClientRect()
  dragOffset.value = {
    x: event.clientX - rect.left,
    y: event.clientY - rect.top,
  }

  // 防止文本选择
  document.body.style.userSelect = 'none'
  document.body.style.cursor = 'move'

  // 添加全局事件监听
  document.addEventListener('mousemove', handleDragMove)
  document.addEventListener('mouseup', handleDragEnd)
}

// 拖拽移动
function handleDragMove(event: MouseEvent) {
  if (!isDragging.value || !containerRef.value) return

  event.preventDefault()

  // 计算新位置
  const newX = event.clientX - dragOffset.value.x
  const newY = event.clientY - dragOffset.value.y

  // 获取窗口尺寸和组件尺寸
  const windowWidth = window.innerWidth
  const windowHeight = window.innerHeight
  const containerRect = containerRef.value.getBoundingClientRect()

  // 限制在窗口范围内
  const minX = 0
  const maxX = windowWidth - containerRect.width
  const minY = 0
  const maxY = windowHeight - containerRect.height

  position.value = {
    x: Math.max(minX, Math.min(maxX, newX)),
    y: Math.max(minY, Math.min(maxY, newY)),
  }

  // 保存位置到localStorage
  localStorage.setItem('conversationIndicatorPosition', JSON.stringify(position.value))
}

// 拖拽结束
function handleDragEnd() {
  isDragging.value = false

  // 恢复样式
  document.body.style.userSelect = ''
  document.body.style.cursor = ''

  // 移除全局事件监听
  document.removeEventListener('mousemove', handleDragMove)
  document.removeEventListener('mouseup', handleDragEnd)
}

// 计算右下角位置
function setDefaultPosition() {
  if (!containerRef.value || hasSetDefaultPosition.value) return

  // 等待DOM渲染完成
  setTimeout(() => {
    if (!containerRef.value || hasSetDefaultPosition.value) return

    const windowWidth = window.innerWidth
    const windowHeight = window.innerHeight

    // 计算组件的最大高度（header + 两个会话项）
    const headerHeight = 45 // header高度约45px
    const conversationItemHeight = 60 // 每个会话项高度约60px
    const maxDisplayHeight = headerHeight + conversationItemHeight * 2 + 2 // +2为边框

    // 计算最小底部距离，确保能完整显示两个会话
    const minBottomMargin = Math.max(20, maxDisplayHeight + 20)

    // 获取当前组件实际高度
    const containerRect = containerRef.value.getBoundingClientRect()
    const actualHeight = Math.min(containerRect.height, maxDisplayHeight)

    // 设置到右下角，确保距离底部足够距离
    position.value = {
      x: windowWidth - containerRect.width - 20,
      y: windowHeight - actualHeight - minBottomMargin,
    }

    hasSetDefaultPosition.value = true

    // 保存默认位置到localStorage
    localStorage.setItem('conversationIndicatorPosition', JSON.stringify(position.value))
  }, 200) // 增加延迟确保组件完全渲染
}

// 从localStorage恢复位置
function restorePosition() {
  const savedPosition = localStorage.getItem('conversationIndicatorPosition')
  if (savedPosition) {
    try {
      const savedPos = JSON.parse(savedPosition)
      position.value = savedPos
      hasSetDefaultPosition.value = true
    } catch (e) {
      console.warn('恢复对话指示器位置失败:', e)
      // 如果恢复失败，设置默认位置
      setDefaultPosition()
    }
  } else {
    // 如果没有保存的位置，设置默认位置到右下角
    setDefaultPosition()
  }
}

// 计算组件样式
const containerStyle = computed(() => ({
  position: 'fixed' as const,
  left: `${position.value.x}px`,
  top: `${position.value.y}px`,
  cursor: isDragging.value ? 'move' : 'default',
  zIndex: isDragging.value ? 1001 : 1000,
  // 在位置未设置前隐藏组件，避免在(0,0)位置闪现
  opacity: hasSetDefaultPosition.value ? 1 : 0,
  transition: hasSetDefaultPosition.value ? 'opacity 0.2s ease-in' : 'none',
}))

// 监听组件显示状态，在首次显示时设置位置
watch(
  () => shouldShowIndicator.value,
  (shouldShow, wasShowing) => {
    // 当从不显示变为显示时，设置默认位置
    if (!wasShowing && shouldShow && !hasSetDefaultPosition.value) {
      setTimeout(() => {
        restorePosition()
      }, 50)
    }
  },
)

// 监听活跃会话变化，重新计算显示状态
watch(
  () => chatStore.active,
  () => {
    // 当活跃会话变化时，重新计算是否显示指示器
    // shouldShowIndicator 是计算属性，会自动响应变化
  },
)

onMounted(() => {
  // 立即更新一次
  updateLoadingStatus()

  // 每秒更新一次状态
  updateTimer = setInterval(updateLoadingStatus, 1000)

  // 如果应该显示指示器，立即设置位置
  if (shouldShowIndicator.value) {
    restorePosition()
  }
})

onUnmounted(() => {
  if (updateTimer) {
    clearInterval(updateTimer)
    updateTimer = null
  }

  // 清理拖拽事件监听
  document.removeEventListener('mousemove', handleDragMove)
  document.removeEventListener('mouseup', handleDragEnd)
})
</script>

<template>
  <!-- resolved 让这个组件可以被鼠标拖动位置 -->
  <div
    v-if="shouldShowIndicator && false"
    ref="containerRef"
    class="conversation-status-indicator"
    :style="containerStyle"
  >
    <div class="indicator-header" @mousedown="handleDragStart">
      <SvgIcon icon="ri:chat-1-line" class="icon" />
      <span class="title">正在进行的对话</span>
      <span class="count">({{ loadingConversations.length }})</span>
      <div class="drag-hint">
        <SvgIcon icon="ri:drag-move-2-line" class="drag-icon" />
      </div>
    </div>
    <div class="conversation-list">
      <div
        v-for="conversationId in loadingConversations"
        :key="conversationId"
        class="conversation-item"
        @click="navigateToConversation(conversationId)"
      >
        <div class="conversation-info">
          <div class="conversation-title">{{ getConversationTitle(conversationId) }}</div>
          <div class="conversation-id">ID: {{ conversationId.slice(-8) }}</div>
        </div>
        <div class="loading-indicator">
          <div class="loading-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.conversation-status-indicator {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e6e8eb;
  max-width: 300px;
  font-size: 14px;
  transition: box-shadow 0.2s ease;

  &:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
  }

  .indicator-header {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #e6e8eb;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
    cursor: move;
    user-select: none;
    position: relative;

    &:hover {
      background: #f1f3f4;
    }

    .icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
      color: #4c5cec;
    }

    .title {
      font-weight: 500;
      color: #1f2328;
      flex: 1;
    }

    .count {
      margin-left: 4px;
      color: #656d76;
      font-size: 12px;
    }

    .drag-hint {
      margin-left: 8px;
      opacity: 0.6;
      transition: opacity 0.2s;

      .drag-icon {
        width: 14px;
        height: 14px;
        color: #8b949e;
      }
    }

    &:hover .drag-hint {
      opacity: 1;
    }
  }

  .conversation-list {
    max-height: 120px; // 刚好显示两个会话项（每个60px）
    overflow-y: auto;
    overflow-x: hidden;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 5px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: #dadde8;
      border-radius: 2px;

      &:hover {
        background: #b0b7c3;
      }
    }
  }

  .conversation-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-bottom: 1px solid #f1f3f4;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: #f8f9fa;
    }

    &:last-child {
      border-bottom: none;
    }

    .conversation-info {
      flex: 1;
      min-width: 0;

      .conversation-title {
        font-weight: 500;
        color: #1f2328;
        margin-bottom: 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .conversation-id {
        font-size: 12px;
        color: #656d76;
      }
    }

    .loading-indicator {
      margin-left: 12px;

      .loading-dots {
        display: flex;
        align-items: center;
        gap: 4px;

        span {
          width: 6px;
          height: 6px;
          background-color: #4c5cec;
          border-radius: 50%;
          animation: loading-pulse 1.4s infinite ease-in-out;

          &:nth-child(1) {
            animation-delay: -0.32s;
          }

          &:nth-child(2) {
            animation-delay: -0.16s;
          }

          &:nth-child(3) {
            animation-delay: 0s;
          }
        }
      }
    }
  }
}

@keyframes loading-pulse {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

// 深色模式支持
@media (prefers-color-scheme: dark) {
  .conversation-status-indicator {
    background: #1c1e21;
    border-color: #30363d;

    .indicator-header {
      background: #21262d;
      border-bottom-color: #30363d;

      &:hover {
        background: #30363d;
      }

      .title {
        color: #f0f6fc;
      }

      .count {
        color: #8b949e;
      }

      .drag-hint .drag-icon {
        color: #8b949e;
      }
    }

    .conversation-list {
      // 深色模式滚动条样式
      &::-webkit-scrollbar-thumb {
        background: #484f58;

        &:hover {
          background: #656d76;
        }
      }
    }

    .conversation-item {
      border-bottom-color: #30363d;

      &:hover {
        background-color: #21262d;
      }

      .conversation-info {
        .conversation-title {
          color: #f0f6fc;
        }

        .conversation-id {
          color: #8b949e;
        }
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .conversation-status-indicator {
    max-width: calc(100vw - 20px);

    .indicator-header {
      padding: 10px 12px;

      .drag-hint {
        display: none; // 移动端隐藏拖拽提示
      }
    }

    .conversation-list {
      max-height: 100px; // 移动端稍微减小高度

      &::-webkit-scrollbar {
        width: 3px; // 移动端滚动条更细
      }
    }

    .conversation-item {
      padding: 10px 12px;
    }
  }
}
</style>
