<script setup lang="ts">
import { NMenu } from 'naive-ui'
import { useRoute, useRouter } from 'vue-router'
import { computed, h, onMounted, provide, ref, watch } from 'vue'
import { SvgIcon } from '@/components/common'
import { useChatStore, useUserStore } from '@/store'
import { t } from '@/locales'
import homeSvg from '@/assets/settings/home.svg'
import user from '@/assets/settings/user.svg'
import releaseSvg from '@/assets/settings/release.svg'
import ai from '@/assets/settings/ai.svg'
import { useKnowledgeBaseStore } from '@/store/modules/knowledge-base'
import ms from '@/utils/message'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const chatStore = useChatStore()
const collapsed = ref(false)

/**
 * 是否有 对外发布的权限
 */
const hasReleasePermission = computed(() => {
  return (
    userStore.roles.includes('superadmin') ||
    userStore.roles.includes('admin') ||
    userStore.permissions.includes('settings:module:all') ||
    userStore.permissions.includes('settings:outward:all')
  )
})

// 提供一个响应式引用来存储header-actions的内容
const headerActions = ref(null)
provide('headerActions', headerActions)

// 监听路由变化，在特定路径下自动收起侧边栏
watch(
  () => route.path,
  newPath => {
    if (newPath.includes('agent-operation')) {
      collapsed.value = true
    } else {
      collapsed.value = false
    }
  },
  { immediate: true },
)

// 初始化时检查当前路径
onMounted(() => {
  if (route.path.includes('agent-operation')) {
    collapsed.value = true
  }
})

const defaultExpandedKeys = ['ai']
const activeKey = ref(route.path.replace('/settings/', ''))

// 计算面包屑路径
// const breadcrumbs = computed(() => {
//   const paths = route.path.split('/').filter(Boolean)
//   const result: string[] = []

//   // 从路由匹配记录中获取每一级的标题
//   route.matched.forEach((record, index) => {
//     if (index > 0) {
//       // 跳过第一个（根路由）
//       result.push(record.meta.title as string)
//     }
//   })

//   return result
// })
const breadcrumbList = ref<string[]>([])
const knowledgeStore = useKnowledgeBaseStore()
// 计算面包屑路径
const generateBreadcrumb = () => {
  const matched = route.matched.filter(item => !item.meta?.hideBreadcrumb)

  breadcrumbList.value = matched
    .map(item => {
      // 处理动态标题的路由
      if (item.meta?.dynamicTitle) {
        return {
          title: knowledgeStore.currentKnowledgeTitle || '',
          path: item.path,
        }
      }
      return {
        title: (item.meta?.title || '') as string,
        path: item.path,
      }
    })
    .filter(item => item.title)
    .map(item => item.title)
}
watch(() => route.path, generateBreadcrumb, { immediate: true })
watch(() => knowledgeStore.currentKnowledgeTitle, generateBreadcrumb)

// 监听路由变化更新菜单激活状态
watch(
  () => route.path,
  newPath => {
    if (newPath.includes('settings-knowledge')) {
      // todo： 这里逻辑应该可以去掉了，现在设置中给已经没有 知识库相关的菜单了
      activeKey.value = 'ai/knowledge'
    } else {
      activeKey.value = newPath.replace('/settings/', '')
    }
  },
  {
    immediate: true,
  },
)

const menuOptions = [
  {
    label: '首页',
    key: 'settings-home',
    icon: renderImg(homeSvg),
  },
  {
    label: '部门成员',
    key: 'user',
    icon: renderImg(user),
  },
  ...(hasReleasePermission.value
    ? [
        {
          label: '对外发布',
          key: 'external-release',
          icon: renderImg(releaseSvg),
        },
      ]
    : []),
  {
    label: 'AI管理',
    key: 'ai',
    icon: renderImg(ai),
    children: [
      {
        label: '模型库',
        key: 'ai/model',
        // icon: renderIcon('ri:database-2-line'),
      },
      // {
      //   label: '知识库',
      //   key: 'ai/knowledge',
      //   // icon: renderIcon('ri:book-2-line'),
      // },
      // {
      //   label: '提示库',
      //   key: 'ai/prompt',
      //   // icon: renderIcon('ri:message-3-line'),
      // },
      {
        label: '助手库',
        key: 'ai/agent',
        // icon: renderIcon('ri:user-voice-line'),
      },
    ],
  },
]

function renderImg(url: string) {
  return () =>
    h(
      'div',
      {
        style: {
          display: 'inline-flex',
          alignItems: 'center',
          marginRight: '16px',
          width: '32px',
          height: '32px',
          flexShrink: '0',
        },
      },
      [
        h('img', {
          src: url,
          style: {
            width: '32px',
            height: '32px',
            borderRadius: '50%',
            flexShrink: '0',
          },
        }),
      ],
    )
}

function renderIcon(icon: string) {
  return () =>
    h(
      'div',
      {
        style: {
          display: 'inline-flex',
          alignItems: 'center',
          marginRight: '16px',
        },
      },
      [h(SvgIcon, { icon })],
    )
}

function handleUpdateValue(key: string) {
  // 检查路由是否存在
  const route = router.resolve(`/settings/${key}`)
  if (route.matched.length === 0) {
    ms.warning('该功能暂未开放')
    return
  }
  router.push(`/settings/${key}`)
}

function handleClearHistory() {
  chatStore.clearHistory()
  ms.success(t('common.success'))
}

function handleLogout() {
  userStore.logout()
  ms.success(t('common.success'))
}

// 添加面包屑配置
const breadcrumbMap: Record<string, string> = {
  user: '部门成员',
  'ai/model': '模型库',
  'ai/knowledge': '知识库',
  'ai/prompt': '提示库',
  'ai/agent': '助手库',
} as const
</script>

<template>
  <div class="settings-container">
    <div class="settings-sider-wrapper" :class="{ collapsed }">
      <div class="settings-sider h-full ex-n-menu-box">
        <NMenu
          :options="menuOptions"
          :value="activeKey"
          :default-value="activeKey"
          :default-expanded-keys="defaultExpandedKeys"
          @update:value="handleUpdateValue"
        />
      </div>
    </div>
    <div
      class="settings-content"
      :class="{ 'full-width': collapsed, 'settings-content-collapsed': collapsed }"
    >
      <div class="my-breadcrumb">
        <div class="flex justify-between items-center">
          <!-- resolved 使用路由表中的meta.title -->
          <!-- <div>{{ breadcrumbs.join(' / ') }}</div> -->
          <div>{{ breadcrumbList.join(' / ') }}</div>
          <component :is="headerActions" v-if="headerActions" />
        </div>
      </div>
      <div class="settings-content-body">
        <router-view />
      </div>
    </div>
  </div>
</template>

<style scoped>
.settings-container {
  height: 100%;
  display: flex;
  overflow-x: hidden;
}

.settings-sider-wrapper {
  width: 240px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.settings-sider-wrapper.collapsed {
  width: 0;
}

.settings-sider {
  width: 240px;
  background: #fff;
  padding: 8px;
  flex-shrink: 0;
}

.settings-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-left: 1px solid #e5e7eb;
  background: #f7f8fa;
  transition: all 0.3s ease;
  margin-left: 0;
  min-width: 0;
  width: 0;
}

.settings-content.settings-content-collapsed {
  border-left: none;
}

.settings-content-body {
  flex: 1;
  overflow: auto;
  /* min-width: 0; */
}
</style>
