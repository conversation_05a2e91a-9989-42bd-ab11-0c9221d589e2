<script setup lang="ts">
import { nextTick, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import AgentKnowledgeBaseSettingsPopover from './AgentKnowledgeBaseSettingsPopover.vue'
import KnowledgeBaseSelector from '@/views/chat/components/SettingSider/KnowledgeBaseSelector.vue'
// resolved 改造KnowledgeBaseSettingsPopover组件，兼容本业务的逻辑，本业务下该组件不需要调用接口，数据传递给src\views\agent\my-agent\operation.vue去提交
// resolved 创建专门用于Agent的知识库设置组件

// 支持v-model绑定
const props = defineProps<{
  modelValue: any[]
  disabled?: boolean
  // 新增知识库设置的props
  knowledgeBaseSettings?: {
    maxResults: number
    minScore: number
    rerank: boolean
    rerankModelId?: number
  }
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: any[]): void
  // 新增知识库设置的emit
  (
    e: 'update:knowledgeBaseSettings',
    value: {
      maxResults: number
      minScore: number
      rerank: boolean
      rerankModelId?: number
    },
  ): void
}>()

// 知识库数据 - 使用props作为初始值
const kbList = ref([...props.modelValue])

// 知识库设置数据
const kbSettings = ref({
  maxResults: 3,
  minScore: 0.5,
  rerank: false,
  rerankModelId: undefined as number | undefined,
  ...props.knowledgeBaseSettings,
})

// 当kbList变化时，通知父组件
const updateParent = (newList: any[]) => {
  nextTick(() => {
    emit('update:modelValue', newList)
  })
}

// 当知识库设置变化时，通知父组件
const updateKnowledgeBaseSettings = (settings: any) => {
  kbSettings.value = { ...settings }
  emit('update:knowledgeBaseSettings', settings)
}

// 监听props变化，同步kbList
watch(
  () => props.modelValue,
  newVal => {
    kbList.value = [...newVal]
  },
  { immediate: true },
)

// 监听知识库设置props变化
watch(
  () => props.knowledgeBaseSettings,
  newVal => {
    if (newVal) {
      kbSettings.value = { ...kbSettings.value, ...newVal }
    }
  },
  { immediate: true, deep: true },
)

// 知识库选择器相关
const showKnowledgeBaseSelector = ref(false)
// 知识库设置相关
const showKnowledgeBaseSettings = ref(false)

const openKnowledgeBaseSelector = () => {
  if (props.disabled) return
  showKnowledgeBaseSelector.value = true
}

function handleKnowledgeBaseSelected(kb: any) {
  if (kb.action === 'remove') {
    const index = kbList.value.findIndex(item => item.id?.toString() === kb.id?.toString())
    if (index !== -1) {
      removeKnowledgeBase(index)
    }
    return
  }
  console.log('选择了知识库:', kb)
  // 添加知识库到列表
  const newList = [...kbList.value, kb]
  kbList.value = newList
  updateParent(newList)
  ElMessage.success('知识库添加成功')
}

function removeKnowledgeBase(index: number) {
  if (props.disabled) return
  const newList = [...kbList.value]
  newList.splice(index, 1)
  kbList.value = newList
  updateParent(newList)
  ElMessage.success('知识库移除成功')
}
</script>

<template>
  <div class="tab-header">
    <div class="title">知识库</div>
    <div v-if="!disabled" class="flex items-center space-x-2">
      <!-- resolved 创建一个弹框组件el-popover，点击弹出 -->
      <!-- resolved 改造KnowledgeBaseSettingsPopover组件，兼容本业务的逻辑，本业务下该组件不需要调用接口，数据传递给src\views\agent\my-agent\operation.vue去提交 -->
      <AgentKnowledgeBaseSettingsPopover
        v-model:visible="showKnowledgeBaseSettings"
        :settings="kbSettings"
        @update:settings="updateKnowledgeBaseSettings"
      >
        <i
          style="font-size: 1.05rem"
          class="iconfont iconfont-hover-c icon-zhishikushezhi text-lg cursor-pointer hover:text-primary"
          @click="showKnowledgeBaseSettings = true"
        />
      </AgentKnowledgeBaseSettingsPopover>
      <i
        style="font-size: 1.125rem; margin-top: -2px"
        class="iconfont iconfont-hover-c icon-xinzengzhishiku text-lg cursor-pointer hover:text-primary"
        @click="openKnowledgeBaseSelector"
      />
    </div>
  </div>
  <div class="tab-content">
    <!-- resolved 参考src\views\chat\components\SettingSider\index.vue的知识库部分，实现知识库设置 -->
    <div class="space-y-4">
      <div
        v-if="kbList && kbList.length"
        class="space-y-2 max-h-[calc(100vh-450px)] overflow-y-auto"
      >
        <div
          v-for="(kb, index) in kbList"
          :key="kb.id"
          class="kb-item flex items-center justify-between p-2 rounded-lg border cursor-pointer g-bg-hover"
        >
          <div class="flex items-center overflow-hidden">
            <img
              v-if="kb.enabled"
              class="text-lg mr-2 flex-shrink-0 w-6 h-6"
              src="@/assets/chat/knowledge-base-icon-0.svg"
              alt="知识库图标"
            />
            <el-tooltip v-else content="未启用" placement="top">
              <div class="relative">
                <img
                  class="text-lg mr-2 flex-shrink-0 w-6 h-6"
                  src="@/assets/chat/knowledge-base-icon-0.svg"
                  alt="知识库图标"
                />
                <img
                  class="absolute top-0 right-2 w-3 h-3"
                  src="@/assets/chat/error.svg"
                  alt="错误图标"
                />
              </div>
            </el-tooltip>
            <span class="text-[14px] truncate">{{ kb.name }}</span>
          </div>
          <i
            v-if="!disabled"
            style="font-size: 1.5rem"
            class="iconfont icon-shanchu text-lg cursor-pointer hover:text-red-500 flex-shrink-0 delete-icon"
            @click.stop="removeKnowledgeBase(index)"
          />
        </div>
      </div>
      <el-empty v-else description="暂无数据" />
    </div>

    <!-- 知识库选择弹窗 -->
    <KnowledgeBaseSelector
      v-model:visible="showKnowledgeBaseSelector"
      :initial-kb-list="kbList"
      @selected="handleKnowledgeBaseSelected"
    />
  </div>
</template>

<style lang="less" scoped>
// .tab-header {
//   display: flex;
//   align-items: center;
//   justify-content: space-between;
//   padding: 16px;
//   border-bottom: 1px solid #e5e7eb;

//   .title {
//     font-size: 14px;
//     font-weight: bold;
//   }
// }

// .tab-content {
//   padding: 16px;
//   flex: 1;
//   overflow: auto;
// }

.kb-item {
  &:hover .delete-icon {
    opacity: 1;
  }

  .delete-icon {
    opacity: 0;
    transition: opacity 0.3s;
  }
}

.text-primary {
  color: #4b5563;
}

.dark .text-primary {
  color: #9ca3af;
}
</style>
