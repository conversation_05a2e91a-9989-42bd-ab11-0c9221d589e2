<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { Close } from '@element-plus/icons-vue'
import { ElMessageBox } from 'element-plus'
import { VERSION_STATUS, type VersionStatusCode } from '@/utils/constants'
import { getBizAgentAgentEventRecordListId } from '@/api/agent'
import { formatDate } from '@/utils/date'

const props = defineProps<{
  visible: boolean
  agentId?: string
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'restore', versionId: string | number): void
}>()

const selectedVersion = ref<string | number>('')
const versionList = ref<Agent.AgentEventRecordVo[]>([])
const loading = ref(false)

// 获取版本历史记录
const fetchVersionHistory = async () => {
  // console.log('开始执行fetchVersionHistory', props.agentId, props.visible)
  if (!props.agentId) {
    // console.log('没有agentId，无法获取版本历史')
    return
  }

  loading.value = true
  try {
    // console.log('调用getBizAgentAgentEventRecordListId', props.agentId)
    const res = await getBizAgentAgentEventRecordListId<API.Response<Agent.AgentEventRecordVo[]>>({
      id: props.agentId,
    })
    // console.log('API返回结果:', JSON.stringify(res))

    if (res.code === 200 && res.data) {
      versionList.value = res.data
      // console.log('设置versionList:', JSON.stringify(versionList.value))
    } else {
      // console.log('API返回结果不符合预期, code:', res.code, '应该是200')
    }
  } catch (error) {
    // console.error('获取版本历史失败', error)
  } finally {
    loading.value = false
  }
}

const handleRestore = () => {
  if (selectedVersion.value) {
    ElMessageBox.confirm('如果将助手恢复到该版本，助手的最新配置将被覆盖。', '确认还原', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        // todo restore的逻辑线应该没走完
        emit('restore', selectedVersion.value)
        emit('update:visible', false)
      })
      .catch(() => {
        // 用户取消操作，不做处理
      })
  }
}

// 检查版本是否可以还原
const isVersionRestorable = (status?: number): boolean => {
  // 可还原的状态: 已发布(1)、私人助手(-1)
  return status === 1 || status === -1
}

// 格式化日期
const formatVersionTime = (date?: Date) => {
  if (!date) return ''
  return formatDate(new Date(date), 'MM-DD HH:mm')
}

// 获取状态码转换为string类型
const getVersionStatus = (status?: number): string => {
  const statusMap: Record<number, string> = {
    [-1]: 'private',
    0: 'pending',
    1: 'success',
    2: 'failed',
  }

  return status !== undefined ? statusMap[status] || 'pending' : 'pending'
}

// 当组件挂载时，如果visible为true且有agentId，则获取数据
onMounted(() => {
  // console.log('组件挂载, visible:', props.visible, 'agentId:', props.agentId)
  if (props.visible && props.agentId) {
    fetchVersionHistory()
  }
})

// 当visible或agentId变化时，重新获取数据
watch(
  () => [props.visible, props.agentId],
  ([newVisible, newAgentId], oldValues) => {
    // console.log('watch触发', '新visible:', newVisible, '新agentId:', newAgentId, '旧值:', oldValues)
    if (newVisible && newAgentId) {
      fetchVersionHistory()
    }
  },
  { immediate: true },
)
</script>

<template>
  <el-popover
    :visible="visible"
    trigger="click"
    placement="bottom-start"
    :width="360"
    popper-class="history-popover-popper"
    @hide="$emit('update:visible', false)"
  >
    <template #reference>
      <slot></slot>
    </template>

    <div class="history-popover">
      <div class="history-header">
        <span class="title">版本历史</span>
        <div class="header-right">
          <el-icon class="close-icon" @click="$emit('update:visible', false)">
            <Close />
          </el-icon>
        </div>
      </div>

      <!-- 历史版本列表 -->
      <div class="version-list">
        <el-empty v-if="versionList.length === 0 && !loading" description="暂无版本历史"></el-empty>
        <el-skeleton v-else-if="loading" :rows="3" animated />
        <div
          v-for="(version, index) in versionList"
          v-else
          :key="version.id"
          class="version-item"
          @click="selectedVersion = version.id || ''"
        >
          <div class="version-info">
            <div class="step-line">
              <div class="step-dot" :class="{ current: index === 0 }"></div>
              <div v-if="index !== versionList.length - 1" class="step-connector"></div>
            </div>
            <div
              class="version-content"
              :class="{
                'version-content--active': selectedVersion === version.id,
                'current-status': getVersionStatus(version.status) === 'current',
              }"
            >
              <div class="version-header">
                <div class="version-header-left">
                  <span
                    class="version-tag"
                    :style="{
                      background: VERSION_STATUS.find(
                        (s: any) => s.code === getVersionStatus(version.status),
                      )?.color,
                    }"
                  >
                    {{
                      VERSION_STATUS.find((s: any) => s.code === getVersionStatus(version.status))
                        ?.name
                    }}
                  </span>
                  <el-tooltip
                    v-if="getVersionStatus(version.status) === 'failed'"
                    :content="version.description || '审核未通过'"
                    placement="top"
                  >
                    <img src="@/assets/agent/exclamation.svg" alt="failed" class="failed-icon" />
                  </el-tooltip>
                </div>
                <span v-if="getVersionStatus(version.status) !== 'current'" class="version-time">{{
                  formatVersionTime(version.createTime)
                }}</span>
              </div>
              <span v-if="getVersionStatus(version.status) !== 'current'" class="version-label"
                ><span style="color: #969ba4">版本号：</span>{{ version.versionId }}</span
              >
            </div>
          </div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <!-- <div class="footer">
        <el-button
          class="w-full"
          type="primary"
          :disabled="
            !selectedVersion ||
            !isVersionRestorable(versionList.find(v => v.id === selectedVersion)?.status) ||
            getVersionStatus(versionList.find(v => v.id === selectedVersion)?.status) === 'current'
          "
          size="large"
          @click="handleRestore"
        >
          还原为此版本
        </el-button>
      </div> -->
    </div>
  </el-popover>
</template>

<style scoped lang="less">
.version-tag {
  height: 28px;
  border-radius: 4px;
  padding: 0 10px;
  line-height: 28px;
  font-weight: 500;
  font-size: 12px;
  color: #ffffff;
  font-style: normal;
}

.history-popover {
  padding: 0;
  border-radius: 8px;
  background: #fff;
}

.history-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.history-header .title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.close-icon {
  cursor: pointer;
  font-size: 16px;
  color: #909399;
  transition: color 0.3s;
}

.close-icon:hover {
  color: #606266;
}

.history-header .total {
  font-size: 13px;
  color: #909399;
}

.version-list {
  max-height: 400px;
  overflow-y: auto;
  padding: 8px;
}

.version-list::-webkit-scrollbar {
  width: 5px;
}

.version-list::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 3px;
}

.version-item {
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 2px;
  position: relative;
}

.version-info {
  display: flex;
  gap: 10px;
}

.step-line {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 16px;
}

.step-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #cfd2d7;
  z-index: 1;
  transition: all 0.3s;
}

.step-dot.current {
  background-color: #4865e8;
}

.step-connector {
  position: absolute;
  top: 24px;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: calc(100% + 12px);
  background-color: #cfd2d7;

  .version-item:has(.current-status) + .version-item & {
    height: calc(100% + 4px);
  }
}

.version-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 8px;
  border-radius: 6px;
  min-height: 32px;

  &.current-status {
    min-height: 24px;
    padding: 4px 8px;
  }
}

.version-content:hover {
  background: rgba(230, 234, 244, 0.6);
}

.version-content--active {
  background: rgba(230, 234, 244, 0.6);
}

.version-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.version-label {
  margin-top: 4px;
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.version-time {
  font-size: 13px;
  color: #909399;
  margin-left: auto;
}

.version-desc {
  font-size: 14px;
  color: #303133;
  line-height: 1.4;
}

.footer {
  margin-top: 8px;
  padding: 16px;
  display: flex;
  justify-content: center;
}

:deep(.el-button--large) {
  padding: 12px 24px;
  font-size: 14px;
}

.version-header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.failed-icon {
  width: 16px;
  height: 16px;
  cursor: help;
}
</style>

<style>
.history-popover-popper {
  padding: 0 !important;
  border-radius: 8px !important;
  height: 500px !important;
}

.history-popover-popper .el-popover__default {
  height: 100%;
}

.history-popover {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.version-list {
  flex: 1;
  max-height: unset !important;
  overflow-y: auto;
  padding: 8px;
}
</style>
