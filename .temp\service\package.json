{"name": "chatgpt-web-service", "version": "1.0.0", "private": false, "description": "ChatGPT Web Service", "author": "ChenZhaoYu <<EMAIL>>", "keywords": ["chatgpt-web", "chatgpt", "chatbot", "express"], "engines": {"node": "^16 || ^18 || ^20"}, "scripts": {"start": "esno ./src/index.ts", "dev": "esno watch ./src/index.ts", "prod": "node ./build/index.mjs", "build": "pnpm clean && tsup", "clean": "<PERSON><PERSON><PERSON> build", "lint": "eslint .", "lint:fix": "eslint . --fix", "common:cleanup": "rimraf node_modules && rimraf pnpm-lock.yaml"}, "dependencies": {"axios": "^1.3.4", "chatgpt": "^5.1.2", "dotenv": "^16.0.3", "esno": "^4.7.0", "express": "^4.18.2", "express-rate-limit": "^6.7.0", "https-proxy-agent": "^5.0.1", "isomorphic-fetch": "^3.0.0", "node-fetch": "^3.3.0", "socks-proxy-agent": "^7.0.0"}, "devDependencies": {"@antfu/eslint-config": "^0.35.3", "@types/express": "^4.17.17", "@types/node": "^18.14.6", "eslint": "^8.35.0", "rimraf": "^4.3.0", "tsup": "^6.6.3", "typescript": "^4.9.5"}}