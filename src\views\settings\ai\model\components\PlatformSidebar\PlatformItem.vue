<script setup lang="ts">
import { Platform } from '@/api/model-base/types'
import PlatformLogo from '../PlatformLogo.vue'

const props = defineProps<{
  platform: Platform
  isActive: boolean
  isSelected: boolean
}>()

const emits = defineEmits(['visible-change', 'edit', 'delete'])
const showStatus = computed(() => props.platform.isEnabled)
</script>
<template>
  <div
    class="model-platform-item"
    :class="{
      active: isActive,
      selected: isSelected,
    }"
  >
    <div class="flex-1 truncate">
      <PlatformLogo :logo-src="platform.icon" :platform-name="platform.platformName" :size="40" />
      <span class="ml-2">{{ platform.platformName }}</span>
    </div>
    <el-dropdown trigger="click" @visible-change="emits('visible-change', $event)">
      <div class="operate hidden" @click.stop>
        <el-icon><MoreFilled /></el-icon>
      </div>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item @click="emits('edit', platform)">
            <el-icon><Edit /></el-icon>
            编辑
          </el-dropdown-item>
          <el-dropdown-item v-if="!platform.isDefault" @click="emits('delete', platform.id)">
            <el-icon style="color: #f25b37"><Delete /></el-icon>
            <span style="color: #f25b37">删除</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    <span v-if="showStatus" class="normal-status"></span>
  </div>
</template>

<style lang="less" scoped>
.model-platform-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  padding-right: 16px;
  cursor: pointer;
  border-radius: 8px;

  .normal-status {
    display: inline-block;
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 0.25rem;
    background-color: #1ebf60;
  }

  &:hover,
  &.active {
    background-color: #e6eaf4;
  }

  &:hover {
    .operate {
      display: block;
    }

    .normal-status {
      display: none;
    }
  }

  &.selected {
    .operate {
      display: block;
    }
    .normal-status {
      display: none;
    }
  }
}
</style>
