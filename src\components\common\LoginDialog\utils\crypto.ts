import CryptoJS from 'crypto-js'

/**
 * Base64 加密
 */
export function encryptBase64(data: string) {
  return CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(data))
}

/**
 * Base64 解密
 */
export function decryptBase64(data: string) {
  return CryptoJS.enc.Base64.parse(data).toString(CryptoJS.enc.Utf8)
}

/**
 * AES加密
 */
export function encryptWithAes(data: string, secretKey: string) {
  const result = CryptoJS.AES.encrypt(data, CryptoJS.enc.Utf8.parse(secretKey), {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7,
  })
  return result.toString()
}

/**
 * AES解密
 */
export function decryptWithAes(data: string, secretKey: string) {
  const result = CryptoJS.AES.decrypt(data, CryptoJS.enc.Utf8.parse(secretKey), {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7,
  })
  return CryptoJS.enc.Utf8.stringify(result)
}

/**
 * 生成AES密钥
 */
export function generateAesKey() {
  return CryptoJS.lib.WordArray.random(16).toString()
}
