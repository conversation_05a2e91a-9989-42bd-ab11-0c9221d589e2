/**
 * 日期格式化函数
 * @param date 日期对象
 * @param format 格式化模式 例如：YYYY-MM-DD HH:mm:ss
 * @returns 格式化后的日期字符串
 */
export function formatDate(date: Date, format = 'YYYY-MM-DD'): string {
  if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
    return ''
  }

  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  // 补零函数
  const padZero = (num: number): string => (num < 10 ? `0${num}` : `${num}`)

  return format
    .replace(/YYYY/g, `${year}`)
    .replace(/YY/g, `${year}`.slice(2))
    .replace(/MM/g, padZero(month))
    .replace(/M/g, `${month}`)
    .replace(/DD/g, padZero(day))
    .replace(/D/g, `${day}`)
    .replace(/HH/g, padZero(hour))
    .replace(/H/g, `${hour}`)
    .replace(/mm/g, padZero(minute))
    .replace(/m/g, `${minute}`)
    .replace(/ss/g, padZero(second))
    .replace(/s/g, `${second}`)
}

/**
 * 获取相对时间
 * @param date 日期对象或时间戳
 * @returns 相对现在的时间
 */
export function getRelativeTime(date: Date | number | string): string {
  let targetDate: Date

  if (date instanceof Date) {
    targetDate = date
  } else if (typeof date === 'number') {
    targetDate = new Date(date)
  } else if (typeof date === 'string') {
    // 尝试转换字符串为日期
    const parsedDate = new Date(date)
    if (!isNaN(parsedDate.getTime())) {
      targetDate = parsedDate
    } else {
      return '无效日期'
    }
  } else {
    return '无效日期'
  }

  const now = new Date()
  const diff = now.getTime() - targetDate.getTime()

  // 如果是未来时间
  if (diff < 0) {
    return formatDate(targetDate)
  }

  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour
  const week = 7 * day
  const month = 30 * day
  const year = 365 * day

  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`
  } else if (diff < week) {
    return `${Math.floor(diff / day)}天前`
  } else if (diff < month) {
    return `${Math.floor(diff / week)}周前`
  } else if (diff < year) {
    return `${Math.floor(diff / month)}个月前`
  } else {
    return `${Math.floor(diff / year)}年前`
  }
}
