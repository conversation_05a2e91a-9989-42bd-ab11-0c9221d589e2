<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import ProfileTab from './ProfileTab.vue'
import StatisticsTab from './StatisticsTab.vue'
import SecurityTab from './SecurityTab.vue'
import AboutTab from './AboutTab.vue'
import { useUserStore } from '@/store'

const props = defineProps<{
  show: boolean
}>()

const emit = defineEmits(['update:show'])
const userStore = useUserStore()
// resolved 打开弹窗时候调用  userStore.getInfo()获取最新用户信息

// 菜单选项
// resolved 按menuOptions抽离出四个模块，每个模块一个组件
const menuOptions = [
  {
    label: '个人资料',
    key: 'profile',
    icon: 'icon-gerenziliao',
  },
  {
    label: '数据统计',
    key: 'statistics',
    icon: 'icon-tongji',
  },
  {
    label: '安全',
    key: 'security',
    icon: 'icon-anquan',
  },
  {
    label: '关于',
    key: 'about',
    icon: 'icon-guanyu',
  },
]

const activeKey = ref('profile')

// 处理菜单选择
function handleMenuSelect(key: string) {
  activeKey.value = key
}

// 处理关闭
function handleClose() {
  emit('update:show', false)
}

onMounted(() => {})

watch(
  () => activeKey.value,
  newValue => {
    // 切换标签时可能需要的逻辑
  },
)

// 监听显示状态变化
watch(
  () => props.show,
  async newVal => {
    if (newVal) {
      // 当弹窗打开时获取最新用户信息
      await userStore.getInfo()
    }
  },
)
</script>

<template>
  <!-- resolved 点击遮罩层不要关闭 -->
  <ElDialog
    draggable
    :model-value="show"
    width="900px"
    style="padding: 0"
    :before-close="handleClose"
    :close-on-click-modal="false"
    destroy-on-close
    @update:model-value="emit('update:show', $event)"
  >
    <div class="dialog-container">
      <!-- 左侧菜单 -->
      <div class="left-menu">
        <div class="my-tit">账户</div>
        <div class="menu-description">管理您的账户信息</div>
        <ElMenu :default-active="activeKey" class="settings-menu" @select="handleMenuSelect">
          <ElMenuItem v-for="item in menuOptions" :key="item.key" :index="item.key">
            <template #title>
              <div class="menu-item-content">
                <i class="iconfont !text-[18px]" :class="item.icon"></i>
                <span>{{ item.label }}</span>
              </div>
            </template>
          </ElMenuItem>
        </ElMenu>
      </div>

      <!-- 右侧内容区域 -->
      <div class="right-content">
        <!-- 使用按模块拆分的组件 -->
        <ProfileTab v-if="activeKey === 'profile'" />
        <StatisticsTab v-else-if="activeKey === 'statistics'" />
        <SecurityTab v-else-if="activeKey === 'security'" />
        <AboutTab v-else-if="activeKey === 'about'" />
      </div>
    </div>
  </ElDialog>
</template>

<style scoped>
.my-tit {
  font-size: 32px;
  line-height: 33px;
  text-align: left;
  font-style: normal;
}
.dialog-container {
  display: flex;
  height: 500px;
}

.left-menu {
  width: 270px;
  border-right: 1px solid #eaecef;
  padding: 16px;
}

.menu-description {
  padding: 12px 0px;
  color: #606266;
  font-size: 14px;
}

.right-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.settings-menu {
  border-right: none;
}

.menu-item-content {
  display: flex;
  align-items: center;
}

.iconfont {
  margin-right: 10px;
  font-size: 16px;
}

:deep(.el-menu-item.is-active) {
  /* color: #4c5cec; */
  background: rgba(76, 92, 236, 0.1);
}

:deep(.el-menu-item:hover) {
  background: rgb(243, 243, 245);
}

:deep(.el-menu-item + .el-menu-item) {
  margin-top: 4px;
}

:deep(.el-dialog__header) {
  margin-right: 0;
  padding: 20px;
  border-bottom: 1px solid #eaecef;
}

:deep(.el-dialog__body) {
  padding: 0;
}

:deep(.el-menu-item) {
  height: 46px;
  border-radius: 8px;
}
</style>

<style>
.section-header {
  padding-bottom: 16px;
  margin-bottom: 16px;
  border-bottom: 1px solid #eaeaea;
}

.section-title {
  font-size: 20px;
  font-weight: 500;
}

.profile-item {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.item-label {
  width: 80px;
  color: #606266;
}

.item-value {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
