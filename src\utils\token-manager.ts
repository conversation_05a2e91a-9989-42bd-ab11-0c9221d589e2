import { useAgentUserStore, useUserStore } from '@/store/modules/user'

// 当前请求上下文，用于指定使用哪个Store的token
let currentAgentId: string | null = null

// 设置当前助手上下文
export const setRequestAgentContext = (agentId: string | null) => {
  currentAgentId = agentId
  console.log('setRequestAgentContext', currentAgentId)
}

// 获取当前请求上下文
export const getRequestAgentContext = () => currentAgentId

// 获取当前上下文对应的token
export const getCurrentToken = () => {
  if (currentAgentId) {
    // 如果有助手上下文，使用助手特定的Store
    const agentUserStore = useAgentUserStore(currentAgentId)
    return agentUserStore.token
  } else {
    // 否则使用默认的用户Store
    const userStore = useUserStore()
    return userStore.token
  }
}
