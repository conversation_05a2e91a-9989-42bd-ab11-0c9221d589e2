import { expect, test } from '@playwright/test'

test('访问百度并搜索', async ({ page }) => {
  // 打开百度首页
  console.log('正在打开百度首页...')
  await page.goto('https://www.baidu.com')

  // 等待页面加载完成
  await page.waitForLoadState('networkidle')

  // 获取页面标题并验证
  const title = await page.title()
  console.log(`页面标题: ${title}`)
  expect(title).toContain('百度')

  // 在搜索框中输入内容
  console.log('在搜索框中输入"Playwright自动化测试"')
  await page.fill('#kw', 'Playwright自动化测试')

  // 点击搜索按钮
  console.log('点击搜索按钮')
  await page.click('#su')

  // 等待搜索结果加载
  console.log('等待搜索结果加载...')
  await page.waitForSelector('#content_left')

  // 截图保存搜索结果
  console.log('保存搜索结果截图')
  await page.screenshot({ path: 'baidu-search-results.png', fullPage: true })

  console.log('测试完成！')
})
