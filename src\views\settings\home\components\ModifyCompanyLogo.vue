<script setup lang="ts">
import 'vue-cropper/dist/index.css'
import { VueCropper } from 'vue-cropper'
import type { UploadRawFile } from 'element-plus'
import { ElMessage } from 'element-plus'
import { postSystemUserProfileAvatar } from '@/api/system'

const props = defineProps<{
  logoSrc: string
  editCompanyLogo: (data: Blob, fileName: string) => Promise<boolean>
}>()

// 定义emit
// const emit = defineEmits(['avatarUpdated'])

interface Options {
  img: string | any // 裁剪图片的地址
  autoCrop: boolean // 是否默认生成截图框
  autoCropWidth: number // 默认生成截图框宽度
  autoCropHeight: number // 默认生成截图框高度
  fixedBox: boolean // 固定截图框大小 不允许改变
  fileName: string
  previews: any // 预览数据
  outputType: string
}

const open = defineModel({ default: false })
const title = ref('修改头像')

const cropper = ref<any>({})
// 图片裁剪数据
const options = reactive<Options>({
  img: '',
  autoCrop: true,
  autoCropWidth: 200,
  autoCropHeight: 200,
  fixedBox: true,
  outputType: 'png',
  fileName: '',
  previews: {},
})

watchEffect(() => {
  options.img = props.logoSrc
})

function handleDialogClose() {
  options.img = props.logoSrc
}

// /** 编辑头像 */
// const editCropper = () => {
//   open.value = true
// }
/** 覆盖默认上传行为 */
const requestUpload = (): any => {}
/** 向左旋转 */
const rotateLeft = () => {
  cropper.value.rotateLeft()
}
/** 向右旋转 */
const rotateRight = () => {
  cropper.value.rotateRight()
}
/** 图片缩放 */
const changeScale = (num: number) => {
  num = num || 1
  cropper.value.changeScale(num)
}
/** 上传预处理 */
const beforeUpload = (file: UploadRawFile): any => {
  if (!file.type.includes('image/')) {
    ElMessage.error('文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。')
  } else {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => {
      options.img = reader.result
      options.fileName = file.name
    }
  }
}
/** 上传图片 */
const uploadImg = async () => {
  cropper.value.getCropBlob(async (data: Blob) => {
    // const formData = new FormData()
    // formData.append('avatarfile', data, options.fileName)
    try {
      const res: boolean = await props.editCompanyLogo(data, options.fileName)
      if (!res) {
        return
      }
      open.value = false
      // 发出自定义事件通知父组件头像已更新
      // emit('avatarUpdated', options.img)
      ElMessage.success('修改成功')
      open.value = false
    } catch (error) {
      ElMessage.error('上传失败')
    }
  })
}
/** 实时预览 */
const realTime = (data: any) => {
  options.previews = data
}

// defineExpose({
//   editCropper,
// })
</script>

<template>
  <el-dialog v-model="open" :title="title" width="800px" append-to-body @close="handleDialogClose">
    <el-row>
      <el-col :xs="24" :md="12" :style="{ height: '350px' }">
        <VueCropper
          v-if="open"
          ref="cropper"
          :img="options.img"
          :info="true"
          :auto-crop="options.autoCrop"
          :auto-crop-width="options.autoCropWidth"
          :auto-crop-height="options.autoCropHeight"
          :fixed-box="options.fixedBox"
          :output-type="options.outputType"
          @real-time="realTime"
        />
      </el-col>
      <el-col :xs="24" :md="12" :style="{ height: '350px' }">
        <div class="avatar-upload-preview">
          <!-- 有一个诡异的样式污染 img{max-width: 100%;}，不知道哪里来的，项目里没搜到，先对应处理下吧 -->
          <img :src="options.previews.url" :style="{ ...options.previews.img, maxWidth: 'none' }" />
        </div>
      </el-col>
    </el-row>
    <br />
    <el-row>
      <el-col :lg="2" :md="2">
        <el-upload
          action="#"
          :http-request="requestUpload"
          :show-file-list="false"
          :before-upload="beforeUpload"
        >
          <el-button>
            选择
            <el-icon class="el-icon--right">
              <Upload />
            </el-icon>
          </el-button>
        </el-upload>
      </el-col>
      <el-col :lg="{ span: 1, offset: 2 }" :md="2">
        <el-button icon="Plus" @click="changeScale(1)"></el-button>
      </el-col>
      <el-col :lg="{ span: 1, offset: 1 }" :md="2">
        <el-button icon="Minus" @click="changeScale(-1)"></el-button>
      </el-col>
      <el-col :lg="{ span: 1, offset: 1 }" :md="2">
        <el-button icon="RefreshLeft" @click="rotateLeft()"></el-button>
      </el-col>
      <el-col :lg="{ span: 1, offset: 1 }" :md="2">
        <el-button icon="RefreshRight" @click="rotateRight()"></el-button>
      </el-col>
      <el-col :lg="{ span: 2, offset: 6 }" :md="2">
        <el-button type="primary" @click="uploadImg()">提 交</el-button>
      </el-col>
    </el-row>
  </el-dialog>
</template>

<style lang="less" scoped>
.el-button {
  min-width: 0;
}
.avatar-upload-preview {
  position: absolute;
  top: 50%;
  transform: translate(50%, -50%);
  width: 200px;
  height: 200px;
  border-radius: 8px;
  box-shadow: 0 0 4px #ccc;
  overflow: hidden;
}
</style>
