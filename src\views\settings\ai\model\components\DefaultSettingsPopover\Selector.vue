<script setup lang="ts">
import { ref, computed } from 'vue' // 添加 ref 和 computed
import { ElInput, ElIcon } from 'element-plus' // 导入 ElInput 和 ElIcon
import { CaretBottom, CaretTop, Search } from '@element-plus/icons-vue' // 导入图标
import { vAutoAnimate } from '@formkit/auto-animate/vue'
import { getModelLogo } from '@/utils/models'

defineOptions({
  name: 'ModelSelector',
})

const props = withDefaults(
  defineProps<{
    modelList: any[]
    loading?: boolean
    width?: number
    disabled?: boolean
  }>(),
  {
    modelList: () => [],
    loading: false,
    width: 320,
    disabled: false,
  },
)

const emits = defineEmits(['change', 'update'])

const currentModel = defineModel<string>({ default: '' })

const isOpen = ref(false) // 新增展开状态
const searchQuery = ref('') // 新增搜索状态

const isFocus = ref(false)

const selectModel = (model: any) => {
  currentModel.value = model.id
  isOpen.value = false
  emits('change', model.id)
}

const selectedModel = computed(() => {
  return props.modelList.find(model => String(model.id) === String(currentModel.value))
})

/**
 * 根据模型平台分组模型列表
 */
function groupModelsByPlatform(models: any[]) {
  const platformList = models.map(model => model.platformName)

  const uniquePlatformList = platformList.filter(
    (item, index) => platformList.indexOf(item) === index,
  )

  return uniquePlatformList.map(platformName => ({
    platformName,
    list: models.filter(model => model.platformName === platformName),
  }))
}

// 过滤模型列表
const filteredModels = computed(() => {
  const filterList = props.modelList || []
  if (!searchQuery.value) return groupModelsByPlatform(filterList)
  const query = searchQuery.value.toLowerCase()
  const list = filterList.filter(
    model =>
      model.displayName.toLowerCase().includes(query) || model.name.toLowerCase().includes(query),
  )
  return groupModelsByPlatform(list)
})
</script>

<template>
  <el-popover
    popper-class="model-popover"
    placement="bottom"
    :width="width"
    trigger="click"
    v-model:visible="isOpen"
    :disabled="disabled"
    @hide="searchQuery = ''"
  >
    <template #reference>
      <div
        class="trigger"
        :style="{ width: width + 'px' }"
        tabindex="0"
        :class="{ 'is-disabled': disabled }"
      >
        <img v-if="selectedModel" :src="getModelLogo(selectedModel?.name)" class="model-logo" />

        <span class="model-name">{{ selectedModel?.displayName || '选择模型' }}</span>
        <el-icon v-show="!isOpen"><CaretBottom /></el-icon>
        <el-icon v-show="isOpen"><CaretTop /></el-icon>
      </div>
    </template>
    <div class="dropdown-menu" v-loading="loading">
      <!-- 搜索框 -->
      <div class="search-box" :class="{ 'is-focus': isFocus }">
        <el-input
          class="model-search"
          v-model="searchQuery"
          placeholder="搜索模型"
          clearable
          @click.stop
          @focus="isFocus = true"
          @blur="isFocus = false"
        />
        <el-icon class="search-icon" size="18"><Search /></el-icon>
      </div>
      <div class="model-list" v-auto-animate>
        <div class="mb-6" v-for="item in filteredModels">
          <div class="text-[#646A73] text-[16px] mb-[12px]">{{ item.platformName }}</div>
          <div
            v-for="model in item.list"
            :key="model.name"
            class="menu-item"
            :class="{ active: model.id === currentModel }"
            @click="selectModel(model)"
          >
            <div class="truncate">
              <img
                v-if="getModelLogo(model?.name)"
                :src="getModelLogo(model?.name)"
                class="model-logo"
              />
              {{ model.displayName }}
            </div>
          </div>
        </div>
        <el-empty
          v-if="filteredModels.length === 0"
          :description="!searchQuery && modelList.length === 0 ? '暂无数据' : '未查询到相关模型'"
        />
      </div>
    </div>
  </el-popover>
</template>

<style lang="less" scoped>
.trigger {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 8px 12px;
  // border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  height: 32px; /* 确保高度与 NSelect 默认高度一致 */
  box-sizing: border-box; /* 包含 padding 和 border 在宽度内 */
  background: #eff4ff;

  &:hover {
    border-color: #a0cfff;
    background: #f0f7ff;
    transition: all 0.2s;
  }

  .model-logo {
    width: 20px;
    height: 20px;
    margin-right: 8px;
    flex-shrink: 0; /* 防止图片缩小 */
  }

  .model-name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; /* 防止文本换行 */
  }

  .arrow-icon {
    transition: transform 0.3s;
    margin-left: 8px;
    font-size: 16px; /* 调整图标大小 */
    flex-shrink: 0; /* 防止图标缩小 */
  }

  &:focus {
    border-color: #409eff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    outline: none;
  }
  &:active {
    transform: scale(0.98);
    transition: transform 0.1s;
  }
}

.is-disabled {
  cursor: not-allowed;
  opacity: 0.6;
  background: #f5f5f5 !important;
  &:hover {
    border-color: #dcdfe6 !important;
    background: #f5f5f5 !important;
  }
  &:focus {
    border-color: #dcdfe6 !important;
    box-shadow: none;
    outline: none;
  }
  &:active {
    transform: scale(1);
  }
}

.dropdown-menu {
  .search-box {
    position: relative;
    padding-right: 24px;
    margin-bottom: 24px;
    border-bottom: 1px solid #e8e8e8;
    &.is-focus {
      border-color: #409eff;
    }

    &:hover {
      border-color: #409eff;
    }

    .model-search:deep(.el-input__wrapper) {
      box-shadow: none;

      &.is-focus {
        box-shadow: none;
      }
    }

    .search-icon {
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  .model-list {
    max-height: 400px;
    overflow-y: auto;
  }

  .fixed-section {
    // border-bottom: 1px solid #f0f0f0;
    padding-bottom: 24px; /* 增加底部内边距 */
    .fixed-item {
      &:hover {
        background: #e6eaf4;
      }
    }
  }

  .divider {
    height: 1px;
    background: #f0f0f0;
    margin: 8px 0;
  }

  .menu-item {
    padding: 8px;
    margin-right: 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &.active {
      background-color: #ecf5ff;
      border: 1px solid #d9ecff;
      color: #409eff;
      border-radius: 8px;
    }

    .fixed-icon {
      display: inline-block;
      width: 22px;
      visibility: hidden;
    }

    &:hover {
      background: #e6eaf4;
      border-radius: 8px;

      .fixed-icon {
        visibility: visible;
      }
    }

    .model-logo {
      display: inline-block;
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }
  }
}
</style>

<style>
.model-popover.el-popover.el-popper {
  background: rgba(255, 255, 255, 0.85);
  box-shadow: 0px 2px 8px 2px rgba(0, 0, 0, 0.15);
  border-radius: 16px;
  backdrop-filter: blur(8px);
}
</style>
