<script setup lang="ts">
import { computed } from 'vue'
import { NConfigProvider } from 'naive-ui'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import { useRoute } from 'vue-router'
import { ConversationStatusIndicator, NaiveProvider } from '@/components/common'
import { useTheme } from '@/hooks/useTheme'
import { useLanguage } from '@/hooks/useLanguage'
import { useUserStore } from '@/store'
import { useStreamResponseCleanup } from '@/hooks/useStreamResponseCleanup'

const { theme, themeOverrides } = useTheme()
const { language } = useLanguage()
const userStore = useUserStore()
const route = useRoute()

// 使用流式响应清理功能
useStreamResponseCleanup()
</script>

<template>
  <NConfigProvider
    class="h-full"
    :theme="theme"
    :theme-overrides="themeOverrides"
    :locale="language"
  >
    <NaiveProvider>
      <el-config-provider :locale="zhCn">
        <RouterView />
        <!-- 会话状态指示器 -->
        <ConversationStatusIndicator />
      </el-config-provider>
    </NaiveProvider>
  </NConfigProvider>
</template>

<style lang="less"></style>
