declare namespace Chat {
  interface ToolExecution {
    name: string
    arguments: string
    result: string
  }

  interface Chat {
    dateTime: string
    text: string
    inversion?: boolean
    error?: boolean
    loading?: boolean
    conversationOptions?: ConversationRequest | null
    // requestOptions: { prompt: string; options?: ConversationRequest | null }
    toolExecutionList?: ToolExecution[]
    chatContentRecordList?: ChatContentRecord[]
    reasoningContent?: string
    imageInfoList?: Array<{ imageUrl: string; ossId: string; fileName?: string }>
  }

  interface History extends ConversationVo {
    title: string
    conversationId?: string
    isCasualChat?: boolean
    summary?: string
    systemMessage?: string
    lastChatTime?: string
    fromAgent?: boolean
    isEditing?: boolean
  }
  interface ConversationVo {
    chatContentRecordList?: ChatContentRecord[]
    conversationId: string
    id?: string
    /**
     * 是否开启知识库
     */
    kbEnableFlag?: boolean
    /**
     * 关联知识库列表
     */
    kbList?: KnowledgeBase.KnowledgeBaseVo[]
    /**
     * 上次对话id
     */
    lastChatId?: number
    /**
     * 上次对话时间
     */
    lastChatTime?: string
    /**
     * 上次对话问题
     */
    lastQuestion?: string
    /**
     * 最大上下文数量
     */
    maxContextCount?: number
    /**
     * 模型id
     */
    modelId?: number
    /**
     * 随机性 (0-2)
     */
    temperature?: number
    /**
     * 最大回复长度
     */
    maxTokens?: number
    /**
     * 是否开启单次回复限制
     */
    singleReplyLimitFlag?: boolean
    /**
     * 名称
     */
    name?: string
    /**
     * 副标题
     */
    subtitle?: string
    /**
     * 系统提示词
     */
    systemMessage?: string
    /**
     * rerank模型id
     */
    rerankModelId?: number
    /**
     * rerank模型id
     */
    rerankModelName?: string
    /**
     * 最大召回数
     */
    maxResults?: number
    /**
     * 最低匹配度
     */
    minScore?: number
    /**
     * 模型name：modelName
     */
    modelName?: string
    /**
     * 模型显示名称
     */
    modelDisplayName?: string
  }

  interface ChatState {
    active: string | null
    usingContext: boolean
    useSystemMessage: boolean
    history: History[]
    chat: { conversationId: string; data: Chat[] }[]
    modelId: number
  }

  interface ConversationRequest {
    conversationId?: string
    parentMessageId?: string
    chatId?: string
    id?: string
    model?: string
  }

  interface ChatRecordVo {
    chatId?: string
    id?: string
    content: string
    role: string
    createTime?: string
    updateTime?: string
    toolExecutionList?: ToolExecution[]
    chatContentRecordList?: ChatContentRecord[]
    reasoningContent?: string
    imageInfo?: string
  }

  interface ChatContentRecord {
    /**
     * chat表的主键
     */
    chatPrimaryKey?: number
    /**
     * 片段的向量库id
     */
    embeddingId?: string
    /**
     * 主键
     */
    id?: number
    metadata?: RAGMetadataDto
    /**
     * 召回次数
     */
    recallCount?: number
    /**
     * 相似度
     */
    score?: number
    /**
     * 片段来源，EMBEDDING:向量库，RE_RANK:rerank
     */
    segmentSource?: string
    /**
     * 片段内容
     */
    textSegment?: string
    /**
     * 字数
     */
    wordCount?: number
    [property: string]: any
  }
  interface RAGMetadataDto {
    enabled?: string
    fileName?: string
    fileType?: string
    fileUrl?: string
    index?: string
    kbId?: string
    kbItemId?: string
    [property: string]: any
  }
}
