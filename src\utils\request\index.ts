import type { AxiosProgressEvent, AxiosResponse, GenericAbortSignal } from 'axios'
import request, { getRequestAgentContext, setRequestAgentContext } from './axios'
// import { useAuthStore } from '@/store'

export interface HttpOption {
  url: string
  data?: any
  method?: string
  headers?: any
  onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void
  signal?: GenericAbortSignal
  beforeRequest?: () => void

  afterRequest?: () => void
  keepUndefined?: boolean
}

export type Response<T = any> = T extends { rows: any[] }
  ? {
    total: number
    code: number
    msg: string
  } & T
  : {
    data: T
    code: number
    msg: string
  }

// 清理空参数的工具函数
function cleanParams(params: Record<string, any>, keepUndefined = false) {
  // console.log('cleanParams-1-params', params)

  // 如果是FormData，直接返回不做处理
  if (params instanceof FormData) {
    return params
  }

  const cleanedParams: Record<string, any> = {}

  Object.entries(params).forEach(([key, value]) => {
    // 如果是对象，递归清理
    if (value && typeof value === 'object' && !Array.isArray(value)) {
      const cleaned = cleanParams(value, keepUndefined)
      if (Object.keys(cleaned).length > 0) cleanedParams[key] = cleaned
    }
    // 保留非空值（排除 undefined、null、空字符串）
    else if (keepUndefined ? value !== null : value !== undefined && value !== null) {
      // && value !== ''
      cleanedParams[key] = value
    }
  })
  // console.log('cleanParams-2-cleanedParams', cleanedParams)
  return cleanedParams
}

function http<T = any>({
  url,
  data,
  method,
  headers,
  onDownloadProgress,
  signal,
  beforeRequest,
  afterRequest,
  keepUndefined = false,
}: HttpOption) {
  const successHandler = (res: AxiosResponse<Response<T>>) => {
    // const authStore = useAuthStore()

    // if (res.data.status === 'Success' || typeof res.data === 'string')
    //   return res.data

    // if (res.data.status === 'Unauthorized') {
    //   authStore.removeToken()
    //   window.location.reload()
    // }
    // console.log('res.data', res.data)

    return res.data
    // return Promise.reject(res.data)
  }

  const failHandler = (error: Response<Error>) => {
    afterRequest?.()
    throw new Error(error?.msg || 'Error')
  }

  beforeRequest?.()

  // method转换成大写
  method = method?.toUpperCase() || 'GET'

  // 根据keepUndefined参数决定是否保留undefined值
  const params = cleanParams(
    Object.assign(typeof data === 'function' ? data() : (data ?? {}), {}),
    keepUndefined,
  )

  return method === 'GET'
    ? request.get(url, { params, signal, onDownloadProgress }).then(successHandler, failHandler)
    : method === 'DELETE'
      ? request.delete(url, { params, headers, signal }).then(successHandler, failHandler)
      : method === 'PUT'
        ? request
          .put(url, params, { headers, signal, onDownloadProgress })
          .then(successHandler, failHandler)
        : request
          .post(url, params, { headers, signal, onDownloadProgress })
          .then(successHandler, failHandler)
}

export function get<T = any>({
  url,
  data,
  method = 'GET',
  onDownloadProgress,
  signal,
  beforeRequest,
  afterRequest,
  keepUndefined,
}: HttpOption): Promise<Response<T>> {
  return http<T>({
    url,
    method,
    data,
    onDownloadProgress,
    signal,
    beforeRequest,
    afterRequest,
    keepUndefined,
  })
}

export function post<T = any>({
  url,
  data,
  method = 'POST',
  headers,
  onDownloadProgress,
  signal,
  beforeRequest,
  afterRequest,
  keepUndefined,
}: HttpOption): Promise<Response<T>> {
  return http<T>({
    url,
    method,
    data,
    headers,
    onDownloadProgress,
    signal,
    beforeRequest,
    afterRequest,
    keepUndefined,
  })
}

export function put<T = any>({
  url,
  data,
  method = 'PUT',
  headers,
  onDownloadProgress,
  signal,
  beforeRequest,
  afterRequest,
  keepUndefined,
}: HttpOption): Promise<Response<T>> {
  return http<T>({
    url,
    method,
    data,
    headers,
    onDownloadProgress,
    signal,
    beforeRequest,
    afterRequest,
    keepUndefined,
  })
}

export function deleteReq<T = any>({
  url,
  data,
  method = 'DELETE',
  headers,
  signal,
  beforeRequest,
  afterRequest,
  keepUndefined,
}: HttpOption): Promise<Response<T>> {
  return http<T>({
    url,
    method,
    data,
    headers,
    signal,
    beforeRequest,
    afterRequest,
    keepUndefined,
  })
}

export default post

// 从token管理工具导出上下文管理函数
export { setRequestAgentContext, getRequestAgentContext } from '@/utils/token-manager'
