export const VERSION_STATUS_EX = [
  {
    code: 'current',
    name: '当前',
    color: '#4865E8',
    value: 100,
  },
  {
    code: 'disabled',
    name: '状态',
    color: '#B6BAC1',
    value: 101,
  },
] as const

export const VERSION_STATUS_MAIN = [
  {
    code: 'private',
    name: '私人助手',
    color: '#47D5C6',
    value: -1,
  },
  {
    code: 'pending',
    name: '审核中',
    color: '#FF9349',
    value: 0,
  },
  {
    code: 'success',
    name: '已发布',
    color: '#1EBF60',
    value: 1,
  },
  {
    code: 'failed',
    name: '审核失败',
    color: '#F25B37',
    value: 2,
  },
] as const

export const VERSION_STATUS_MANAGE = [
  {
    code: 'private',
    name: '私人助手',
    color: '#47D5C6',
    value: -1,
  },
  {
    code: 'pending',
    name: '待审核',
    color: '#FF9349',
    value: 0,
  },
  {
    code: 'success',
    name: '已发布',
    color: '#1EBF60',
    value: 1,
  },
  {
    code: 'failed',
    name: '审核失败',
    color: '#F25B37',
    value: 2,
  },
] as const

export const VERSION_STATUS = [...VERSION_STATUS_EX, ...VERSION_STATUS_MAIN]

export type VersionStatusCode = (typeof VERSION_STATUS)[number]['value']

export const kbIdKey = Symbol() as InjectionKey<string>
