const { chromium } = require('playwright')

;(async () => {
  // 启动浏览器
  console.log('启动浏览器...')
  const browser = await chromium.launch({
    headless: false, // 设置为非无头模式，以便可以看到浏览器界面
  })

  // 创建新页面
  console.log('创建新页面...')
  const page = await browser.newPage()

  // 导航到百度
  console.log('正在打开百度页面...')
  await page.goto('https://www.baidu.com')

  console.log('已成功打开百度页面！')

  // 在搜索框中输入"今天天气"
  console.log('在搜索框中输入"今天天气"')
  await page.fill('#kw', '今天天气')

  // 点击搜索按钮
  console.log('点击搜索按钮')
  await page.click('#su')

  // 等待搜索结果加载
  console.log('等待搜索结果加载...')
  await page.waitForSelector('#content_left')

  console.log('搜索完成，显示天气信息')

  // 等待用户手动关闭浏览器
  console.log('浏览器将保持打开状态，请手动关闭或按Ctrl+C终止程序')
})().catch(err => {
  console.error('发生错误:', err)
  process.exit(1)
})
