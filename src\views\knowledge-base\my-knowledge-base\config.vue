<script setup lang="ts">
import type { TableInstance } from 'element-plus'
import { getBizKbDetail, postBizKbUpdateKnowledgeBase } from '@/api/knowledge-base'
import type { AIModelVo } from '@/api'
import { getBizAiModelAvailableModels } from '@/api'
import { kbIdKey } from '@/utils/constants'
import { deptTreeSelect, listUser, listUserByDeptId } from '@/api/system/user'
import type { DeptVO } from '@/api/system/dept/types'
import AddIconSvg from '@/assets/knowledge/add-icon.svg?component'
import type { UserVO } from '@/api/system/user/types'

defineOptions({
  name: 'Config',
})
const id = inject(kbIdKey) as string
const models = ref<AIModelVo[]>([])
getBizAiModelAvailableModels({
  type: 'embedding',
}).then(res => {
  if (res.code === 200) {
    const tempList = (res.data || []).map(item => ({
      ...item,
      label: item.displayName,
      value: `${item.id}`,
      modelName: item.name,
    }))
    models.value = tempList
  }
})

type User = UserVO & { permissionLevel: number }
const shareUserList = ref<User[]>([])
const multipleTableRef = ref<TableInstance>()
const dialogVisible = ref(false)
const form = ref<KnowledgeBase.KnowledgeBaseBo>({})
type DePtWithPermission = DeptVO & { permissionLevel?: number }
// 表格中显示的部门数据树状结构
const apartmentTableData = ref<DePtWithPermission[]>([])

/**
 * 默认选择第一个部门
 */
const currentDepartent = ref<number>()

/**
 * 知识库详情
 */
const knowledgeBaseDetail = ref<KnowledgeBase.KnowledgeBaseBo>({})
function getConfig() {
  return getBizKbDetail<KnowledgeBase.KnowledgeBaseVo>(id).then(res => {
    if (res.code === 200) {
      knowledgeBaseDetail.value = res.data || {}
      const {
        id,
        name,
        type,
        modelId,
        modelExist,
        description,
        shareStatus,
        permissionList = [],
      } = res.data
      form.value = {
        id,
        name,
        type,
        modelId: modelExist ? modelId : undefined, // 如果模型失效，则不回填 modelId
        description,
        shareStatus,
        shareInfoList: permissionList.map(p => ({
          targetId: p.targetId,
          permissionLevel: p.permissionLevel,
          targetType: p.targetType,
        })),
      }
    }
    return res
  })
}

function addMembers() {
  const userTablePromise = handleQuery()
  if (userTablePromise) {
    userTablePromise.then(result => {
      // 已共享的成员id
      const selectUserIdList = shareUserList.value.map(u => u.userId) || []
      const tableData = result.rows
      const selectTable = tableData.filter((t: any) => selectUserIdList?.includes(t.userId))

      nextTick(() => {
        if (multipleTableRef.value && selectTable.length) {
          multipleTableRef.value.toggleRowSelection(selectTable, true)
        }
      })
    })
  }
  dialogVisible.value = true
}

const loading = ref(false)

const permissionList = [
  {
    label: '仅查看',
    value: 1,
  },
  {
    label: '可编辑',
    value: 2,
  },
  {
    label: '不可见',
    value: 0,
  },
]

const handleNodeClick = (data: DeptVO) => {
  currentDepartent.value = data.id
  handleQuery()
}

const defaultProps = {
  children: 'children',
  label: 'label',
}

const selectMemberList = ref<UserVO[]>([])

const handleSelectionChange = (val: UserVO[]) => {
  selectMemberList.value = val
}

function closeDialog() {
  dialogVisible.value = false
}

/**
 * 保存知识库配置
 */
function saveKnowledgeconfig() {
  // 知识库的名称必填、模型必选
  if (!form.value.name) {
    ElMessage.error('请填写知识库名称')
    return
  }
  if (!form.value.modelId) {
    ElMessage.error('请选择Embedding模型')
    return
  }

  // 如果选择 指定部门共享，更新共享列表
  if (form.value.shareStatus === 2) {
    const permissionList: KnowledgeBase.KnowledgeBasePermissionBo[] = []

    form.value.shareInfoList = traverseDept(apartmentTableData.value, permissionList)
  }

  // 如果选择成员共享，更新共享列表
  if (form.value.shareStatus === 3) {
    form.value.shareInfoList = shareUserList.value.map(user => ({
      targetId: user.userId,
      targetType: '2',
      permissionLevel: user.permissionLevel || 0,
    }))
  }

  /**
   * 判断文本向量模型是否改变，如果改变进行提示
   */
  if (form.value.modelId !== knowledgeBaseDetail.value.modelId) {
    saveDialogVisible.value = true
    return
  }

  saveConfig()
}

const saveLoading = ref(false)
function saveConfig() {
  saveLoading.value = true
  return postBizKbUpdateKnowledgeBase(form.value)
    .then(res => {
      if (res.code === 200) {
        getConfig()
        ElMessage.success('保存成功')
        return true
      } else {
        ElMessage.error(res.msg)
      }
    })
    .finally(() => {
      saveLoading.value = false
    })
}

/** 查询部门下拉树结构 */
const getTreeSelect = async () => {
  return deptTreeSelect().then(res => {
    apartmentTableData.value = res.data || []
    currentDepartent.value = apartmentTableData.value[0]?.id
    return res
  })
}

/**
 * 回填部门权限
 */
const fillDeptPermissions = () => {
  if (!apartmentTableData.value) {
    return
  }
  if (form.value.id && apartmentTableData.value) {
    const permissionList = form.value.shareInfoList || []
    const permissionDepIdList = permissionList.map(p => p.targetId)
    const permissionMap = permissionList.reduce(
      (acc: Record<string | number, number>, item) => {
        acc[item.targetId] = item.permissionLevel || 0
        return acc
      },
      {} as Record<string | number, number>,
    )

    // 递归遍历树形结构
    const traverse = (depts: DePtWithPermission[]) => {
      depts.forEach(dept => {
        if (permissionDepIdList.includes(dept.id)) {
          dept.permissionLevel = permissionMap[dept.id]
        } else {
          // 默认不可见
          dept.permissionLevel = 0
        }
        if (dept.children) {
          traverse(dept.children)
        }
      })
    }

    traverse(apartmentTableData.value)
  }
}

onMounted(() => {
  Promise.all([getConfig(), getTreeSelect()]).then(([resDetaiil, resTree]) => {
    if (resDetaiil.code === 200) {
      if (resDetaiil.data.shareStatus === 2) {
        // 回填部门共享权限
        fillDeptPermissions()
      } else if (resDetaiil.data.shareStatus === 3 && (resTree as any).code === 200) {
        if (currentDepartent.value) {
          getUserListByApartment(currentDepartent.value).then(res => {
            // 回填共享成员列表
            if (res.code === 200) {
              const rows = res.rows || []
              const permissionList = resDetaiil.data.permissionList || []
              const userIdList = permissionList.map(p => p.targetId)
              shareUserList.value = rows
                .filter((u: User) => userIdList.includes(u.userId))
                .map((u: User) => ({
                  ...u,
                  permissionLevel:
                    permissionList.find(p => p.targetId === u.userId)?.permissionLevel || 0,
                }))
            }
          })
        }
      }
    }
  })
})

function traverseDept(depts: DePtWithPermission[], acc: KnowledgeBase.KnowledgeBasePermissionBo[]) {
  depts.forEach((dept: DePtWithPermission) => {
    acc.push({
      permissionLevel: dept.permissionLevel ?? 0,
      targetId: dept.id,
      targetType: '1',
    })
    if (dept.children) {
      traverseDept(dept.children, acc)
    }
  })
  return acc
}

const userList = ref([])
const queryParams = ref({
  pageNum: 1,
  pageSize: 999999999,
  nickName: '',
})
/**
 * 获取选择部门的成员
 */
function getUserListByApartment(deptId: number | string) {
  // listUserByDeptId(deptId).then(res => {
  //   console.log(res)
  //   // if (res.code === 200) {

  //   // }
  // })

  return listUser({ ...queryParams.value, deptId }).then((res: any) => {
    if (res.code === 200) {
      userList.value = res.rows
    }
    return res
  })
}

function handleQuery() {
  if (!currentDepartent.value) return
  return getUserListByApartment(currentDepartent.value)
}

function resetQuery() {
  queryParams.value.nickName = ''
  handleQuery()
}

/**
 * 添加成员 保存
 */
function saveSelectMember() {
  // 列表中选择的成员 与 原本的成员做对比，如果原来有，成员权限保持不表，如果成员权限 默认为仅查看
  const tempUser = selectMemberList.value.map(member => {
    const user = shareUserList.value.find(u => u.userId === member.userId)
    if (user) {
      return user
    } else {
      return Object.assign({ permissionLevel: 1 }, member)
    }
  })

  shareUserList.value = tempUser
  closeDialog()
}

const handleDelete = (id: number) => {
  ElMessageBox.confirm('确认删除该成员吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    shareUserList.value = shareUserList.value.filter(s => s.userId !== id)
  })
}

const saveDialogVisible = ref(false)

function confirmSave() {
  saveConfig().then(res => {
    saveDialogVisible.value = false
  })
}

/**
 * 当前向量模型名称
 */
const currentModelName = computed(() => {
  const model = models.value.find(m => String(m.id) === String(form.value.modelId))
  return model?.displayName || model?.name
})
</script>

<template>
  <div class="p-[20px] flex flex-col h-full">
    <div class="flex justify-between items-center mb-[24px]">
      <div>
        <img
          class="size-8 inline-block mr-2 align-middle"
          src="@/assets/knowledge/search-icon.png"
          alt=""
        />
        <span class="text-[18px] text-[#30343A] align-middle">配置</span>
      </div>
      <el-button type="primary" :loading="saveLoading" @click="saveKnowledgeconfig">保存</el-button>
    </div>

    <div class="config-form-wrap">
      <el-form label-position="top" :model="form" label-width="auto">
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="知识库名称">
              <el-input v-model="form.name" show-word-limit maxlength="100"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="文本向量模型">
              <!-- 取消 disabled 时，记得去掉 cursor-not-allowed 样式，不知道为什么组件内部的 cursor: not-allowed 样式没有生效 -->
              <Selector
                class="model-selector"
                v-model="form.modelId"
                :opentionList="models"
                placeholder="请选择文本向量模型"
              >
              </Selector>
              <!-- <el-select
                v-model="form.modelId"
                class="model-select cursor-not-allowed"
                placeholder="请选择文本向量模型"
              >
                <template #prefix>
                  <img class="size-[20px]" src="@/assets/knowledge/model-icon.png" alt="" />
                </template>
                <el-option
                  v-for="item in models"
                  :key="item.id"
                  :label="item.displayName || item.name"
                  :value="item.id"
                />
              </el-select> -->
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="知识库描述">
          <el-input
            v-model="form.description"
            style="height: 120px"
            placeholder="描述该数据集的内容"
            show-word-limit
            maxlength="2000"
            type="textarea"
            resize="none"
            :rows="5"
          />
        </el-form-item>
      </el-form>
    </div>

    <div class="flex justify-between items-center my-4 h-10">
      <div>
        <span>共享：</span>
        <el-radio-group v-model="form.shareStatus" class="align-middle">
          <el-radio :value="0">只有我</el-radio>
          <el-radio :value="1">全体共享</el-radio>
          <el-radio :value="2">指定部门共享</el-radio>
          <el-radio :value="3">指定成员共享</el-radio>
        </el-radio-group>
      </div>
      <el-button
        v-if="form.shareStatus === 3"
        class="ex-el-button-gray"
        type="primary"
        plain
        @click="addMembers"
      >
        <span class="text-[20px]">
          <AddIconSvg class="el-icon mr-[8px]" />
        </span>
        添加成员
      </el-button>
    </div>
    <div>
      <el-table v-if="form.shareStatus === 3" v-loading="loading" :data="shareUserList">
        <el-table-column prop="nickName" label="成员" />
        <el-table-column prop="phonenumber" label="手机号" />
        <el-table-column prop="deptName" label="部门" />
        <el-table-column prop="permission" label="权限">
          <template #default="{ row }">
            <el-select
              v-model="row.permissionLevel"
              class="permission-select"
              size="small"
              style="width: 100px"
            >
              <el-option
                v-for="item in permissionList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="80">
          <template #default="{ row }">
            <el-button link type="danger" @click="handleDelete(row.userId)">
              <el-icon size="18"><Delete /></el-icon>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-table
        v-else-if="form.shareStatus === 2"
        :data="apartmentTableData"
        row-key="id"
        default-expand-all
      >
        <el-table-column prop="label" label="部门" />
        <el-table-column label="权限" width="320">
          <template #default="{ row }">
            <el-radio-group v-model="row.permissionLevel">
              <el-radio v-for="p in permissionList" :key="p.value" :value="p.value">{{
                p.label
              }}</el-radio>
            </el-radio-group>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
  <el-dialog
    v-model="dialogVisible"
    draggable
    class="member-dialog"
    width="1000"
    :show-close="false"
  >
    <div class="flex h-[720px]">
      <div class="w-[272px] bg-[#F7F8FA] p-[20px] h-full flex flex-col">
        <h1 class="text-[18px] text-[#30343A] mb-6">添加成员</h1>
        <el-input
          v-model="queryParams.nickName"
          placeholder="搜索"
          clearable
          @clear="resetQuery"
          @keyup.enter="handleQuery"
        >
          <template #suffix>
            <el-icon>
              <Search />
            </el-icon>
          </template>
        </el-input>
        <div class="flex-1 min-h-0 overflow-auto">
          <el-tree
            :expand-on-click-node="false"
            node-key="id"
            :current-node-key="currentDepartent"
            :data="apartmentTableData"
            highlight-current
            default-expand-all
            :props="defaultProps"
            @node-click="handleNodeClick"
          />
        </div>
      </div>
      <div class="select-member-content h-full flex flex-col">
        <div class="flex justify-between items-center mb-6">
          <div>
            <img class="size-6 inline-block mr-2" src="@/assets/knowledge/member-icon.png" alt="" />
            <span class="text-[#4865E8] text-[16px] align-middle"
              >已选择<span class="font-bold mx-1">{{ selectMemberList.length }}</span
              >名成员</span
            >
          </div>
          <el-icon class="cursor-pointer" size="24" @click="closeDialog"><Close /></el-icon>
        </div>
        <div class="flex-1 min-h-0">
          <el-table
            ref="multipleTableRef"
            :data="userList"
            row-key="userId"
            height="100%"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column type="index" label="序号" width="60" />
            <el-table-column property="nickName" label="姓名" />
            <el-table-column property="phonenumber" label="手机号" />
            <el-table-column property="deptName" label="部门" />
            <el-table-column property="post" label="岗位">
              <template #default> 岗位 </template>
            </el-table-column>
            <el-table-column property="role" label="角色" width="80">
              <template #default> 角色 </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="text-right mt-5">
          <el-button class="mr-4" @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="saveSelectMember">确认</el-button>
        </div>
      </div>
    </div>
  </el-dialog>

  <el-dialog v-model="saveDialogVisible" width="600">
    <template #header>
      <span class="text-[24px] g-family-medium">确认更换向量模型？ </span>
    </template>
    <div class="text-[16px]/[28px]">
      <div>
        您即将把向量模型更改为[<span class="text-[#4865E8]">{{ currentModelName }}</span
        >]。
      </div>
      <div>此操作将导致以下后果，请仔细阅读：</div>
    </div>
    <ul class="precautions">
      <li>• 清空现有数据：当前知识库的所有向量索引将被立即清空且无法恢复。</li>

      <li>• 服务暂时中断：在重新构建完成前，知识库的语义搜索和智能问答功能将无法使用。</li>
      <li>• 耗时且可能产生费用：此过程可能需要一定时间，并可能产生相关的 API 调用费用。</li>
    </ul>

    <div class="text-[18px] g-family-medium my-[24px]">您确定要继续吗？</div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="saveDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="confirmSave"
          >确认更换并重新构建</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<style lang="less" scoped>
:deep(.el-input),
.model-select,
:deep(.el-textarea__inner) {
  background-color: #fff;
}

.config-form-wrap {
  border-bottom: 1px solid rgba(218, 221, 232, 0.5);
}

// .ease-btn {
//   border-radius: 8px;
//   border: 1px solid #dadde8;
//   padding: 9px 12px;
//   background: #f7f8fa;

//   &.disabled {
//     background: rgba(230, 234, 244, 0.6);
//   }
// }

:deep(.el-table) {
  tr th {
    // background: rgba(218, 221, 232, 0.5) !important;
    color: #30343a;
  }
  .el-table__body {
    background: #ffffff;
  }
  .el-table__row {
    background: #ffffff;
  }
}

.permission-select:deep(.el-select__wrapper) {
  box-shadow: none !important;
  // min-height: 0;
}

.select-member-content {
  flex: 1;
  min-width: 0;
  padding: 30px 24px 20px;
  :deep(.el-checkbox) {
    background-color: #fff;
  }
}

.precautions {
  // list-style: disc;
  padding: 12px 12px 12px 8px;
  margin-top: 8px;
  background: #f7f8fa;
  border-radius: 8px;
  color: #30343a;
  font-size: 16px;
  line-height: 28px;
}

:deep(.model-selector) {
  height: 40px;
}
</style>

<style lang="less">
.member-dialog {
  padding: 0;
  overflow: hidden;
  width: 100%;
  max-width: 1000px;
  min-width: 820px;
  header {
    padding-bottom: 0;
  }
}
</style>
