<script setup lang="ts">
import { Icon } from '@iconify/vue'
import KkviewPreview from './components/KkviewPreview.vue'
import { listByIds } from '@/api/system/oss'

const router = useRouter()
const route = useRoute()

const ossId = computed(() => route.params.ossId as string)

const loading = ref(true)
const error = ref('')
const fileList = ref<any[]>([])

// 获取文件列表
const fetchFiles = async () => {
  try {
    if (!ossId.value) {
      error.value = 'ossId不能为空'
      return
    }
    loading.value = true
    const res = await listByIds(ossId.value)
    if (res.code === 200) {
      fileList.value = res.data
    } else {
      error.value = '获取文件列表失败'
    }
  } catch (err) {
    error.value = '请求出错'
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchFiles()
})
</script>

<template>
  <div class="h-full flex flex-col">
    <div class="my-2 ml-2">
      <Icon
        class="size-[28px] text-[#4865E8] inline-block mr-[14px]"
        icon="famicons:arrow-back-circle-outline"
        @click="router.back()"
      />
      <span>预览文件 </span>
    </div>
    <div class="file-preview-container">
      <template v-if="fileList.length === 0 && !loading">
        <div class="no-files">没有文件可预览</div>
      </template>
      <template v-if="loading">
        <div class="loading">获取文件中...</div>
      </template>

      <template v-else-if="error">
        <div class="error">{{ error }}</div>
      </template>

      <template v-else>
        <template v-for="file in fileList" :key="file.id">
          <!-- <component
            :is="getPreviewComponent(file.fileSuffix)"
            v-if="getPreviewComponent(file.fileSuffix)"
            :file-url="file.url"
          /> -->
          <KkviewPreview :file-url="file.url" :default-pdf-mode="true" />
          <!-- <div v-else class="unsupported-file">不支持的文件类型: {{ file.fileName }}</div> -->
        </template>
      </template>
    </div>
  </div>
</template>

<style scoped>
.file-preview-container {
  width: 100%;
  height: 100%;
  max-width: 1200px;
  min-width: 700px;
  padding-bottom: 15px;
  margin-left: auto;
  margin-right: auto;
  flex: 1;
  min-height: 0;
  overflow-y: auto;
}

.loading,
.error,
.unsupported-file {
  padding: 20px;
  text-align: center;
}

.error {
  color: #f56c6c;
}

.unsupported-file {
  color: #e6a23c;
}
</style>
