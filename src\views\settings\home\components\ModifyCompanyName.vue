<script setup lang="ts">
defineOptions({
  name: 'ModifyCompanyName',
})

const props = defineProps<{
  editCompanyInfo: (n: string) => Promise<boolean>
  companyName: string
}>()

// 编辑企业名称相关
const nameDialogVisible = defineModel({ default: false })
const newName = ref('')
watchEffect(() => {
  newName.value = props.companyName
})
function handleDialogClose() {
  newName.value = props.companyName
}

// 提交更新企业名称
function submitUpdateName() {
  props.editCompanyInfo(newName.value).then((res: boolean) => {
    if (res) {
      nameDialogVisible.value = false
    }
  })
}
</script>

<template>
  <!-- 编辑企业名称对话框 -->
  <ElDialog
    v-model="nameDialogVisible"
    title="编辑"
    width="400px"
    append-to-body
    @close="handleDialogClose"
  >
    <ElForm>
      <ElFormItem label="企业名称">
        <ElInput v-model="newName" placeholder="请输入企业名称" />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <span class="dialog-footer">
        <ElButton @click="nameDialogVisible = false">取消</ElButton>
        <ElButton type="primary" @click="submitUpdateName">确定</ElButton>
      </span>
    </template>
  </ElDialog>
</template>
