import { get, post, put } from '../utils/request';

/** =====首页业务接口===== */

/**
 * 获取用户和助手统计信息
 * @method get
 * @path /biz/index/getUserAndAgentStat
 */
export function getHomeUserAndAgentStat<T>() {
  return get<T>({
    url: `/biz/index/getUserAndAgentStat`,
  });
}

/**
 * Token消耗数
 * @method get
 * @path /biz/index/getTokenCount/{type}
 */
export function getHomeTokenCount<T>(type: 1 | 2 | 3) {
  return get<T>({
    url: `/biz/index/getTokenCount/${type}`,
  });
}

/**
 * 最热大预言模型排行
 * @method get
 * @path /biz/index/getHotLLMModelRank
 */
export function getHomeHotLLMModelRank<T>() {
  return get<T>({
    url: `/biz/index/getHotLLMModelRank`,
  });
}

/**
 * 最热助手排行
 * @method get
 * @path /biz/index/getHotAgentRank
 */
export function getHomeHotAgentRank<T>() {
  return get<T>({
    url: `/biz/index/getHotAgentRank`,
  });
}

/**
 * 对话次数
 * @method get
 * @path /biz/index/getChatCount/{type}
 */
export function getHomeChatCount<T>(type: 1 | 2 | 3) {
  return get<T>({
    url: `/biz/index/getChatCount/${type}`,
  });
}

/**
 * 更新租户名称
 * @method put
 * @path /system/index/updateTenant
 */
export function putHomeUpdateTenant<T>(data: { tenantName: string }) {
  return put<T>({
    url: `/system/index/updateTenant?tenantName=${data.tenantName}`
  });
}

/**
 * 更新租户头像
 * @method post
 * @path /system/index/tenant/avatar
 */
export function postHomeTenantAvatar<T>(data: FormData) {
  return post<T>({
    url: `/system/index/tenant/avatar`,
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 获取租户信息
 * @method get
 * @path /system/index/getTenant
 */
export function getHomeTenantInfo<T>() {
  return get<T>({
    url: `/system/index/getTenant`,
  });
}
