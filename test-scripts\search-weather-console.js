const { chromium } = require('playwright')

;(async () => {
  // 启动浏览器
  console.log('启动浏览器...')
  const browser = await chromium.launch({
    headless: false, // 设置为非无头模式，以便可以看到浏览器界面
    devtools: true, // 自动打开开发者工具
  })

  // 创建新页面
  console.log('创建新页面...')
  const page = await browser.newPage()

  // 设置页面控制台消息监听器
  page.on('console', msg => {
    console.log(`浏览器控制台[${msg.type()}]: ${msg.text()}`)
  })

  // 导航到百度
  console.log('正在打开百度页面...')
  await page.goto('https://www.baidu.com')

  console.log('已成功打开百度页面！')

  // 在搜索框中输入"今天天气"
  console.log('在搜索框中输入"今天天气"')
  await page.fill('#kw', '今天天气')

  // 点击搜索按钮
  console.log('点击搜索按钮')
  await page.click('#su')

  // 等待搜索结果加载
  console.log('等待搜索结果加载...')
  await page.waitForSelector('#content_left')

  console.log('搜索完成，显示天气信息')

  // 执行JavaScript代码，生成一些控制台消息
  await page.evaluate(() => {
    console.log('这是通过Playwright注入的脚本生成的日志')
    console.warn('这是一条警告消息')
    console.error('这是一条错误消息')
    console.info('这是一条信息消息')
  })

  // 获取并显示所有控制台日志
  console.log('获取页面控制台日志...')
  const logs = await page.evaluate(() => {
    // 在浏览器环境中无法直接访问之前的控制台日志，但可以尝试再次生成一些日志
    console.log('获取页面信息...')
    return {
      title: document.title,
      url: location.href,
      time: new Date().toLocaleString(),
    }
  })

  console.log('页面信息:', logs)

  // 等待用户手动关闭浏览器
  console.log('浏览器将保持打开状态，请手动关闭或按Ctrl+C终止程序')
  console.log('提示：您可以在浏览器中按F12或右键检查，查看更多控制台消息')
})().catch(err => {
  console.error('发生错误:', err)
  process.exit(1)
})
