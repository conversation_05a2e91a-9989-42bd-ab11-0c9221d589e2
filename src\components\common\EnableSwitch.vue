<template>
  <el-switch v-model="internalEnabled" :loading="loading" @change="handleChange" />
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'

type SwitchFunc = () => Promise<any>

const props = defineProps<{
  handleFuncPromise: SwitchFunc
}>()

const internalEnabled = defineModel<boolean>({ default: false })

const loading = ref(false)

const handleChange = async (value: string | number | boolean) => {
  loading.value = true
  const enabled = !!value
  try {
    const res = await props.handleFuncPromise()
    if (res.code === 200) {
      // emit('update:enabled', enabled) // 接口成功返回后，触发 update:enabled 事件
    } else {
      // ElMessage.error(`启用状态更新失败: ${res.msg}`)
      internalEnabled.value = !enabled // 失败后，回滚状态
    }
  } catch (error: any) {
    // ElMessage.error(`启用状态更新失败: ${error.message}`)
    internalEnabled.value = !enabled // 失败后，回滚状态
  } finally {
    loading.value = false
  }
}
</script>
