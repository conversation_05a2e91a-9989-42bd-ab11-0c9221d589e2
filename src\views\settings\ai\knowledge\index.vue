<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, Sort } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import Pagination from '@/components/common/Pagination/index.vue'
import { getBizKbAllList, postBizKbEnable, postBizKbRemove } from '@/api/knowledge-base'
import { formatFileSize } from '@/utils/formatter'
import type { AIModelVo } from '@/api'
import { getBizAiModelAvailableModels } from '@/api'
import EnableSwitch from '@/components/common/EnableSwitch.vue'
import '@/typings/knowledge-base.d.ts'

const router = useRouter()
const loading = ref(false)

const tableData = ref<KnowledgeBase.KnowledgeBaseVo[]>([])
const queryFormRef = ref<FormInstance>()
const total = ref(0)

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  title: '',
  orderByColumn: '',
  isAsc: 'asc',
})

const getKnowledgeList = () => {
  loading.value = true
  getBizKbAllList<{ rows: KnowledgeBase.KnowledgeBaseVo[]; total: number }>({
    pageNum: queryParams.value.pageNum,
    pageSize: queryParams.value.pageSize,
    name: queryParams.value.title,
    orderByColumn: queryParams.value.orderByColumn,
    isAsc: queryParams.value.isAsc,
  })
    .then((res: { rows: KnowledgeBase.KnowledgeBaseVo[]; total: number }) => {
      tableData.value = res.rows || []
      total.value = res.total || 0
      loading.value = false
    })
    .catch(() => {
      loading.value = false
    })
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1
  getKnowledgeList()
}

const handleLookUp = (kbId: number | string) => {
  router.push({
    name: 'settings-dataset',
    params: {
      kbId,
    },
  })
}

const handleDelete = (row: KnowledgeBase.KnowledgeBaseVo) => {
  ElMessageBox.confirm('确认删除该知识吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    if (row.id !== undefined) {
      postBizKbRemove(row.id)
        .then(res => {
          if (res.code === 200) {
            ElMessage.success('删除成功')
            getKnowledgeList()
          } else {
            ElMessage.error(`删除失败: ${res.msg}`)
          }
        })
        .catch((error: any) => {
          ElMessage.error(`删除失败: ${error.message}`)
        })
    } else {
      ElMessage.error('知识库 ID 未定义')
    }
  })
}

const models = ref<AIModelVo[]>([])

onMounted(() => {
  getKnowledgeList()
  getBizAiModelAvailableModels({
    type: 'embedding',
  }).then(res => {
    if (res.code === 200) {
      models.value = res.data || []
    }
  })
})

/**
 * 编辑时间排序
 */
function sortChange({ prop, order }: Sort) {
  let isAsc = ''
  if (order === 'ascending') {
    isAsc = 'asc'
  } else if (order === 'descending') {
    isAsc = 'desc'
  }

  queryParams.value.pageNum = 1
  queryParams.value.orderByColumn = prop
  queryParams.value.isAsc = isAsc
  getKnowledgeList()
}

function handleEnable(id: number) {
  return postBizKbEnable(id)
}
</script>

<template>
  <div class="my-container">
    <div class="flex justify-end">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="text-right">
        <el-form-item label="" prop="title" class="!mr-0">
          <el-input
            v-model="queryParams.title"
            style="width: 400px"
            placeholder="请输入你需要搜索的内容"
            clearable
            @clear="handleQuery"
            @keyup.enter="handleQuery"
          >
            <template #suffix>
              <el-icon>
                <Search />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>
      </el-form>
    </div>

    <el-table v-loading="loading" :data="tableData" style="width: 100%" @sort-change="sortChange">
      <el-table-column type="index" label="序号" width="60" />
      <el-table-column prop="name" label="知识" min-width="200" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="flex items-center cursor-pointer" @click="handleLookUp(row.id)">
            <img
              class="inline-block w-[18px] h-[18px]"
              src="@/assets/knowledge/text-icon.png"
              alt=""
            />
            <div class="flex-1 ml-1 overflow-hidden whitespace-nowrap min-w-0 text-ellipsis">
              {{ row.name }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="type" label="类型" width="120">
        <template #default="{ row }">
          <span v-if="row.type === 1">文本</span>
          <span v-if="row.type === 2">图像</span>
        </template>
      </el-table-column>
      <el-table-column prop="size" label="文件大小" width="120">
        <template #default="{ row }">
          {{ formatFileSize(row.size) }}
        </template>
      </el-table-column>
      <el-table-column prop="userName" label="拥有者" width="120" />
      <el-table-column prop="shareStatus" label="共享" width="120">
        <!-- 分享状态 0-私人 1-全体共享 2-指定部门共享 3-指定成员共享 -->
        <template #default="{ row }">
          <span v-if="row.shareStatus === 0">私人</span>
          <span v-if="row.shareStatus === 1">全体共享</span>
          <span v-if="row.shareStatus === 2">指定部门共享</span>
          <span v-if="row.shareStatus === 3">指定成员共享</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="editTime"
        label="编辑时间"
        width="180"
        sortable
        :sort-orders="['ascending', 'descending']"
      />
      <el-table-column prop="enabled" label="启用" width="90">
        <template #default="{ row }">
          <EnableSwitch v-model="row.enabled" :handle-func-promise="() => handleEnable(row.id)" />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180" fixed="right">
        <template #default="{ row }">
          <el-button link type="primary" @click="handleLookUp(row.id)"> 查看 </el-button>
          <el-button link type="danger" @click="handleDelete(row)"> 删除 </el-button>
        </template>
      </el-table-column>
    </el-table>

    <Pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getKnowledgeList"
    />
  </div>
</template>

<style scoped lang="less">
.my-container {
  padding: 20px;
}

:deep(.el-table) {
  --el-table-header-bg-color: #f7f8fa;
}
</style>
