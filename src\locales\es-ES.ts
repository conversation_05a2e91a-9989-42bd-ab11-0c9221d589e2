export default {
  common: {
    add: 'Agregar',
    addSuccess: 'Agregado con éxito',
    edit: 'Editar',
    editSuccess: 'Edición exitosa',
    delete: 'Borrar',
    deleteSuccess: 'Bo<PERSON><PERSON> con éxito',
    save: 'Guardar',
    saveSuccess: 'Guardado con éxito',
    reset: 'Reiniciar',
    action: 'Acción',
    export: 'Exportar',
    exportSuccess: 'Exportación exitosa',
    import: 'Importar',
    importSuccess: 'Importación exitosa',
    clear: 'Limpiar',
    clearSuccess: 'Limpieza exitosa',
    yes: 'Sí',
    no: 'No',
    confirm: 'Confirmar',
    download: 'Descargar',
    noData: 'Sin datos',
    wrong: 'Algo salió mal, inténtalo de nuevo más tarde.',
    success: 'Exitoso',
    failed: 'Fallido',
    verify: 'Verificar',
    unauthorizedTips: 'No autorizado, por favor verifique primero.',
    stopResponding: 'No responde',
  },
  chat: {
    newChatButton: 'Nueva conversación',
    newChatTitle: 'Nueva conversación',
    placeholder: 'Pregúntame lo que sea...(Shift + Enter = salto de línea, "/" para activar avisos)',
    placeholderMobile: 'Pregúntame lo que sea...',
    copy: 'Copiar',
    copied: 'Copiado',
    copyCode: 'Copiar código',
    copyFailed: 'Copia fallida',
    clearChat: 'Limpiar chat',
    clearChatConfirm: '¿Estás seguro de borrar este chat?',
    exportImage: 'Exportar imagen',
    exportImageConfirm: '¿Estás seguro de exportar este chat a png?',
    exportSuccess: 'Exportación exitosa',
    exportFailed: 'Exportación fallida',
    usingContext: 'Modo de contexto',
    turnOnContext: 'En el modo actual, el envío de mensajes llevará registros de chat anteriores.',
    turnOffContext: 'En el modo actual, el envío de mensajes no incluirá registros de conversaciones anteriores.',
    deleteMessage: 'Borrar mensaje',
    deleteMessageConfirm: '¿Estás seguro de eliminar este mensaje?',
    deleteHistoryConfirm: '¿Estás seguro de borrar esta historia?',
    clearHistoryConfirm: '¿Estás seguro de borrar el historial de chat?',
    preview: 'Avance',
    showRawText: 'Mostrar como texto sin formato',
  },
  setting: {
    setting: 'Configuración',
    general: 'General',
    advanced: 'Avanzado',
    config: 'Configurar',
    avatarLink: 'Enlace de avatar',
    name: 'Nombre',
    description: 'Descripción',
    role: 'Rol',
    temperature: 'Temperatura',
    top_p: 'Top_p',
    resetUserInfo: 'Restablecer información de usuario',
    chatHistory: 'Historial de chat',
    theme: 'Tema',
    language: 'Idioma',
    api: 'API',
    reverseProxy: 'Reverse Proxy',
    timeout: 'Tiempo de espera',
    socks: 'Socks',
    httpsProxy: 'HTTPS Proxy',
    balance: 'Saldo de API',
    monthlyUsage: 'Uso mensual de API',
    openSource: 'Este proyecto es de código abierto en',
    freeMIT: 'gratis y basado en la licencia MIT, ¡sin ningún tipo de comportamiento de pago!',
    stars: 'Si encuentras este proyecto útil, por favor dame una Estrella en GitHub o da un pequeño patrocinio, ¡gracias!',
  },
  store: {
    siderButton: 'Tienda rápida',
    local: 'Local',
    online: 'En línea',
    title: 'Título',
    description: 'Descripción',
    clearStoreConfirm: '¿Estás seguro de borrar los datos?',
    importPlaceholder: 'Pegue los datos JSON aquí',
    addRepeatTitleTips: 'Título duplicado, vuelva a ingresar',
    addRepeatContentTips: 'Contenido duplicado: {msg}, por favor vuelva a entrar',
    editRepeatTitleTips: 'Conflicto de título, revíselo',
    editRepeatContentTips: 'Conflicto de contenido {msg} , por favor vuelva a modificar',
    importError: 'Discrepancia de valor clave',
    importRepeatTitle: 'Título saltado repetidamente: {msg}',
    importRepeatContent: 'El contenido se salta repetidamente: {msg}',
    onlineImportWarning: 'Nota: ¡Compruebe la fuente del archivo JSON!',
    downloadError: 'Verifique el estado de la red y la validez del archivo JSON',
  },
}
