/**
 * Markdown 和 LaTeX 公式处理工具函数
 * 主要用于处理文本中的特殊符号和数学公式，使其能够正确渲染
 */

/**
 * 转义文本中跟在数字前的 '$' 符号
 * 主要解决以下问题：
 * - 防止 KaTeX 将例如 '$100' 这样的文本误解析为数学公式
 * - 保证金额等常见的数字前美元符号能够正确显示
 *
 * @example
 * // 返回 "\\$100 is the price"
 * escapeNumericDollar("$100 is the price");
 *
 * @param {string} text - 输入的原始字符串
 * @returns {string} - 处理后的字符串，数字前的 $ 被转义为 \$
 */
export const escapeNumericDollar = (text: string): string => {
  let escapedText = ''
  for (let i = 0; i < text.length; i++) {
    let char = text[i]
    const nextChar = text[i + 1] || ' '
    if (char === '$' && nextChar >= '0' && nextChar <= '9') char = '\\$'
    escapedText += char
  }
  return escapedText
}

/**
 * 将 LaTeX 数学公式的转义方括号和圆括号转换为 KaTeX 可识别的美元符号定界符
 * 处理两种常见的 LaTeX 语法：
 * - \[...\] → $$...$$ (块级公式/行间公式)
 * - \(...\) → $...$ (行内公式)
 *
 * 同时具有智能检测功能，会跳过代码块中的内容，防止错误转换
 *
 * @example
 * // 返回 "Formula: $$x^2 + y^2 = z^2$$"
 * convertLatexNotation("Formula: \\[x^2 + y^2 = z^2\\]");
 *
 * @param {string} text - 输入的 Markdown 字符串
 * @returns {string} - 转换后的字符串，LaTeX 公式符号被替换为 KaTeX 可识别的格式
 */
export const convertLatexNotation = (text: string): string => {
  const pattern = /(```[\s\S]*?```|`.*?`)|\\\[([\s\S]*?[^\\])\\\]|\\\((.*?)\\\)/g
  return text.replace(pattern, (match, codeBlock, squareBracket, roundBracket) => {
    if (codeBlock) return codeBlock
    else if (squareBracket) return `$$${squareBracket}$$`
    else if (roundBracket) return `$${roundBracket}$`
    return match
  })
}

// 用于标记当前处理的环境是否为流式响应
let isStreamResponseMode = false

/**
 * 设置当前环境是否为流式响应模式
 * @param {boolean} isStream - 是否是流式响应状态
 */
export const setStreamResponseMode = (isStream: boolean): void => {
  isStreamResponseMode = isStream
}

/**
 * 防抖函数
 * @param {Function} fn - 需要防抖的函数
 * @param {number} delay - 延迟时间，默认5000ms
 * @returns {Function} - 防抖处理后的函数
 */
const debounce = (fn: Function, delay = 5000) => {
  let timer: number | null = null
  return (...args: any[]) => {
    if (timer) clearTimeout(timer)
    timer = window.setTimeout(() => {
      fn(...args)
      timer = null
    }, delay)
  }
}

/**
 * 查找并记录文本处理前后的差异
 * @param {string} before - 处理前的文本
 * @param {string} after - 处理后的文本
 * @returns {Array} - 差异数组，格式为 [{be: 变化前, af: 变化后}, {be: 变化前, af: 变化后}, ...]
 */
const findTextDifferences = (before: string, after: string): { be: string; af: string }[] => {
  // 如果处理前后完全相同，则返回空数组
  if (before === after) return []

  const differences: { be: string; af: string }[] = []

  // 检查美元符号相关的变化
  const dollarPattern = /\$\d+/g
  let dollarMatch: RegExpExecArray | null = dollarPattern.exec(before)

  while (dollarMatch) {
    const originalText = dollarMatch[0]
    const escapedText = `\\${originalText}`
    if (after.includes(escapedText) && !after.includes(originalText)) {
      differences.push({ be: originalText, af: escapedText })
    }
    dollarMatch = dollarPattern.exec(before)
  }

  // 检查 LaTeX 符号相关的变化
  const latexPatterns = [
    { pattern: /\\\[([\s\S]*?[^\\])\\\]/g, replacement: (m: string, p1: string) => `$$${p1}$$` },
    { pattern: /\\\((.*?)\\\)/g, replacement: (m: string, p1: string) => `$${p1}$` },
  ]

  latexPatterns.forEach(({ pattern, replacement }) => {
    let match: RegExpExecArray | null = pattern.exec(before)
    while (match) {
      const originalText = match[0]
      const replacedText = replacement(originalText, match[1])
      if (after.includes(replacedText)) {
        differences.push({ be: originalText, af: replacedText })
      }
      match = pattern.exec(before)
    }
  })

  return differences
}

/**
 * 记录Markdown处理日志到sessionStorage
 */
const logMarkdownProcess = debounce((before: string, after: string) => {
  try {
    // resolved markdown_debug_difference记录差异，记录为[{be:变化前,af:变化后},{be:变化前,af:变化后},{be:变化前,af:变化后},...]这样的形式
    sessionStorage.setItem('prepare_text_before', before)
    sessionStorage.setItem('prepare_text_after', after)

    // 查找并记录差异
    const differences = findTextDifferences(before, after)
    sessionStorage.setItem('prepare_text_difference', JSON.stringify(differences))

    sessionStorage.setItem('prepare_text_timestamp', new Date().toISOString())
  } catch (error) {
    console.error('存储Markdown调试信息失败:', error)
  }
}, 500)

/**
 * 预处理 Markdown 文本，执行所有需要的转换操作
 * 是各个转换函数的组合调用，按照最优处理顺序执行
 *
 * 处理流程：
 * 1. 通过 escapeNumericDollar 处理数字前的美元符号
 * 2. 通过 convertLatexNotation 转换 LaTeX 公式标记
 *
 * @param {string} text - 输入的原始 Markdown 字符串
 * @returns {string} - 预处理完成的字符串，准备好用于 markdown-it 渲染
 */
export const prepareMarkdownText = (text: string): string => {
  // resolved 记录到sessionStorage作为日志，方便调试，考虑到在流式过程中持续触发，可以防抖后执行最后一次完整数据
  const result = convertLatexNotation(escapeNumericDollar(text))
  // 记录处理前后的文本到sessionStorage，使用防抖避免频繁写入
  // resolved 要求只在streamResponse时候触发，加载历史记录时候不触发
  if (isStreamResponseMode) {
    logMarkdownProcess(text, result)
  }
  return result
}
