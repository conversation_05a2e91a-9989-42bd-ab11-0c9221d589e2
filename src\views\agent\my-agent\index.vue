<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue'
import {
  CircleCheckFilled,
  CircleCloseFilled,
  Delete,
  DocumentCopy,
  Edit,
  Link,
  Lock,
  MoreFilled,
  Search,
  Timer,
} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import AgentBaseDialog from './components/AgentBaseDialog.vue'
import { VERSION_STATUS_MAIN } from '@/utils/constants'
import type { Agent } from '@/typings/agent'
import {
  getBizAgentMyAgentList,
  postBizAgentDuplicate,
  postBizAgentRemoveAgentId,
  postBizAgentTurnToPrivateId,
} from '@/api/agent'

const isDev = import.meta.env.MODE === 'development'
const router = useRouter()

// 查询参数
// resolved 分页加载，要求滚动到底部时候加载
const queryParams = ref({
  pageNum: 1,
  pageSize: 48, // 增加每页数量，确保大屏幕也有足够内容
  keyword: '',
})

const queryFormRef = ref(null)
const loading = ref(false)
const list = ref<Agent.AgentVo[]>([])
const hasMore = ref(true)
const isLoading = ref(false) // 底部加载状态
const firstLoading = ref(true) // 首次加载状态

/** 获取列表数据 */
const getList = async (refresh = false) => {
  if (isLoading.value) return
  // todo 这个refresh逻辑需要吗？怎么优化呢？
  if (refresh) {
    list.value = []
    queryParams.value.pageNum = 1
  }
  isLoading.value = true
  try {
    const res = await getBizAgentMyAgentList<{ rows: Agent.AgentVo[] }>(queryParams.value)
    if (res.code === 200) {
      if (queryParams.value.pageNum === 1) {
        list.value = res.rows
      } else {
        list.value = [...list.value, ...res.rows]
      }
      // 判断是否还有更多数据
      hasMore.value = res.rows.length === queryParams.value.pageSize
    }
  } finally {
    isLoading.value = false
    firstLoading.value = false
  }
}

// 加载更多数据
const loadMore = () => {
  console.log('loadMore called') // 添加日志确认是否触发
  if (isLoading.value || !hasMore.value) return
  queryParams.value.pageNum++
  getList()
}

onMounted(() => {
  getList()
})

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1
  hasMore.value = true
  firstLoading.value = true
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  queryParams.value.pageNum = 1
  hasMore.value = true
  firstLoading.value = true
  handleQuery()
}

const handleCreate = () => {
  // 跳转到新增助手页面
  router.push('/agent/agent-operation')
}

const dialogVisible = ref(false)
const currentAgent = ref<Agent.AgentVo | null>(null)

const handleCardClick = (item: Agent.AgentVo) => {
  currentAgent.value = item
  dialogVisible.value = true
}

const handleStartChat = () => {
  // TODO: 实现开始对话的逻辑
  dialogVisible.value = false
}

const handleEditAgent = (item: Agent.AgentVo) => {
  // resolved: 实现编辑的逻辑
  if (item.id) {
    router.push({
      path: '/agent/agent-operation',
      query: {
        id: item.id,
      },
    })
  }
  dialogVisible.value = false
}

const handleDuplicate = async (item: Agent.AgentVo) => {
  try {
    const res = await postBizAgentDuplicate<API.Response<null>>({ id: item.id })
    if (res.code === 200) {
      ElMessage.success('创建副本成功')
      getList(true)
    }
  } catch (error) {
    console.error('创建副本失败:', error)
  }
}

const handleDelete = async (item: Agent.AgentVo) => {
  try {
    // 测试环境不需要确认，直接删除
    if (import.meta.env.MODE !== 'production') {
      const res = await postBizAgentRemoveAgentId<API.Response<null>>({ id: item.id })
      if (res.code === 200) {
        ElMessage.success('删除成功')
        // 直接从列表中移除被删除的项
        list.value = list.value.filter(agent => agent.id !== item.id)
      }
      return
    }

    // 生产环境需要确认
    await ElMessageBox.confirm('确定要删除该助手吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    const res = await postBizAgentRemoveAgentId<API.Response<null>>({ id: item.id })
    if (res.code === 200) {
      ElMessage.success('删除成功')
      // 直接从列表中移除被删除的项
      list.value = list.value.filter(agent => agent.id !== item.id)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
    }
  }
}

const handleSetPrivate = async (item: Agent.AgentVo) => {
  try {
    await ElMessageBox.confirm('确定要将该助手转为私有吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    const res = await postBizAgentTurnToPrivateId<API.Response<null>>({ id: item.id })
    if (res.code === 200) {
      ElMessage.success('转为私有成功')
      // 只更新当前项的状态，不重新加载整个列表
      const index = list.value.findIndex(agent => agent.id === item.id)
      if (index !== -1) {
        list.value[index].status = -1 // 私有状态
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('转为私有失败:', error)
    }
  }
}

// todo 生成助手公开发布链接
const handleGeneratePublicLink = (item: Agent.AgentVo) => {
  if (!item.id) {
    ElMessage.error('助手ID不存在')
    return
  }

  // 生成公开链接
  const baseUrl = window.location.origin
  const publicUrl = `${baseUrl}/#/share/${item.id}`

  // 复制到剪贴板
  navigator.clipboard
    .writeText(publicUrl)
    .then(() => {
      ElMessage.success('公开链接已复制到剪贴板')
    })
    .catch(() => {
      // 降级方案：使用传统方法复制
      const textArea = document.createElement('textarea')
      textArea.value = publicUrl
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      ElMessage.success('公开链接已复制到剪贴板')
    })
}

const getStatusClass = (status: number) => {
  switch (status) {
    case 1:
      return 'success'
    case 0:
      return 'pending'
    case 2:
      return 'failed'
    case -1:
      return 'private'
    default:
      return ''
  }
}
</script>

<template>
  <div class="my-container">
    <div class="flex justify-between">
      <div class="flex">
        <!-- resolved 在win平台没问题，在mac平台鼠标移到button上时候没有cursor样式、也没有正确的hover变化 -->
        <!-- resolved 不使用icon="Plus"，使用<i class="iconfont iconfont-c icon-xinzeng"></i> -->
        <el-button class="ex-el-button-gray" type="primary" plain @click="handleCreate()">
          <i class="iconfont iconfont-c icon-xinzeng mr-2"></i>
          新增
        </el-button>
      </div>
      <div class="flex justify-end">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="text-right">
          <el-form-item label="" prop="keyword" class="!mr-0">
            <el-input
              v-model="queryParams.keyword"
              style="width: 400px"
              placeholder="请输入你需要搜索的内容"
              clearable
              @clear="handleQuery"
              @keyup.enter="handleQuery"
            >
              <template #suffix>
                <el-icon>
                  <Search />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>
        </el-form>
        <!-- <el-button class="ml-3" type="primary" plain @click="handleQuery"> 高级搜索 </el-button> -->
      </div>
    </div>
    <!-- resolved 滚动加载，在小屏幕下成功运行，在大屏幕下未触发，是不是因为小屏幕一行放四个item，大屏幕一行放八个item -->
    <!-- <div>isLoading || !hasMore-{{ isLoading || !hasMore }}</div> -->
    <div
      v-infinite-scroll="loadMore"
      class="agent-list"
      :infinite-scroll-disabled="isLoading || !hasMore"
      :infinite-scroll-distance="30"
      :infinite-scroll-immediate="false"
      style="overflow: auto"
    >
      <!-- 首次加载时显示骨架屏 -->
      <div v-if="firstLoading" class="card-list">
        <div v-for="i in 8" :key="i" class="card">
          <el-skeleton animated>
            <template #template>
              <div class="card-header">
                <el-skeleton-item variant="circle" style="width: 50px; height: 50px" />
                <div class="info" style="width: 100%; margin-left: 12px">
                  <el-skeleton-item variant="text" style="width: 70%" />
                  <el-skeleton-item variant="text" style="width: 40%; margin-top: 8px" />
                </div>
              </div>
              <div class="card-content">
                <el-skeleton-item variant="p" style="width: 100%; height: 75px" />
              </div>
              <div class="card-footer">
                <el-skeleton-item variant="text" style="width: 30%" />
              </div>
            </template>
          </el-skeleton>
        </div>
      </div>

      <!-- 数据加载后显示实际内容 -->
      <div v-else class="card-list">
        <div v-for="item in list" :key="item.id" class="card" @click="handleCardClick(item)">
          <div class="card-header">
            <div
              class="emoji-wrap !w-[56px] !h-[56px]"
              :style="{ backgroundColor: item.backgroundColor }"
            >
              <img v-if="item.emoji" :src="item.emoji" class="emoji" />
            </div>
            <div class="info !h-[56px]">
              <div class="name">
                {{ item.name }}
              </div>
              <div class="date flex items-center">
                <div class="max-w-[100px] ell">
                  {{ item.createUserName }}
                </div>
                <div class="ml-1">|</div>
                <div class="max-w-[120px] ell ml-1">{{ item.createTime }}</div>
              </div>
            </div>
          </div>
          <div class="card-content">
            {{ item.description }}
          </div>
          <div class="card-footer">
            <div class="status" :class="getStatusClass(item.status)">
              <el-icon v-if="item.status === 1">
                <CircleCheckFilled />
              </el-icon>
              <el-icon v-else-if="item.status === 0">
                <Timer />
              </el-icon>
              <el-icon v-else-if="item.status === 2">
                <CircleCloseFilled />
              </el-icon>
              <el-icon v-else-if="item.status === -1">
                <Lock />
              </el-icon>
              {{ VERSION_STATUS_MAIN.find(s => s.value === item.status)?.name }}
            </div>
            <div class="actions" @click.stop>
              <!-- resolved 点击此处的时候不要冒泡触发handleCardClick -->
              <el-dropdown>
                <span class="el-dropdown-link">
                  <el-icon><MoreFilled /></el-icon>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <!-- resolved 生成助手公开发布链接 -->
                    <template v-if="isDev && [1, -1].includes(item.status)">
                      <el-dropdown-item @click="handleGeneratePublicLink(item)">
                        <el-icon><Link /></el-icon>
                        生成公开链接
                      </el-dropdown-item>
                    </template>
                    <template v-if="[0, 1, 2].includes(item.status)">
                      <el-dropdown-item @click="handleSetPrivate(item)">
                        <el-icon><Lock /></el-icon>
                        转为私有
                      </el-dropdown-item>
                    </template>
                    <!-- resolved 调用postBizAgentDuplicate -->
                    <el-dropdown-item @click="handleDuplicate(item)">
                      <el-icon><DocumentCopy /></el-icon>
                      创建副本
                    </el-dropdown-item>
                    <el-dropdown-item @click="handleEditAgent(item)">
                      <el-icon><Edit /></el-icon>
                      编辑
                    </el-dropdown-item>
                    <!-- resolved 调用postBizAgentRemoveAgentId -->
                    <el-dropdown-item @click="handleDelete(item)">
                      <el-icon style="color: #f25b37">
                        <Delete />
                      </el-icon>
                      <span style="color: #f25b37">删除</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部加载状态 -->
      <div v-if="isLoading && !firstLoading" class="loading-more">
        <el-icon class="loading-icon"
          ><svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" class="loading">
            <path
              fill="currentColor"
              d="M512 64a32 32 0 0 1 32 32v192a32 32 0 0 1-64 0V96a32 32 0 0 1 32-32zm0 640a32 32 0 0 1 32 32v192a32 32 0 1 1-64 0V736a32 32 0 0 1 32-32zm448-192a32 32 0 0 1-32 32H736a32 32 0 1 1 0-64h192a32 32 0 0 1 32 32zm-640 0a32 32 0 0 1-32 32H96a32 32 0 0 1 0-64h192a32 32 0 0 1 32 32zM195.2 195.2a32 32 0 0 1 45.248 0L376.32 331.008a32 32 0 0 1-45.248 45.248L195.2 240.448a32 32 0 0 1 0-45.248zm452.544 452.544a32 32 0 0 1 45.248 0L828.8 783.552a32 32 0 0 1-45.248 45.248L647.744 692.992a32 32 0 0 1 0-45.248zM828.8 195.264a32 32 0 0 1 0 45.184L692.992 376.32a32 32 0 0 1-45.248-45.248l135.808-135.808a32 32 0 0 1 45.248 0zm-452.544 452.48a32 32 0 0 1 0 45.248L240.448 828.8a32 32 0 0 1-45.248-45.248l135.808-135.808a32 32 0 0 1 45.248 0z"
            ></path></svg
        ></el-icon>
        <span>加载中...</span>
      </div>

      <!-- 没有更多数据提示 -->
      <div v-if="!hasMore && !firstLoading && list.length > 0" class="no-more">
        <span>没有更多数据了</span>
      </div>

      <!-- 空数据提示 -->
      <div v-if="!firstLoading && list.length === 0" class="empty-data">
        <el-empty description="暂无数据" />
      </div>
    </div>

    <AgentBaseDialog
      v-model="dialogVisible"
      :agent="currentAgent"
      @edit="handleEditAgent"
      @start-chat="handleStartChat"
    />
  </div>
</template>

<style lang="less" scoped>
@import '@/styles/variables.less';

.my-container {
  padding: 20px;
}
.agent-list {
  height: calc(100vh - 170px);
  padding-bottom: 30px;
  overflow-y: auto;
  padding-right: 10px;
  .card-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;

    .card {
      background: #fff;
      border-radius: 8px;
      padding: 16px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      cursor: pointer;
      border: 2px solid transparent;
      &:hover {
        border: 2px solid rgba(77, 91, 236, 0.65);
      }

      .card-header {
        display: flex;
        align-items: flex-start;
        margin-bottom: 12px;

        .info {
          margin-left: 12px;
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .name {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 4px;
          }

          .date {
            font-size: 12px;
            color: #999;
          }
        }

        .actions {
          .el-dropdown-link {
            cursor: pointer;
            color: #999;
          }
        }
      }

      .card-content {
        line-height: 1.5;
        margin-bottom: 12px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        overflow: hidden;
        // width: 303px;
        height: 75px;
        background: #f5f7fb;
        border-radius: 4px;
        color: #646a73;
        padding: 8px;
        font-size: 14px;
      }

      .card-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .status {
          display: inline-flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          padding: 2px 8px;
          border-radius: 4px;

          &.success {
            color: #67c23a;
            background: rgba(103, 194, 58, 0.1);
          }

          &.pending {
            color: #e6a23c;
            background: rgba(230, 162, 60, 0.1);
          }

          &.failed {
            color: #f56c6c;
            background: rgba(245, 108, 108, 0.1);
          }

          &.private {
            color: @c;
            background: fade(@c, 10%);
          }
        }
      }
    }
  }

  .loading-more,
  .no-more,
  .empty-data {
    text-align: center;
    padding: 20px 0;
    color: #909399;
    font-size: 14px;
  }

  .loading-more {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;

    .loading-icon {
      animation: rotating 2s linear infinite;
    }
  }
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.el-dropdown-link {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  &:hover {
    background: rgba(230, 234, 244, 0.6);
    border-radius: 4px;
  }
}
</style>
