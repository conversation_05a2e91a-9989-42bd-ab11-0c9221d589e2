export default {
  common: {
    add: 'Добавить',
    addSuccess: 'Добавлено успешно',
    edit: 'Редактировать',
    editSuccess: 'Изменено успешно',
    delete: 'Удалить',
    deleteSuccess: 'Удалено успешно',
    save: 'Сохранить',
    saveSuccess: 'Сохранено успешно',
    reset: 'Сбросить',
    action: 'Действие',
    export: 'Экспортировать',
    exportSuccess: 'Экспорт выполнен успешно',
    import: 'Импортировать',
    importSuccess: 'Импорт выполнен успешно',
    clear: 'Очистить',
    clearSuccess: 'Очищено успешно',
    yes: 'Да',
    no: 'Нет',
    confirm: 'Подтвердить',
    download: 'Загрузить',
    noData: 'Нет данных',
    wrong: 'Что-то пошло не так, пожалуйста, повторите попытку позже.',
    success: 'Успех',
    failed: 'Не удалось',
    verify: 'Проверить',
    unauthorizedTips: 'Не авторизован, сначала подтвердите свою личность.',
    stopResponding: 'Прекращение отклика',
  },
  chat: {
    newChatButton: 'Новый чат',
    newChatTitle: 'Новый чат',
    placeholder: 'Спросите меня о чем-нибудь ... (Shift + Enter = перенос строки, "/" для вызова подсказок)',
    placeholderMobile: 'Спросите меня о чем-нибудь ...',
    copy: 'Копировать',
    copied: 'Скопировано',
    copyCode: 'Копировать код',
    copyFailed: 'Не удалось скопировать',
    clearChat: 'Очистить чат',
    clearChatConfirm: 'Вы уверены, что хотите очистить этот чат?',
    exportImage: 'Экспорт в изображение',
    exportImageConfirm: 'Вы уверены, что хотите экспортировать этот чат в формате PNG?',
    exportSuccess: 'Экспортировано успешно',
    exportFailed: 'Не удалось выполнить экспорт',
    usingContext: 'Режим контекста',
    turnOnContext: 'В текущем режиме отправка сообщений будет включать предыдущие записи чата.',
    turnOffContext: 'В текущем режиме отправка сообщений не будет включать предыдущие записи чата.',
    deleteMessage: 'Удалить сообщение',
    deleteMessageConfirm: 'Вы уверены, что хотите удалить это сообщение?',
    deleteHistoryConfirm: 'Вы уверены, что хотите очистить эту историю?',
    clearHistoryConfirm: 'Вы уверены, что хотите очистить историю чата?',
    preview: 'Предварительный просмотр',
    showRawText: 'Показать как обычный текст',
    thinking: 'Думаю...',
  },
  setting: {
    setting: 'Настройки',
    general: 'Общее',
    advanced: 'Дополнительно',
    config: 'Конфигурация',
    avatarLink: 'Ссылка на аватар',
    name: 'Имя',
    description: 'Описание',
    role: 'Роль',
    temperature: 'Температура',
    top_p: 'Top_p',
    resetUserInfo: 'Сбросить информацию о пользователе',
    chatHistory: 'История чата',
    theme: 'Тема',
    language: 'Язык',
    api: 'API',
    reverseProxy: 'Обратный прокси-сервер',
    timeout: 'Время ожидания',
    socks: 'Socks',
    httpsProxy: 'HTTPS-прокси',
    balance: 'Баланс API',
    monthlyUsage: 'Ежемесячное использование',
    openSource: 'Этот проект опубликован в открытом доступе на',
    freeMIT: 'бесплатно и основан на лицензии MIT, без каких-либо форм оплаты!',
    stars: 'Если вы считаете этот проект полезным, пожалуйста, поставьте мне звезду на GitHub или сделайте небольшое пожертвование, спасибо!',
  },
  store: {
    siderButton: 'Хранилище подсказок',
    local: 'Локальное',
    online: 'Онлайн',
    title: 'Название',
    description: 'Описание',
    clearStoreConfirm: 'Вы действительно хотите очистить данные?',
    importPlaceholder: 'Пожалуйста, вставьте здесь JSON-данные',
    addRepeatTitleTips: 'Дубликат названия, пожалуйста, введите другое название',
    addRepeatContentTips: 'Дубликат содержимого: {msg}, пожалуйста, введите другой текст',
    editRepeatTitleTips: 'Конфликт названий, пожалуйста, измените название',
    editRepeatContentTips: 'Конфликт содержимого {msg}, пожалуйста, измените текст',
    importError: 'Не совпадает ключ-значение',
    importRepeatTitle: 'Название повторяющееся, пропускается: {msg}',
    importRepeatContent: 'Содержание повторяющееся, пропускается: {msg}',
    onlineImportWarning: 'Внимание! Проверьте источник JSON-файла!',
    downloadError: 'Проверьте состояние сети и правильность JSON-файла',
  },
}
