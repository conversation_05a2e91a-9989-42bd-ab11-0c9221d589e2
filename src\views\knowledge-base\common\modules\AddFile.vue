<script lang="ts" setup>
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, UploadFile, UploadProps } from 'element-plus'
import { Icon } from '@iconify/vue'
import { Delete } from '@element-plus/icons-vue'
import type { GlobalThemeOverrides } from 'naive-ui'
import { NConfigProvider } from 'naive-ui'
import { getBizKbItemPreviewSlice, postBizKbUploadItem } from '@/api/knowledge-base'
import { globalHeaders } from '@/utils/request/axios'
import { formatFileSize } from '@/utils/formatter'
import { useKnowledgeFileUpload } from '@/hooks/useKnowledgeFileUpload'
import { getFileSuffix } from '@/utils/file'

const props = defineProps<{
  kbId: string
}>()
const emits = defineEmits(['changeMode'])
const router = useRouter()

const themeOverrides: GlobalThemeOverrides = {
  Steps: {
    indicatorColorWait: '#fff',
  },
}

const stepList = [
  {
    title: '选择文件',
  },
  {
    title: '数据处理',
  },
  {
    title: '确认上传',
  },
]
const loading = ref(false)

const handleExceed: UploadProps['onExceed'] = (files, uploadFiles) => {
  const currentCount = uploadFiles.length
  const newCount = files.length
  ElMessage.warning(
    `最多支持上传${MAX_FILES}个文件，当前已选${currentCount}个，本次又选择了${newCount}个`,
  )
}
const allowedTypes = ['txt', 'docx', 'doc', 'pdf', 'md', 'html', 'pptx', 'ppt']
const allowedTypesText = computed(() => allowedTypes.map(t => `.${t}`).join(', '))
const MAX_FILES = 1000
const checkFileType: UploadProps['beforeUpload'] = rawFile => {
  // 检查每个文件
  const fileType = rawFile.name.split('.').pop()?.toLowerCase()
  const isValidType = fileType && allowedTypes.includes(fileType)
  const isValidSize = rawFile.size <= 500 * 1024 * 1024 // 500MB

  if (!isValidType) {
    ElMessage.error(`不支持 ${fileType} 文件类型`)
    return false
  }
  if (!isValidSize) {
    ElMessage.error(`${rawFile.name} 文件大小不能超过500MB`)
    return false
  }

  return true
}

function backToTable() {
  clearFileList()
  emits('changeMode', 'table')
}

const current = ref(1)
const status = ref<'error' | 'process' | 'wait' | 'finish' | undefined>('process')

function nextStep() {
  // 如果当前步骤未上传文件 或者 有文件上传失败，弹出提示，不进行下一步
  if (!fileList.value || !fileList.value.length) {
    ElMessage.error('请选择文件')
    return
  } else if (fileList.value && tableData.value.some(f => f.progress !== 100)) {
    ElMessage.warning('请等待文件上传完成')
    return
  }
  // 如果 当前不是最后步，则进行下一步
  if (current.value < stepList.length) {
    current.value++
  }

  if (current.value === 2) {
    selectFile.value = tableData.value[0]?.ossId
  }
}

function prevStep() {
  if (current.value > 1) {
    current.value--
    if (current.value === 2) {
      selectFile.value = tableData.value[0]?.ossId
    }
  }
}

const dataHandleFormRef = ref<FormInstance>()
const dataHandleForm = reactive({
  // 预期在input显式展示这个字符串‘\n\n’，但是会渲染成换行
  segmentSign: '\\n\\n',
  maxSegmentSizeInTokens: 500,
  maxOverlapSizeInTokens: 100,
  textPreprocessingRules: ['replaceSpecialChar'],
})

/**
 * 预览的内容
 */
const previewBlockList = ref<string[]>([])
const previewBlockLoading = ref(false)
function showWarningMessage(message: string) {
  ElMessage.error(message)
}

function validateDataHandleForm(dataHandleForm: any): boolean {
  const { maxSegmentSizeInTokens, maxOverlapSizeInTokens } = dataHandleForm

  if (typeof maxSegmentSizeInTokens !== 'number' && !maxSegmentSizeInTokens) {
    showWarningMessage('请填写分段最大长度')
    return false
  }

  if (maxSegmentSizeInTokens > 1000) {
    showWarningMessage('分段最大长度不能超过 1000')
    return false
  }

  if (maxSegmentSizeInTokens < 1) {
    showWarningMessage('分段最大长度不能小于 1')
    return false
  }

  if (typeof maxOverlapSizeInTokens !== 'number' && !maxOverlapSizeInTokens) {
    showWarningMessage('请填写分段重叠长度')
    return false
  }
  if (maxOverlapSizeInTokens < 0) {
    showWarningMessage('分段重叠长度不能小于 0')
    return false
  }

  if (maxOverlapSizeInTokens > maxSegmentSizeInTokens) {
    showWarningMessage('分段重叠长度不能大于分段最大长度')
    return false
  }

  return true
}

function handlePreviewBlock() {
  if (!selectFile.value) {
    showWarningMessage('请选择预览文件')
    return
  }

  if (!validateDataHandleForm(dataHandleForm)) {
    return
  }

  const { maxSegmentSizeInTokens, maxOverlapSizeInTokens } = dataHandleForm

  previewBlockLoading.value = true
  // 这里不要replace了，让query展示segmentSign=%5Cn%5Cn而不是%0A%0A
  // const segmentSign = dataHandleForm.segmentSign.replace(/\\n/g, '\n')
  const segmentSign = dataHandleForm.segmentSign
  console.log('segmentSign', segmentSign, JSON.stringify(segmentSign))
  getBizKbItemPreviewSlice({
    ossId: selectFile.value,
    maxSegmentSizeInTokens,
    maxOverlapSizeInTokens,
    segmentSign,
    replaceSpecialChar: dataHandleForm.textPreprocessingRules.includes('replaceSpecialChar'),
    clearUrlAndEmail: dataHandleForm.textPreprocessingRules.includes('clearUrlAndEmail'),
  })
    .then(res => {
      if (res.code === 200) {
        previewBlockList.value = res.data
      } else {
        ElMessage.error('预览失败')
      }
    })
    .finally(() => {
      previewBlockLoading.value = false
    })
}

function handleReset(formEl: FormInstance | undefined) {
  if (!formEl) return
  formEl.resetFields()
  previewBlockList.value = []
}

const baseUrl = import.meta.env.VITE_APP_BASE_API
const uploadFileUrl = ref(`${baseUrl}/resource/oss/upload`) // 上传文件服务器地址
const headers = ref(globalHeaders())
type FileListType = UploadFile & { response: API.UploadFileResponse }

const { fileList, clearFileList } = useKnowledgeFileUpload<FileListType>()

onMounted(() => {
  // 如果页面重新加载，初始化 第三步的 表格中上传文件的数据
  fileList.value.forEach(f => {
    addFile(f)
  })
})

const {
  associationAnalysisTable,
  uploadButtonText,
  loading: fileAnalyLoading,
  addFile,
  deleteFile,
  handleUpload,
} = useAssociationAnalysis()

function handleUploadFileChange(uploadFile: UploadFile) {
  if (uploadFile.percentage === 100) {
    addFile(uploadFile as FileListType)
  }
}

interface FileRow {
  uid: number
  ossId: string
  // type: 'word' | 'pdf' | 'excel' | 'txt' | 'png' | 'jpg'
  // 类型 1-文本 2-图像
  type: 1 | 2
  name: string
  progress: number
  size: string
}

/**
 * 第一步上传文件的列表
 */
const tableData = computed<FileRow[]>(() => {
  return fileList.value.map(f => {
    const ossId = f?.response?.data?.ossId
    return {
      uid: f.uid,
      ossId,
      type: 1,
      name: f.name,
      progress: ossId ? 100 : Math.min(f.percentage || 0, 90),
      size: formatFileSize(f.size || 0),
      fileSuffix: getFileSuffix(f.name),
    }
  })
})

const previewFileList = computed(() => tableData.value.filter(item => item.ossId))

const handleDelete = (row: FileRow) => {
  ElMessageBox.confirm('确认删除该文件吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    fileList.value = fileList.value.filter(
      f => f.uid !== row.uid || f.response.data.ossId !== row.ossId,
    )
    deleteFile(row.uid)
  })
}

function handlePreview(ossId: string) {
  router.push({
    name: 'filePreview',
    params: {
      ossId,
    },
  })
}

const selectFile = ref<string>()

/**
 * 选择的文件改变
 * 如果 数据处理配置 有值，则技能型预览
 */
function handleSelectFile() {
  if (
    typeof dataHandleForm.maxSegmentSizeInTokens === 'number' &&
    typeof dataHandleForm.maxOverlapSizeInTokens === 'number'
  ) {
    handlePreviewBlock()
  }
}

function useAssociationAnalysis() {
  interface AssociationAnalysisTableType {
    ossId: string
    name: string
    type: number
    size: number
    progress: number
    uid: number
    /**
     * 关联解析状态
     * 0-未解析 1-解析中 2-解析成功 3-解析失败
     */
    status: 0 | 1 | 2 | 3
    fileSuffix: string
  }
  const associationAnalysisTable = ref<AssociationAnalysisTableType[]>([])

  /**
   * 解析成功的数量
   */
  const sucessAnalyNum = computed(() => {
    return associationAnalysisTable.value.filter(f => f.status === 2).length
  })

  /**
   * 上传按钮显示文字
   */
  const uploadButtonText = computed(() => {
    if (sucessAnalyNum.value === 0) {
      return '开始上传'
    } else if (sucessAnalyNum.value < associationAnalysisTable.value.length) {
      return `已上传${sucessAnalyNum.value},继续上传`
    } else if (sucessAnalyNum.value === associationAnalysisTable.value.length) {
      return '完成上传'
    }
  })
  /**
   * 新增文件
   */
  function addFile(file: FileListType) {
    associationAnalysisTable.value.push({
      ossId: file.response.data.ossId,
      name: file.name,
      type: 1,
      size: file.size || 0,
      progress: file.percentage || 0,
      uid: file.uid,
      status: 0,
      fileSuffix: getFileSuffix(file.name),
    })
  }

  /**
   * 删除文件
   */
  function deleteFile(fuid: number) {
    associationAnalysisTable.value = associationAnalysisTable.value.filter(f => f.uid !== fuid)
  }

  /**
   * 上传laoding
   */
  const loading = ref(false)

  /**
   * 进行上传文件任务，其实已经文件上传，这里只是告诉 服务器可以解析了，并将文件和数据库关联起来
   * 一个一个上传
   * 全部上传完成，跳转到数据集
   * 如果有上传失败的，不进行跳转
   */
  async function handleUpload() {
    if (sucessAnalyNum.value === associationAnalysisTable.value.length) {
      ElMessage.success('上传完成')
      backToTable()
      return
    }
    loading.value = true
    for await (const item of generateRequest()) {
      await sendUploadTask(item)
    }
    loading.value = false

    // 是否全部上传完成
    if (sucessAnalyNum.value === associationAnalysisTable.value.length) {
      ElMessage.success('上传完成')
      backToTable()
    }

    async function* generateRequest() {
      // 未解析 或解析失败 的列表:
      const noAnalyList = associationAnalysisTable.value.filter(
        f => f.status === 0 || f.status === 3,
      )
      for (const item of noAnalyList) {
        yield item
      }
    }

    async function sendUploadTask(row: AssociationAnalysisTableType) {
      row.status = 1
      const segmentSign = dataHandleForm.segmentSign.replace(/\\n/g, '\n')
      await postBizKbUploadItem({
        kbId: props.kbId,
        ossIds: [row.ossId],
        maxSegmentSizeInTokens: dataHandleForm.maxSegmentSizeInTokens,
        maxOverlapSizeInTokens: dataHandleForm.maxOverlapSizeInTokens,
        segmentSign,
        replaceSpecialChar: dataHandleForm.textPreprocessingRules.includes('replaceSpecialChar'),
        clearUrlAndEmail: dataHandleForm.textPreprocessingRules.includes('clearUrlAndEmail'),
      })
        .then(res => {
          if (res.code === 200) {
            row.status = 2
          } else {
            row.status = 3
          }
        })
        .catch(err => {
          console.error(err)
          row.status = 3
        })
    }
  }

  return {
    associationAnalysisTable,
    sucessAnalyNum,
    uploadButtonText,
    addFile,
    deleteFile,
    handleUpload,
    loading,
  }
}

/**
 * 开始上传
 */
function startUpload() {
  ElMessageBox.confirm('确认开始上传吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    handleUpload()
  })
}
</script>

<template>
  <div class="flex items-center justify-between">
    <div class="text-[18px] text-[#30343A] cursor-pointer" @click="backToTable">
      <Icon
        class="size-[28px] text-[#4865E8] inline-block mr-[14px]"
        icon="famicons:arrow-back-circle-outline"
      />
      <span>新增文件</span>
    </div>

    <div class="flex items-center">
      <img
        class="size-[24px] inline-block mr-[8px]"
        src="@/assets/knowledge/file-icon.png"
        alt=""
      />
      <span class="text-[15px] text-[#646A73] mr-[24px]">共{{ fileList.length }}个文件</span>
      <el-button
        v-if="stepList.length !== current"
        class="mr-[16px]"
        type="primary"
        @click="nextStep"
        >下一步</el-button
      >
      <el-button
        v-else="stepList.length === current"
        class="mr-[16px]"
        type="primary"
        :loading="fileAnalyLoading"
        @click="startUpload"
        >{{ uploadButtonText }}</el-button
      >
      <el-button :disabled="current === 1 || fileAnalyLoading" @click="prevStep">上一步</el-button>
    </div>
  </div>
  <div class="mx-auto max-w-[690px] w-full mb-6 mt-8">
    <NConfigProvider :theme-overrides="themeOverrides" abstract>
      <n-steps class="w-full" :current="current" :status="status">
        <n-step v-for="step in stepList" :key="step.title" :title="step.title" />
      </n-steps>
    </NConfigProvider>
  </div>
  <template v-if="current === 1">
    <el-upload
      v-model:file-list="fileList"
      class="mb-[24px]"
      drag
      multiple
      :accept="allowedTypes.join(',')"
      :show-file-list="false"
      :action="uploadFileUrl"
      :headers="headers"
      :before-upload="checkFileType"
      :limit="MAX_FILES"
      @change="handleUploadFileChange"
      @exceed="handleExceed"
    >
      <Icon class="size-[40px] text-[#4865E8] inline-block" icon="bx:cloud-upload" />
      <div class="text-[#30343A] text-[16px] mb-[8px] mt-[5px]">点击或拖动文件到此处上传</div>
      <div class="text-[14px]/[20px] text-[#969ba4]">支持 {{ allowedTypesText }} 类型文件</div>
      <div class="text-[14px]/[20px] text-[#969ba4]">最多支持 1000 个文件，单个文件最大500 MB</div>
    </el-upload>
    <div class="flex-1 min-h-0">
      <el-table v-loading="loading" :data="tableData" height="100%">
        <el-table-column prop="name" label="文件名" min-width="200px">
          <template #default="{ row }">
            <div class="flex items-center">
              <FileTypePic :suffix="row.fileSuffix" />
              <span class="ml-1">{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column v-slot="{ row }" prop="progress" label="文件上传进度" width="240px">
          <el-progress :percentage="row.progress" color="#1EBF60">
            {{ row.progress }}%
          </el-progress>
        </el-table-column>
        <el-table-column prop="size" label="文件大小" width="130" />
        <el-table-column label="操作" fixed="right" width="100">
          <template #default="{ row }">
            <el-button link type="default" @click="handlePreview(row.ossId)">
              <Icon class="size-[24px]" icon="mdi-light:eye" />
            </el-button>
            <el-button link type="danger" @click="handleDelete(row)">
              <el-icon size="18"><Delete /></el-icon>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </template>

  <template v-else-if="current === 2">
    <div class="flex flex-1 min-h-0">
      <div class="data-handle-config">
        <h1 class="title">
          <img class="size-[24px] inline-block" src="@/assets/knowledge/config-icon.png" alt="" />
          数据处置配置
        </h1>
        <div class="data-handle-config-content">
          <el-form
            ref="dataHandleFormRef"
            class="handle-form"
            :model="dataHandleForm"
            label-position="top"
            size="large"
          >
            <el-form-item label="分段最大长度" prop="maxSegmentSizeInTokens">
              <el-input-number
                v-model="dataHandleForm.maxSegmentSizeInTokens"
                class="number-input"
                controls-position="right"
              >
                <template #suffix>
                  <span>Tokens</span>
                </template>
              </el-input-number>
            </el-form-item>

            <el-form-item label="分段重叠长度" prop="maxOverlapSizeInTokens">
              <el-input-number
                v-model="dataHandleForm.maxOverlapSizeInTokens"
                class="number-input"
                controls-position="right"
              >
                <template #suffix>
                  <span>Tokens</span>
                </template>
              </el-input-number>
            </el-form-item>
            <!-- https://cloud.dify.ai/datasets/create： 分隔符是用于分隔文本的字符。\n\n和\n是常用于分隔段落和行的分隔符。用逗号连接分隔符(\n\n,\n)，当段落超过最大块长度时，会按行进行分割。你也可以使用自定义的特殊分隔符(例如 ***)。 -->
            <el-form-item label="分段标识符" prop="segmentSign">
              <template #label>
                <span>分段标识符</span>
                <el-tooltip
                  content="分隔符是用于分隔文本的字符。\n\n和\n是常用于分隔段落和行的分隔符。用逗号连接分隔符(\n\n,\n)，当段落超过最大块长度时，会按行进行分割。你也可以使用自定义的特殊分隔符(例如 ***)。"
                  placement="top"
                >
                  <i class="iconfont icon-wenhao ml-1 cursor-pointer"></i>
                </el-tooltip>
              </template>
              <el-input v-model="dataHandleForm.segmentSign" />
            </el-form-item>

            <el-form-item label="文本预处理规则" prop="textPreprocessingRules">
              <el-checkbox-group v-model="dataHandleForm.textPreprocessingRules" size="large">
                <el-checkbox value="replaceSpecialChar" name="type">
                  <span class="text-[#30343A] text-[16px]"> 替换掉连续的空格、换行符和制表符 </span>
                </el-checkbox>
                <el-checkbox value="clearUrlAndEmail" name="type">
                  <span class="text-[#30343A] text-[16px]"> 删除所有 URL 和电子邮件地址 </span>
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-form>
        </div>
        <div class="grid grid-cols-2 gap-4">
          <el-button @click="handlePreviewBlock">
            <img
              class="inline-block size-5"
              src="@/assets/knowledge/preview-block-icon.png"
              alt=""
            />预览块
          </el-button>
          <el-button @click="handleReset(dataHandleFormRef)">
            <img class="inline-block size-5" src="@/assets/knowledge/reset-icon.png" alt="" />重置
          </el-button>
        </div>
      </div>
      <div class="preview-block">
        <div class="title">
          <div>
            <img
              class="inline-block size-6 mr-2 align-sub"
              src="@/assets/knowledge/preview-block-icon.png"
              alt=""
            />预览
          </div>
          <el-select
            v-model="selectFile"
            style="max-width: 350px"
            :border="false"
            @change="handleSelectFile"
          >
            <template #prefix>
              <img class="inline-block size-6 mr-2" src="@/assets/word.png" alt="" />
            </template>
            <el-option
              v-for="item in previewFileList"
              :key="item.ossId"
              :label="item.name"
              :value="item.ossId"
            />
          </el-select>
        </div>
        <div v-loading="previewBlockLoading" class="preview-block-content">
          <template v-for="(block, i) in previewBlockList" :key="i">
            <!-- <div>Chunk-1 • 253 characters</div> -->
            <div class="mb-5">
              {{ block }}
            </div>
          </template>
          <el-empty v-if="previewBlockList.length === 0" description="点击左侧文件后进行预览" />
        </div>
      </div>
    </div>
  </template>

  <template v-else-if="current === 3">
    <div class="flex-1 min-h-0">
      <el-table v-loading="loading" :data="associationAnalysisTable" height="100%">
        <el-table-column prop="name" label="文件名" min-width="200px">
          <template #default="{ row }">
            <div class="flex items-center">
              <FileTypePic :suffix="row.fileSuffix" />
              <span class="ml-1">{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column v-slot="{ row }" prop="status" label="状态" width="240px">
          <span v-if="row.status === 0" class="tag">等待中</span>
          <span v-if="row.status === 1" class="tag">进行中</span>
          <span v-if="row.status === 2" class="tag">完成</span>
          <span v-if="row.status === 3" class="tag">失败</span>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="100">
          <template #default="{ row }">
            <el-button
              v-if="[0, 3].includes(row.status)"
              link
              type="danger"
              @click="handleDelete(row)"
            >
              <el-icon size="18"><Delete /></el-icon>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </template>
</template>

<style lang="less" scoped>
:deep(.el-upload-dragger) {
  background-color: #fff;
}

.data-handle-config {
  width: 380px;
  padding: 16px;
  background-color: #fff;
  margin-right: 24px;
  .title {
    color: #4865e8;
    font-size: 16px;
    border-bottom: 1px solid rgba(218, 221, 232, 0.5);
    padding-bottom: 16px;
  }

  &-content {
    height: calc(100% - 80px);
    overflow-y: auto;
    padding-top: 16px;
    padding-bottom: 16px;
  }
}

.preview-block {
  flex: 1;
  background-color: #fff;
  padding: 16px;
  padding-bottom: 8px;
  min-height: 0;
  overflow: hidden;
  .title {
    display: flex;
    justify-content: space-between;
    color: #4865e8;
    font-size: 16px;
    border-bottom: 1px solid rgba(218, 221, 232, 0.5);
    padding-bottom: 5px;
  }

  &-content {
    color: #30343a;
    padding-bottom: 16px;
    font-size: 14px;
    line-height: 22px;
    max-height: calc(100% - 42px);
    overflow-y: auto;
    padding-bottom: 8px;
  }
}

:deep(.el-select .el-select__wrapper) {
  box-shadow: none !important;
  min-height: 0;
}

.handle-form:deep(.el-form-item__label) {
  color: #30343a;
  font-size: 16px;
}
.number-input {
  width: 100%;
  :deep(.el-input) {
    input {
      text-align: left;
    }
  }
  :deep(.el-input-number__decrease),
  :deep(.el-input-number__increase) {
    background: #fff;
  }
}

.tag {
  display: inline-block;
  padding: 0px 16px;
  background: #e9e9fe;
  border-radius: 4px;
  height: 32px;
  line-height: 32px;
  font-size: 14px;
  color: #4865e8;
}
</style>
