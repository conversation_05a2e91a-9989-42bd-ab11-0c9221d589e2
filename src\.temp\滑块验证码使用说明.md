# 滑块验证码使用说明

## 概述

本系统已集成AJ-Captcha滑块验证码，支持在登录时进行滑块拼图验证，提升系统安全性。

## 接口说明


### 1. 判断是否需要滑块验证
**接口地址：** `GET/auth/checkCaptcha?username=admin&tenantId=000000`

响应示例：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "needCaptcha": true,
    "failureCount": 1,
    "message": "需要进行滑块验证"
  }
}
```

### 2. 获取验证码
**接口地址：** `POST /captcha/get`

**请求参数：**
```json
{
    "captchaType": "blockPuzzle",
    "clientUid": "客户端唯一标识(可选)"
}
```

**响应参数：**
```json
{
    "repCode": "0000",
    "repData": {
        "originalImageBase64": "底图base64",
        "jigsawImageBase64": "滑块图base64",
        "token": "验证token",
        "secretKey": "AES密钥(可选)",
        "point": {
            "x": 205,
            "y": 5
        }
    },
    "success": true
}
```

### 3. 校验验证码
**接口地址：** `POST /captcha/check`

**请求参数：**
```json
{
    "captchaType": "blockPuzzle",
    "pointJson": "加密后的坐标信息",
    "token": "验证token"
}
```

### 4. 登录时使用验证码
**接口地址：** `POST /auth/login`

**请求参数：**
```json
{
    "clientId": "e5cd7e4891bf95d1d19206ce24a7b32e",
    "grantType": "password",
    "username": "admin",
    "password": "admin123",
    "captchaVerification": "验证码验证信息"
}
```

## 错误码说明

| 错误码 | 说明 |
|-------|------|
| 0000  | 验证成功 |
| 9999  | 服务器内部异常 |
| 0011  | 参数不能为空 |
| 6110  | 验证码已失效，请重新获取 |
| 6111  | 验证失败 |
| 6112  | 获取验证码失败，请联系管理员 |
| 6113  | 底图未初始化成功，请检查路径 |
| 6201  | get接口请求次数超限，请稍后再试 |
| 6206  | 无效请求，请重新获取验证码 |
| 6202  | 接口验证失败数过多，请稍后再试 |
| 6204  | check接口请求次数超限，请稍后再试 |

