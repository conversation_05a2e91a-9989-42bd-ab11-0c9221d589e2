import { useSessionStorage } from "@vueuse/core"

/**
 * 我的知识库文件上传 key
 */
export const MY_KNOWLEDGE_FILE_UPLOAD_KEY = 'MY_KNOWLEDGE_FILE_UPLOAD_KEY'

/**
 * 共享芝士焗文件上传 key
 */
// export const SHARE_KNOWLEDGE_FILE_UPLOAD_KEY = 'SHARE_KNOWLEDGE_FILE_UPLOAD_KEY'

export function useKnowledgeFileUpload<T = any>() {
  const fileList = useSessionStorage<T[]>(MY_KNOWLEDGE_FILE_UPLOAD_KEY, [])

  /**
   * 清除 上传文件
   */
  function clearFileList() {
    fileList.value = []
  }

  return {
    fileList,
		clearFileList
  }
}
