<script setup lang="ts">
const selectedValue = defineModel<number>({ default: 1 })

const options = [
  { label: '近七天', value: 1 },
  { label: '昨天', value: 2 },
  { label: '今天', value: 3 },
]
</script>

<template>
  <div class="mydict">
    <div>
      <label v-for="option in options" :key="option.value">
        <input
          type="radio"
          :value="option.value"
          :checked="selectedValue === option.value"
          @change="selectedValue = option.value"
        />
        <span>{{ option.label }}</span>
      </label>
    </div>
  </div>
</template>

<style scoped>
:focus {
  outline: 0;
  /* border-color: #2260ff;
  box-shadow: 0 0 0 4px #b5c9fc; */
}

.mydict div {
  display: flex;
  flex-wrap: wrap;
  margin-top: 0.5rem;
  justify-content: center;
}

.mydict div label {
  flex: 1;
}

.mydict input[type='radio'] {
  clip: rect(0 0 0 0);
  clip-path: inset(100%);
  height: 1px;
  overflow: hidden;
  position: absolute;
  white-space: nowrap;
  width: 1px;
}

.mydict input[type='radio']:checked + span {
  /* box-shadow: 0 0 0 0.0625em #4865e8; */
  background-color: #e9e9fe;
  z-index: 1;
  color: #4865e8;
}

label span {
  display: block;
  cursor: pointer;
  background-color: #fff;
  padding: 0.375em 0.75em;
  position: relative;
  /* margin-left: 0.0625em; */
  box-shadow: 0 0 0 0.0625em #e8e8e8;
  letter-spacing: 0.05em;
  color: #3e4963;
  text-align: center;
  transition: background-color 0.5s ease;
}

label:first-child span {
  border-radius: 20px 0 0 20px;
}

label:last-child span {
  border-radius: 0 20px 20px 0;
}
</style>
