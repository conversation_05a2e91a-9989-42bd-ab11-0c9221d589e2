<script setup lang="ts">
import { getModelLogo } from '@/utils/models'
import { modelTypeList } from '../../constant/index'
import { AIModelVo } from '@/api'
import { deleteBizAiModel } from '@/api/model'
import { getBizKbGetModelBindKb } from '@/api/knowledge-base'
import DeleteEmbeddingModelDialog from './DeleteEmbeddingModelDialog.vue'

defineOptions({
  name: '',
})
const emits = defineEmits(['addModel', 'editModel', 'updateModelList', 'getAvaliableModelList'])
const props = defineProps<{
  textModelList: AIModelVo[]
  embeddingModelList: AIModelVo[]
  rerankModelList: AIModelVo[]
  loading: boolean
}>()

const activeModelType = ref('text')
const modelList = computed(() => {
  return getModelListBySort(activeModelType.value)
})
const deleteEmbeddingModelDialogRef = ref()

function getModelListBySort(sort: string) {
  switch (sort) {
    case 'text':
      return props.textModelList
    case 'embedding':
      return props.embeddingModelList
    case 'rerank':
      return props.rerankModelList
    default:
      return []
  }
}

const modelTypeWithCountList = computed(() => {
  return modelTypeList.map(item => {
    const modelList = getModelListBySort(item.value)

    return {
      ...item,
      count: modelList.length,
    }
  })
})

/**
 * 删除模型
 * 如果删除文本向量模型，需获取关联的知识库进行提示
 */
async function handleDeleteModel({
  id: modelId,
  type,
  displayName: modelName,
}: {
  id: string
  type: string
  displayName?: string
}) {
  if (type === 'embedding') {
    getBizKbGetModelBindKb(modelId).then(res => {
      if (res.code === 200) {
        console.log('关联的知识库:', res.data)
        if (res.data.length > 0) {
          if (deleteEmbeddingModelDialogRef.value) {
            deleteEmbeddingModelDialogRef.value.handleTip({
              modelId,
              modelName: modelName,
              knowledgeBaseList: res.data,
            })
          }
        } else {
          confirmDeleteModel()
        }
      }
    })

    return
  } else {
    confirmDeleteModel()
  }

  async function confirmDeleteModel() {
    try {
      await ElMessageBox.confirm('确定要删除该模型吗？', '删除模型', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })

      deleteModel(modelId)
    } catch (error) {
      if (error instanceof Error) {
        console.error('删除模型失败:', error)
        ElMessage.error(error.message || '删除失败')
      }
    }
  }
}

const deleteModelLoading = ref(false)
/**
 * 删除模型
 */
function deleteModel(modelId: string) {
  if (!modelId) {
    ElMessage.error('模型ID不能为空')
    return
  }
  deleteModelLoading.value = true
  deleteBizAiModel(modelId)
    .then(res => {
      if (res.code === 200) {
        ElMessage.success('删除成功')
        emits('updateModelList')
      } else {
        ElMessage.error(res.msg || '删除失败')
      }
    })
    .finally(() => {
      deleteModelLoading.value = false
    })
}
</script>

<template>
  <div class="mb-3 flex items-center justify-between">
    <span class="text-[16px] g-family-medium">模型</span>
    <div>
      <el-button class="mr-4" @click="emits('getAvaliableModelList')">获取模型列表</el-button>
      <el-button class="" @click="emits('addModel')">自定义添加</el-button>
    </div>
  </div>
  <div class="bg-white rounded-lg p-4 flex-1 min-h-0 flex flex-col" v-loading="loading">
    <div class="pb-4">
      <el-button
        :type="item.value === activeModelType ? 'primary' : ''"
        v-for="item in modelTypeWithCountList"
        :key="item.value"
        :link="item.value !== activeModelType"
        class="mr-4"
        @click="activeModelType = item.value"
      >
        <span class="g-family-medium text-[16px]">{{ item.label }}</span>

        <span
          v-if="item.count"
          class="inline-block size-5 bg-[#E9E9FE] text-[#30343A] text-[14px]/5 rounded-full ml-2"
          :class="{ 'bg-[#fff] text-[#4865E8]': item.value === activeModelType }"
        >
          {{ item.count }}
        </span>
      </el-button>
    </div>
    <div class="flex-1 min-h-0 overflow-auto">
      <div v-for="(model, i) in modelList" :key="model.name" class="model-list-item">
        <div class="model-item">
          <img
            class="size-10 inline-block mr-2 rounded-lg"
            :src="getModelLogo(model.name)"
            alt=""
          />
          <span class="mr-3 text-[16px] g-family-medium truncate">{{ model.displayName }}</span>
          <el-tooltip
            v-if="model.capabilities?.includes('reasoning')"
            effect="dark"
            placement="top"
            content="推理"
          >
            <img
              class="inline-block w-[32px] h-[20px] mr-2"
              src="@/assets/model/tuili.svg"
              alt=""
            />
          </el-tooltip>

          <el-tooltip
            v-if="model.capabilities?.includes('vision')"
            effect="dark"
            placement="top"
            content="视觉"
          >
            <img
              class="inline-block w-[32px] h-[20px] mr-2"
              src="@/assets/model/shijue.svg"
              alt=""
            />
          </el-tooltip>

          <el-tooltip
            v-if="model.capabilities?.includes('toolCalling')"
            effect="dark"
            placement="top"
            content="工具"
          >
            <img
              class="inline-block w-[32px] h-[20px] mr-2"
              src="@/assets/model/gongju.svg"
              alt=""
            />
          </el-tooltip>
        </div>

        <div class="h-[18px]">
          <el-icon class="cursor-pointer mr-4" size="18" @click="emits('editModel', model)"
            ><Setting
          /></el-icon>
          <el-icon
            class="cursor-pointer"
            size="18"
            color="#F36442"
            @click="handleDeleteModel(model)"
            ><Delete
          /></el-icon>
        </div>
      </div>
      <el-empty v-if="modelList.length === 0"></el-empty>
    </div>
  </div>
  <DeleteEmbeddingModelDialog
    ref="deleteEmbeddingModelDialogRef"
    :loading="deleteModelLoading"
    @deleteModel="deleteModel"
  />
</template>

<style lang="less" scoped>
.model-item {
  display: grid;
  grid-template-columns: 40px 1fr auto repeat(3, auto);
  gap: 8px;
  align-items: center;
}

.model-list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  border-radius: 8px;
  padding: 8px;
  padding-right: 16px;
  transition: all 0.3s ease-in-out;
  &:hover {
    background: #e6eaf4;
  }
}
</style>
