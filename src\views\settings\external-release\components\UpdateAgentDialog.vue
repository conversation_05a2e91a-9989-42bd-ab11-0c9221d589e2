<script setup lang="ts">
import { ref } from 'vue'
import { syncOutward } from '@/api/external-release'

defineOptions({
  name: 'UpdateAgentDialog',
})
const emits = defineEmits(['updateReleaseList'])

const dialogVisible = ref(false)
const loading = ref(false)
const agentId = ref<string>()

const handleConfirm = async () => {
  loading.value = true
  syncOutward(agentId.value)
    .then(res => {
      if (res.code === 200) {
        emits('updateReleaseList')
      }
    })
    .finally(() => {
      dialogVisible.value = false
      loading.value = false
    })
}
function updateAgent(id: string) {
  dialogVisible.value = true
  agentId.value = id
}
defineExpose({
  updateAgent,
})

function handleClose(done) {
  if (!loading.value) {
    done()
  }
}
</script>

<template>
  <el-dialog v-model="dialogVisible" width="500" :be4fore-close="handleClose">
    <template #header>
      <span class="text-[24px] g-family-medium">更新确认</span>
    </template>

    <span>发现助手有更新，是否同步？</span>
    <template #footer>
      <div class="dialog-footer">
        <el-button :disabled="loading" @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
