import { deleteReq, get, post } from '@/utils/request'
import { ModelParams } from './type'

// 添加
export function postBizAiModelAdd<T>(data: ModelParams) {
	return post<T>({
		url: '/biz/aiModel/add',
		data,
	})
}


// 根据平台ID获取模型列表（按类型分组）
export function getBizAiModelListByPlatformGrouped<T>(platformId: string | number) {
	return get<T>({
		url: `/biz/aiModel/listByPlatformGrouped/${platformId}`,
	})
}

// 删除
export function deleteBizAiModel<T>(id: string) {
	return deleteReq<T>({
		url: `/biz/aiModel/delete/${id}`,
	})
}

// 更新
export function postBizAiModelUpdate<T>(data: Record<string, any>) {
	return post<T>({
		url: '/biz/aiModel/update',
		data,
	})
}

// 检查模型是否可用（直接）
export function postBizAiModelCheckModelStatusByRequest<T>(data: Record<string, any>) {
	return post<T>({
		url: '/biz/aiModel/checkModelStatusByRequest',
		data,
	})
}

// 获取平台可用的模型列表
export function getBizAiPlatformModels<T>(id: string | number) {
	return get<T>({
		url: `/biz/aiPlatform/platform/${id}/models`,
	})
}

// 检查模型是否可用
export function getBizAiModelCheckModelStatus<T>(data: any) {
	return get<T>({
		url: '/biz/aiModel/checkModelStatus',
		data,
	})
}

// 更新模型固定状态
export function updateBizAiModelPinned<T>(data: { id: string, pinned: 0 | 1 }) {
	return post<T>({
		url: '/biz/aiModel/updatePinned',
		data,
	})
}