<script setup lang="ts">
import '@/typings/knowledge-base.d.ts'
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import rightArrowIcon from '@/assets/right-arrow.svg'

defineOptions({
  name: 'DeleteEmbeddingModelDialog',
})

const emits = defineEmits(['deleteModel'])
const props = defineProps<{
  loading: boolean
}>()

type TipContent = {
  modelId: string
  modelName: string
  knowledgeBaseList: KnowledgeBase.KnowledgeBaseBo[]
}
const tipContet = reactive<TipContent>({
  modelId: '',
  modelName: '',
  knowledgeBaseList: [],
})

// const dialogVisible = defineModel({ default: false })
const dialogVisible = ref(false)

function handleBeforeClose(done: () => void) {
  if (!props.loading) {
    done()
  }
}

function handleClose() {
  dialogVisible.value = false
}

/**
 * 进行二次确认提示
 */
function handleTip(cofirmContent: TipContent) {
  tipContet.modelId = cofirmContent.modelId
  tipContet.modelName = cofirmContent.modelName
  tipContet.knowledgeBaseList = cofirmContent.knowledgeBaseList
  dialogVisible.value = true
}

/**
 * 确认删除
 */
function handleConfirm() {
  emits('deleteModel', tipContet.modelId)
  handleClose()
}

const router = useRouter()

function handleNavigate(kbId: string) {
  dialogVisible.value = false
  router.push({
    name: 'dataset',
    params: {
      kbId,
    },
  })
}

defineExpose({
  handleTip,
})
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    width="600"
    :close-on-click-modal="false"
    :before-close="handleBeforeClose"
    @close="handleClose"
  >
    <template #header>
      <img class="inline-block size-8 mr-3" src="@/assets/warning.svg" alt="" />
      <span class="text-[24px] g-family-medium">模型正在使用中，确认删除？</span>
    </template>

    <div>
      您要删除的向量模型 「<span class="text-[#4865E8]">{{ tipContet.modelName }}</span
      >」 正在被以下{{ tipContet.knowledgeBaseList.length }}个知识库使用：
    </div>
    <ul class="knowledge-list">
      <li
        class="knowledge-item truncate"
        v-for="item in tipContet.knowledgeBaseList"
        :key="item.id"
        @click="handleNavigate(item.id)"
      >
        <span class="ml-[-10px]">• {{ item.name }}</span>
        <img class="size-5 absolute right-1 top-2" :src="rightArrowIcon" alt="" />
      </li>
    </ul>

    <div class="text-[16px]/[28px] mt-6 mb-6">
      <div>删除此模型将产生严重后果：</div>
      <div>1. 功能立即失效：上述知识库的语义搜索、智能问答等核心功能将立刻无法使用。</div>
      <div>2. 数据需要重建：您必须为这些知识库手动设置新模型并重新构建全部向量，才能恢复功能。</div>

      <div class="mt-3">强烈建议您先前往对应的知识库更换模型后再进行删除。</div>
    </div>

    <div class="text-[18px] g-family-medium mb-6">您确定要忽略以上风险，强制删除此模型吗？</div>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="danger" plain :loading="loading" @click="handleConfirm">
          {{ loading ? '删除中...' : '强制删除' }}
        </el-button>
        <el-button type="primary" :disabled="loading" @click="handleClose"
          >我知道了，先不删除</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<style lang="less" scoped>
.knowledge-list {
  max-height: 310px;
  overflow-y: auto;
  // list-style-type: disc;
  // list-style-position: inside;

  .knowledge-item {
    height: 36px;
    line-height: 36px;
    padding: 0 8px;
    font-size: 16px;
    background: #f7f8fa;
    border-radius: 4px;
    margin-top: 12px;
    padding-left: 20px;
    padding-right: 35px;
    position: relative;

    &:hover {
      background: rgba(72, 101, 232, 0.15);
    }
  }
}
</style>
