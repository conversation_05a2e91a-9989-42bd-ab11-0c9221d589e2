<script setup lang="ts">
import { computed } from 'vue'
import { NLayout, NLayoutContent } from 'naive-ui'
import { useRoute, useRouter } from 'vue-router'
import Permission from './Permission.vue'
import Sider from './LayoutSider/index.vue'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { useAppStore, useChatStore, useUserStore } from '@/store'

const router = useRouter()
const route = useRoute()
const appStore = useAppStore()
const chatStore = useChatStore()
const userStore = useUserStore()

// 只有在非公开聊天路由下才进行路由替换
if (!route.path.includes('/chat/public')) {
  router.replace({ name: 'Chat', params: { conversationId: chatStore.active } })
}

const { isMobile } = useBasicLayout()

const collapsed = computed(() => appStore.siderCollapsed)

// 根据实际需求调整权限检查逻辑，这里暂时设为false，因为auth模块已废弃
const needPermission = computed(() => false)

const getMobileClass = computed(() => {
  if (isMobile.value) return ['rounded-none', 'shadow-none']
  return [] // 'border', 'shadow-md', 'dark:border-neutral-800'
})

const getContainerClass = computed(() => {
  return ['h-full', { 'pl-[360px]': !isMobile.value && !collapsed.value }]
})
</script>

<template>
  <div class="h-full overflow-hidden" :class="getMobileClass">
    <NLayout class="z-40 transition" :class="getContainerClass" has-sider>
      <Sider />
      <NLayoutContent class="h-full">
        <RouterView v-slot="{ Component, route }">
          <component :is="Component" :key="route.fullPath" />
        </RouterView>
      </NLayoutContent>
    </NLayout>
  </div>
  <Permission :visible="needPermission" />
</template>
