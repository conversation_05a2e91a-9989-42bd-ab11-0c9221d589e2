<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { postBizAgentAuditAgent } from '@/api/agent'
import type { AgentEventRecordVo } from '@/typings/agent'

const props = defineProps<{
  modelValue: boolean
  agentId: string
  categoryIdList?: number[]
  modelId?: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}>()

const formRef = ref<FormInstance>()
const form = ref<AgentEventRecordVo>({
  agentId: props.agentId,
  auditStatus: true,
  description: '',
  categoryIdList: props.categoryIdList,
  modelId: props.modelId,
})

const rules = {
  auditStatus: [{ required: true, message: '请选择审核结果', trigger: 'change' }],
  description: [{ required: true, message: '请输入审核说明', trigger: 'blur' }],
}

// 监听agentId变化
watch(
  () => props.agentId,
  newVal => {
    if (newVal) {
      form.value.agentId = newVal
    }
  },
)

// 监听categoryIdList变化
watch(
  () => props.categoryIdList,
  newVal => {
    form.value.categoryIdList = newVal
  },
)

// 监听modelId变化
watch(
  () => props.modelId,
  newVal => {
    form.value.modelId = newVal
  },
)

const handleClose = () => {
  emit('update:modelValue', false)
  form.value = {
    agentId: props.agentId,
    auditStatus: true,
    description: '',
    categoryIdList: props.categoryIdList,
    modelId: props.modelId,
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async valid => {
    if (valid) {
      try {
        const res = await postBizAgentAuditAgent(form.value)
        if (res.code === 200) {
          ElMessage.success('审核成功')
          emit('success')
          handleClose()
        }
      } catch (error) {
        console.error('审核失败:', error)
      }
    }
  })
}
</script>

<template>
  <el-dialog
    draggable
    :model-value="modelValue"
    title="审核助手"
    width="500px"
    :close-on-click-modal="false"
    @update:model-value="val => emit('update:modelValue', val)"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="审核结果" prop="auditStatus">
        <el-radio-group v-model="form.auditStatus">
          <el-radio :label="true">通过</el-radio>
          <el-radio :label="false">拒绝</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="审核说明" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="4"
          placeholder="请输入审核说明"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style lang="less" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
