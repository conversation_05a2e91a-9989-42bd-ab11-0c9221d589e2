export const formatFileSize = (size: number | undefined | null): string => {
  if (size === null || size === undefined) {
    return '0 KB'
  }
  const units = ['KB', 'MB', 'GB', 'TB', 'PB']
  let unitIndex = 0
  let calculatedSize = size / 1024

  while (calculatedSize > 1024 && unitIndex < units.length - 1) {
    calculatedSize /= 1024
    unitIndex++
  }

  return `${calculatedSize.toFixed(2)} ${units[unitIndex]}`
};

/**
 * 数字千分位格式化
 * @param num 需要格式化的数字
 * @returns 千分位格式的字符串
 */
export const formatNumberWithCommas = (num: any): string => {
	num  = Number(num)

	 // 检查是否为有效数字
  if (isNaN(num)) {
    return '-'; // 或根据需求返回其他值
  }
  return new Intl.NumberFormat('en-US').format(num);
};
