<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { getBizAgentCategoryList, postBizAgentUpdateAgentCategory } from '@/api/agent'

const props = defineProps<{
  visible: boolean
  agent: Agent.AgentVo | null
}>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

const dialogVisible = ref(props.visible)
const categoryList = ref<Agent.AgentCategoryVo[]>([])
const selectedCategoryIds = ref<string[]>([])

// 重置表单数据
const resetForm = () => {
  selectedCategoryIds.value = []
}

// 初始化表单数据
const initForm = async () => {
  await loadCategoryList()
  // 设置当前选中的分类
  // console.log('props.agent', props.agent)
  // console.log('props.agent?.categoryIdList', props.agent?.categoryIdList)
  if (props.agent?.categoryIdList?.length) {
    selectedCategoryIds.value = props.agent.categoryIdList.map(id => id.toString())
  } else {
    selectedCategoryIds.value = []
  }
}

// 监听visible属性的变化
watch(
  () => props.visible,
  newVal => {
    dialogVisible.value = newVal
    if (newVal) {
      // 打开对话框时初始化表单
      initForm()
    } else {
      // 关闭对话框时重置表单
      resetForm()
    }
  },
)

// 监听dialogVisible的变化
watch(dialogVisible, newVal => {
  emit('update:visible', newVal)
})

// 加载分类列表
const loadCategoryList = async () => {
  try {
    const res = await getBizAgentCategoryList<Agent.AgentCategoryVo[]>()
    if (res.code === 200 && res.data) {
      categoryList.value = res.data
    } else {
      ElMessage.error(res.msg || '获取分类列表失败')
    }
  } catch (error) {
    console.error('获取分类列表失败:', error)
    ElMessage.error('获取分类列表失败')
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!selectedCategoryIds.value.length) {
    ElMessage.warning('请选择分类')
    return
  }

  if (!props.agent?.id) {
    ElMessage.error('助手ID不能为空')
    return
  }

  try {
    const res = await postBizAgentUpdateAgentCategory<API.Response<any>>({
      id: props.agent.id,
      categoryIdList: selectedCategoryIds.value,
    })

    if (res.code === 200) {
      ElMessage.success('修改分类成功')
      dialogVisible.value = false
      emit('success')
    } else {
      ElMessage.error(res.msg || '修改分类失败')
    }
  } catch (error) {
    if (error instanceof Error) {
      console.error('修改分类失败:', error)
      ElMessage.error(error.message || '修改分类失败')
    }
  }
}
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    draggable
    title="修改分类"
    width="400px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form label-width="80px" label-position="top">
      <el-form-item label="选择分类">
        <el-select
          v-model="selectedCategoryIds"
          multiple
          placeholder="请选择分类"
          style="width: 100%"
        >
          <el-option
            v-for="item in categoryList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>
