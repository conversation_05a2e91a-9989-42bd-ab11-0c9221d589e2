import { defineStore } from 'pinia'

export const useKnowledgeBaseStore = defineStore('knowledge-base-store', {
	state: () => ({
    currentKnowledgeTitle: '',
		knowledgeBasePermissions: {} as { [knowledgeBaseId: string]: string }, // 知识库ID和权限的对应关系
  }),
	getters: {
    hasEditPermission: (state) => (knowledgeBaseId: string) => state.knowledgeBasePermissions[knowledgeBaseId] === '2',
  },
  actions: {
    setKnowledgeTitle(title: string) {
      this.currentKnowledgeTitle = title
    },
		setSharePermissionLevel(knowledgeBaseId: string, level: string) {
      this.knowledgeBasePermissions[knowledgeBaseId] = level
      sessionStorage.setItem(`sharePermissionLevel_${knowledgeBaseId}`, level)
    },
    clearSharePermissionLevel(knowledgeBaseId: string) {
      delete this.knowledgeBasePermissions[knowledgeBaseId]
      sessionStorage.removeItem(`sharePermissionLevel_${knowledgeBaseId}`)
    },
  }
})
