import type { AxiosResponse } from 'axios'
import axios from 'axios'
import { encrypt as rsaEncrypt } from './jsencrypt'
import { encryptBase64, encryptWithAes, generateAesKey } from './crypto'
import { getCurrentToken } from '@/utils/token-manager'
import ms from '@/utils/message'

// 获取环境变量
const baseURL = import.meta.env.VITE_APP_BASE_API
const clientId = import.meta.env.VITE_APP_CLIENT_ID

// 创建axios实例
const service = axios.create({
  baseURL,
  timeout: 50000,
  headers: {
    'Content-Type': 'application/json;charset=utf-8',
    clientid: clientId,
  },
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 使用统一的token获取逻辑
    const token = getCurrentToken()
    // 如果存在token，则添加到请求头
    if (token && config.headers?.isToken !== false) config.headers.Authorization = `Bearer ${token}`

    // 对应国际化资源文件后缀
    config.headers['Content-Language'] = 'zh_CN'

    // 是否需要防止数据重复提交
    const isRepeatSubmit = config.headers?.repeatSubmit === false
    // 是否需要加密
    const isEncrypt = config.headers?.isEncrypt === true || config.headers?.isEncrypt === 'true'

    if (!isRepeatSubmit && (config.method === 'post' || config.method === 'put')) {
      const requestObj = {
        url: config.url,
        data: typeof config.data === 'object' ? JSON.stringify(config.data) : config.data,
        time: new Date().getTime(),
      }
      const sessionObj = sessionStorage.getItem('sessionObj')
      if (sessionObj === null) {
        sessionStorage.setItem('sessionObj', JSON.stringify(requestObj))
      } else {
        const s_obj = JSON.parse(sessionObj)
        if (s_obj.url === requestObj.url && requestObj.time - s_obj.time < 500) {
          const message = '数据正在处理，请勿重复提交'
          console.warn(`[${s_obj.url}]: ${message}`)
          return Promise.reject(new Error(message))
        } else {
          sessionStorage.setItem('sessionObj', JSON.stringify(requestObj))
        }
      }
    }

    if (isEncrypt && (config.method === 'post' || config.method === 'put')) {
      // 生成AES密钥
      const aesKey = generateAesKey()
      config.headers['Encrypt-Key'] = rsaEncrypt(encryptBase64(aesKey))
      config.data =
        typeof config.data === 'object'
          ? encryptWithAes(JSON.stringify(config.data), aesKey)
          : encryptWithAes(config.data, aesKey)
    }

    return config
  },
  error => {
    console.error(error)
    return Promise.reject(error)
  },
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data
    if (res.code === 401) return Promise.reject('登录状态已过期，请重新登录')
    else if (res.code !== 200) return Promise.reject(res.msg || '未知错误')

    return res
  },
  error => {
    let message = '请求失败'
    const status = error.response?.status
    switch (status) {
      case 400:
        message = '请求格式有误，请检查输入'
        break
      case 401:
        message = '登录已过期，请重新登录'
        break
      case 403:
        message = '暂无权限，请联系管理员'
        break
      case 404:
        message = '请求的资源不存在'
        break
      case 500:
        message = '服务器开小差了，请稍后重试'
        break
      case 502:
        message = '网络连接异常，请检查网络'
        break
      case 503:
        message = '服务正在维护，请稍后再试'
        break
      case 504:
        message = '网络超时，请稍后重试'
        break
      default:
        if (error.code === 'ECONNABORTED') {
          message = '请求超时，请检查网络'
        } else if (!error.response) {
          message = '网络连接失败，请检查网络'
        } else {
          message = error.response?.data?.message || '操作失败，请稍后重试'
        }
    }
    ms.error(message)

    console.error('请求错误', error)
    return Promise.reject(error.message)
  },
)

// API接口
export interface LoginData {
  tenantId: string
  username: string
  password: string
  code: string
  uuid: string
  clientId?: string
  grantType?: string
  phonenumber?: string
  smsCode?: string
  invitationCode?: string
  captchaVerification?: string
}

export interface TenantVO {
  tenantId: string
  companyName: string
}

export interface LoginResult {
  token: string
  [key: string]: any
}

export interface UserInfo {
  userId: number
  username: string
  nickname: string
  avatar?: string
  roles: string[]
  permissions: string[]
  [key: string]: any
}

export interface VerifyCodeResult {
  captchaEnabled: boolean
  img: string
  conversationId: string
}

export interface TenantListResult {
  tenantEnabled: boolean
  voList: TenantVO[]
}

/**
 * TenantListVo，租户列表
 */
export interface TenantListVo {
  /**
   * 企业名称
   */
  companyName?: string
  /**
   * 域名
   */
  domain?: string
  /**
   * 租户编号
   */
  tenantId?: string
  [property: string]: any
}

/**
 * 登录方法
 */
export function login(data: LoginData): Promise<AxiosResponse<LoginResult>> {
  const params = {
    ...data,
    clientId: data.clientId || import.meta.env.VITE_APP_CLIENT_ID || 'web',
    grantType: data.grantType || 'password',
  }
  return service({
    url: '/auth/login',
    headers: {
      isToken: false,
      isEncrypt: true,
      repeatSubmit: false,
    },
    method: 'post',
    data: params,
  })
}

/**
 * 获取用户信息
 */
export function getUserInfo(): Promise<AxiosResponse<UserInfo>> {
  return service({
    url: '/system/user/getInfo',
    method: 'get',
  })
}

/**
 * 获取验证码
 */
export function getCodeImg(): Promise<AxiosResponse<VerifyCodeResult>> {
  return service({
    url: '/auth/code',
    headers: {
      isToken: false,
    },
    method: 'get',
    timeout: 20000,
  })
}

/**
 * 获取租户列表
 */
export function getTenantList(): Promise<AxiosResponse<TenantListResult>> {
  return service({
    url: '/auth/tenant/list',
    headers: {
      isToken: false,
    },
    method: 'get',
  })
}

/**
 * 注销
 */
export function logout() {
  service({
    url: '/resource/sse/close',
    method: 'get',
  })
  return service({
    url: '/auth/logout',
    method: 'post',
  })
}

// 查询已加入的企业
// /auth/choose
export function getChooseTenantList(): Promise<AxiosResponse<TenantListVo>> {
  return service({
    url: '/auth/choose',
    method: 'get',
  })
}

// 选择企业 /auth/dynamic/{tenantId}  get
export function chooseTenant(tenantId: string): Promise<AxiosResponse<any>> {
  return service({
    url: `/auth/dynamic/${tenantId}`,
    method: 'get',
  })
}

// 查询邀请码所属企业 /auth/invitationCode/{code} get
export function getInvitationCode(code: string): Promise<AxiosResponse<TenantListVo>> {
  return service({
    url: `/auth/invitationCode/${code}`,
    method: 'get',
  })
}

// 短信验证码 /resource/sms/code GET
export function getSmsCode(data?: { phonenumber?: string }): Promise<AxiosResponse<any>> {
  return service({
    url: `/resource/sms/code`,
    method: 'get',
    params: data,
  })
}

// resolved 根据md内容写一下对应接口

// 滑块验证码相关接口类型定义
export interface CheckCaptchaData {
  needCaptcha: boolean
  failureCount: number
  message: string
}

export interface CaptchaGetRequest {
  captchaType: string
  clientUid?: string
}

export interface CaptchaPoint {
  x: number
  y: number
}

export interface CaptchaGetResult {
  repCode: string
  repMsg: string | null
  repData: {
    captchaId: string | null
    projectCode: string | null
    captchaType: string | null
    captchaOriginalPath: string | null
    captchaFontType: string | null
    captchaFontSize: string | null
    secretKey: string
    originalImageBase64: string
    point: CaptchaPoint | null
    jigsawImageBase64: string
    wordList: string | null
    pointList: string | null
    pointJson: string | null
    token: string
    result: boolean
    captchaVerification: string | null
    clientUid: string | null
    ts: string | null
    browserInfo: string | null
  }
  success: boolean
}

export interface CaptchaCheckRequest {
  captchaType: string
  pointJson: string
  token: string
  clientUid?: string
  ts?: string
}

export interface CaptchaCheckResult {
  repCode: string
  success: boolean
  [key: string]: any
}

/**
 * 判断是否需要滑块验证
 */
export function checkCaptcha(
  usernameOrPhone: string,
  tenantId: string,
): Promise<AxiosResponse<CheckCaptchaData>> {
  return service({
    url: '/auth/checkCaptcha',
    method: 'get',
    params: {
      usernameOrPhone,
      tenantId,
    },
    headers: {
      isToken: false,
    },
  })
}

/**
 * 获取滑块验证码
 */
export function getCaptcha(data: CaptchaGetRequest): Promise<AxiosResponse<CaptchaGetResult>> {
  return service({
    url: '/captcha/get',
    method: 'post',
    data,
    headers: {
      isToken: false,
    },
  })
}

/**
 * 校验滑块验证码
 */
export function checkCaptchaVerification(
  data: CaptchaCheckRequest,
): Promise<AxiosResponse<CaptchaCheckResult>> {
  return service({
    url: '/captcha/check',
    method: 'post',
    data,
    headers: {
      isToken: false,
    },
  })
}

export default service
