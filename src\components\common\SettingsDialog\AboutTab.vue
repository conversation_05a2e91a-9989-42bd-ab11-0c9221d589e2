<script setup lang="ts">
// 关于组件
</script>

<template>
  <div>
    <div class="section-header">
      <div class="section-title">关于</div>
    </div>
    <div class="tit">版本</div>
    <div class="profile-item">
      <div class="item-label flex items-center flex-1">
        <img class="w-[64px] h-[64px]" src="@/assets/blue_logo.png" alt="" />
        <div>
          <div class="g-c mt-2">易慧科技</div>
          <div>v1.0.0</div>
        </div>
      </div>
      <a target="_blank" href="//demo.easeidea.com" class="item-value g-c cursor-pointer">
        官方网站
        <div class="cursor-pointer ml-2">
          <i class="iconfont icon-qianwang iconfont-c !text-[20px]"></i>
        </div>
      </a>
    </div>
  </div>
</template>

<style scoped>
.tit {
  margin-top: 10px;
  margin-bottom: 10px;
  font-size: 14px;
  color: #606266;
}

.profile-item {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.item-label {
  color: #606266;
}
</style>
