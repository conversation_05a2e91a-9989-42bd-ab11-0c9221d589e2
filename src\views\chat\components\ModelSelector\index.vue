<script setup lang="ts">
import { ref, computed } from 'vue' // 添加 ref 和 computed
import { ElInput, ElIcon } from 'element-plus' // 导入 ElInput 和 ElIcon
import { CaretBottom, CaretTop, Search } from '@element-plus/icons-vue' // 导入图标
import { updateBizAiModelPinned } from '@/api/model'
import { vAutoAnimate } from '@formkit/auto-animate/vue'
import { getModelLogo } from '@/utils/models'

defineOptions({
  name: 'ModelSelector',
})

const props = defineProps<{
  modelList: any[]
  loading: boolean
  disabled?: boolean
}>()

const emits = defineEmits(['change', 'update'])

const currentModel = defineModel<string>({ default: '' })

const isOpen = ref(false) // 新增展开状态
const searchQuery = ref('') // 新增搜索状态

const isFocus = ref(false)

const selectModel = (model: any) => {
  currentModel.value = model.value
  isOpen.value = false
  emits('change', model.value)
}

const selectedModel = computed(() => {
  return props.modelList.find(model => String(model.value) === String(currentModel.value))
})

// 固定模型列表
const fixedModels = computed(() => {
  return props.modelList.filter(model => model.pinned)
})

/**
 * 根据模型平台分组模型列表
 */
function groupModelsByPlatform(models: any[]) {
  const platformList = models.map(model => model.platformName)

  const uniquePlatformList = platformList.filter(
    (item, index) => platformList.indexOf(item) === index,
  )

  return uniquePlatformList.map(platformName => ({
    platformName,
    list: models.filter(model => model.platformName === platformName),
  }))
}

/**
 * 未固定模型列表
 */
const unFixedModels = computed(() => {
  const list = props.modelList.filter(model => !model.pinned)
  return groupModelsByPlatform(list)
})

// 过滤模型列表
const filteredModels = computed(() => {
  if (!searchQuery.value) return props.modelList.filter(model => !model.pinned)
  const query = searchQuery.value.toLowerCase()
  const list = props.modelList.filter(
    model => model.label.toLowerCase().includes(query) || model.value.toLowerCase().includes(query),
  )
  return groupModelsByPlatform(list)
})

function toggleFixed(model: any) {
  updateBizAiModelPinned({
    id: model.modelId,
    pinned: model.pinned === true ? 0 : 1,
  }).then(res => {
    if (res.code === 200) {
      // 刷新列表
      emits('update')
    } else {
      ElMessage.error('操作失败')
    }
  })
}
</script>

<template>
  <el-popover
    popper-class="model-popover"
    placement="top-start"
    :width="600"
    trigger="click"
    :disabled="disabled"
    v-model:visible="isOpen"
    @hide="searchQuery = ''"
  >
    <template #reference>
      <div class="trigger" tabindex="0" :class="{ 'is-disabled': disabled }">
        <img v-if="selectedModel" :src="getModelLogo(selectedModel?.value)" class="model-logo" />
        <span class="model-name">{{ selectedModel?.label || '选择模型' }}</span>
        <el-icon v-show="!isOpen"><CaretBottom /></el-icon>
        <el-icon v-show="isOpen"><CaretTop /></el-icon>
      </div>
    </template>
    <div class="dropdown-menu" v-loading="loading">
      <!-- 搜索框 -->
      <div class="search-box" :class="{ 'is-focus': isFocus }">
        <el-input
          class="model-search"
          v-model="searchQuery"
          placeholder="搜索模型"
          clearable
          @click.stop
          @focus="isFocus = true"
          @blur="isFocus = false"
        />
        <el-icon class="search-icon" size="18"><Search /></el-icon>
      </div>
      <div class="model-list" v-auto-animate>
        <template v-if="searchQuery.length === 0">
          <!-- 固定模型列表 -->
          <div class="fixed-section" v-if="fixedModels.length > 0">
            <div class="text-[#646A73] text-[16px] mb-[12px]">已固定</div>
            <div
              v-for="model in fixedModels"
              :key="'fixed-' + model.value"
              class="menu-item fixed-item"
              :class="{ active: model.value === currentModel }"
              @click="selectModel(model)"
            >
              <div>
                <img
                  v-if="getModelLogo(model?.value)"
                  :src="getModelLogo(model?.value)"
                  class="model-logo"
                />
                {{ model.label }}
                <span class="text-[#b6b7b7] text-[12px]">
                  |
                  {{ model.platformName }}
                </span>
              </div>
              <div class="model-features">
                <el-tooltip
                  v-if="model.capabilities?.includes('reasoning')"
                  effect="dark"
                  placement="top"
                  content="推理"
                >
                  <img
                    class="inline-block w-[32px] h-[20px] mr-2"
                    src="@/assets/model/tuili.svg"
                    alt=""
                  />
                </el-tooltip>

                <el-tooltip
                  v-if="model.capabilities?.includes('vision')"
                  effect="dark"
                  placement="top"
                  content="视觉"
                >
                  <img
                    class="inline-block w-[32px] h-[20px] mr-2"
                    src="@/assets/model/shijue.svg"
                    alt=""
                  />
                </el-tooltip>

                <el-tooltip
                  v-if="model.capabilities?.includes('toolCalling')"
                  effect="dark"
                  placement="top"
                  content="工具"
                >
                  <img
                    class="inline-block w-[32px] h-[20px] mr-2"
                    src="@/assets/model/gongju.svg"
                    alt=""
                  />
                </el-tooltip>
                <img
                  class="fixed-icon cursor-pointer"
                  src="@/assets/chat/fixed.png"
                  alt=""
                  @click.stop="toggleFixed(model)"
                />
              </div>
            </div>
          </div>

          <div class="mb-6" v-for="item in unFixedModels">
            <div class="text-[#646A73] text-[16px] mb-[12px]">{{ item.platformName }}</div>
            <div
              v-for="model in item.list"
              :key="model.value"
              class="menu-item"
              :class="{ active: model.value === currentModel }"
              @click="selectModel(model)"
            >
              <div>
                <img
                  v-if="getModelLogo(model?.value)"
                  :src="getModelLogo(model?.value)"
                  class="model-logo"
                />
                {{ model.label }}
              </div>
              <div class="model-features">
                <el-tooltip
                  v-if="model.capabilities?.includes('reasoning')"
                  effect="dark"
                  placement="top"
                  content="推理"
                >
                  <img
                    class="inline-block w-[32px] h-[20px] mr-2"
                    src="@/assets/model/tuili.svg"
                    alt=""
                  />
                </el-tooltip>

                <el-tooltip
                  v-if="model.capabilities?.includes('vision')"
                  effect="dark"
                  placement="top"
                  content="视觉"
                >
                  <img
                    class="inline-block w-[32px] h-[20px] mr-2"
                    src="@/assets/model/shijue.svg"
                    alt=""
                  />
                </el-tooltip>

                <el-tooltip
                  v-if="model.capabilities?.includes('toolCalling')"
                  effect="dark"
                  placement="top"
                  content="工具"
                >
                  <img
                    class="inline-block w-[32px] h-[20px] mr-2"
                    src="@/assets/model/gongju.svg"
                    alt=""
                  />
                </el-tooltip>
                <img
                  class="fixed-icon cursor-pointer"
                  src="@/assets/chat/no-fixed.png"
                  alt=""
                  @click.stop="toggleFixed(model)"
                />
              </div>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="mb-6" v-for="item in filteredModels">
            <div class="text-[#646A73] text-[16px] mb-[12px]">{{ item.platformName }}</div>
            <div
              v-for="model in item.list"
              :key="model.value"
              class="menu-item"
              :class="{ active: model.value === currentModel }"
              @click="selectModel(model)"
            >
              <div>
                <img
                  v-if="getModelLogo(model?.value)"
                  :src="getModelLogo(model?.value)"
                  class="model-logo"
                />
                {{ model.label }}
              </div>
              <div class="model-features">
                <el-tooltip
                  v-if="model.capabilities?.includes('reasoning')"
                  effect="dark"
                  placement="top"
                  content="推理"
                >
                  <img
                    class="inline-block w-[32px] h-[20px] mr-2"
                    src="@/assets/model/tuili.svg"
                    alt=""
                  />
                </el-tooltip>

                <el-tooltip
                  v-if="model.capabilities?.includes('vision')"
                  effect="dark"
                  placement="top"
                  content="视觉"
                >
                  <img
                    class="inline-block w-[32px] h-[20px] mr-2"
                    src="@/assets/model/shijue.svg"
                    alt=""
                  />
                </el-tooltip>

                <el-tooltip
                  v-if="model.capabilities?.includes('toolCalling')"
                  effect="dark"
                  placement="top"
                  content="工具"
                >
                  <img
                    class="inline-block w-[32px] h-[20px] mr-2"
                    src="@/assets/model/gongju.svg"
                    alt=""
                  />
                </el-tooltip>
              </div>
            </div>
          </div>
          <el-empty v-if="filteredModels.length === 0" description="未查询到相关模型" />
        </template>

        <el-empty v-if="!searchQuery && modelList.length === 0" description="暂无数据" />
      </div>
    </div>
  </el-popover>
</template>

<style lang="less" scoped>
.trigger {
  width: 300px;
  display: flex;
  align-items: center;
  padding: 8px 12px;
  // border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  height: 32px; /* 确保高度与 NSelect 默认高度一致 */
  box-sizing: border-box; /* 包含 padding 和 border 在宽度内 */
  background: #e6eaf4;

  &:hover {
    border-color: #a0cfff;
    background: #f0f7ff;
    transition: all 0.2s;
  }

  .model-logo {
    width: 20px;
    height: 20px;
    margin-right: 8px;
    flex-shrink: 0; /* 防止图片缩小 */
  }

  .model-name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; /* 防止文本换行 */
  }

  .arrow-icon {
    transition: transform 0.3s;
    margin-left: 8px;
    font-size: 16px; /* 调整图标大小 */
    flex-shrink: 0; /* 防止图标缩小 */
  }

  &:focus {
    border-color: #409eff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    outline: none;
  }
  &:active {
    transform: scale(0.98);
    transition: transform 0.1s;
  }
}

.is-disabled {
  cursor: not-allowed;
  opacity: 0.6;
  background: #f5f5f5 !important;
  &:hover {
    border-color: #dcdfe6 !important;
    background: #f5f5f5 !important;
  }
  &:focus {
    border-color: #dcdfe6 !important;
    box-shadow: none;
    outline: none;
  }
  &:active {
    transform: scale(1);
  }
}

.dropdown-menu {
  .search-box {
    position: relative;
    padding-right: 24px;
    margin-bottom: 24px;
    border-bottom: 1px solid #e8e8e8;
    &.is-focus {
      border-color: #409eff;
    }

    &:hover {
      border-color: #409eff;
    }

    .model-search:deep(.el-input__wrapper) {
      box-shadow: none;

      &.is-focus {
        box-shadow: none;
      }
    }

    .search-icon {
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  .model-list {
    // max-height: 720px;
    min-height: 450px;
    max-height: min(70vh, 500px);
    overflow-y: auto;
  }

  .fixed-section {
    // border-bottom: 1px solid #f0f0f0;
    padding-bottom: 24px; /* 增加底部内边距 */
    .fixed-item {
      &:hover {
        background: #e6eaf4;
      }
    }
  }

  .divider {
    height: 1px;
    background: #f0f0f0;
    margin: 8px 0;
  }

  .menu-item {
    padding: 8px;
    margin-right: 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &.active {
      background-color: #ecf5ff;
      border: 1px solid #d9ecff;
      color: #409eff;
      border-radius: 8px;
    }

    .fixed-icon {
      display: inline-block;
      width: 22px;
      visibility: hidden;
    }

    &:hover {
      background: #e6eaf4;
      border-radius: 8px;

      .fixed-icon {
        visibility: visible;
      }
    }

    .model-logo {
      display: inline-block;
      width: 40px;
      height: 40px;
      margin-right: 8px;
    }

    .model-features {
    }
  }
}
</style>

<style>
.model-popover.el-popover.el-popper {
  background: rgba(255, 255, 255, 0.85);
  box-shadow: 0px 2px 8px 2px rgba(0, 0, 0, 0.15);
  border-radius: 16px;
  backdrop-filter: blur(8px);
}
</style>
