<script setup lang="ts">
const props = defineProps<{
  fileUrl: string // 在线文件地址
  defaultPdfMode?: boolean // 是否默认使用PDF模式预览
}>()

const loading = ref(true)
const error = ref('')
const previewUrl = ref('')

onMounted(() => {
  if (!props.fileUrl) {
    error.value = '文件地址不能为空'
    loading.value = false
    return
  }
  // 假设 kkview 服务的预览 URL 格式如下：
  // http://your-kkview-server/preview?url=<fileUrl>
  // 这里可根据实际部署的 kkview 服务调整
  // http://************:8888/onlinePreview?url='+encodeURIComponent(Base64.encode(previewUrl))
  let url = `${import.meta.env.VITE_APP_FILE_PREVIEW_URL}/onlinePreview?url=${encodeURIComponent(btoa(props.fileUrl))}`

  // 如果需要默认使用PDF模式预览，添加相应参数
  if (props.defaultPdfMode) {
    url += '&officePreviewType=pdf'
  }

  previewUrl.value = url
  loading.value = false
})
</script>

<template>
  <div class="kkview-preview-container">
    <div v-if="loading" class="kkview-preview-loading">
      <div class="loader-wrap">
        <span class="loader ml-2"></span>
      </div>
    </div>
    <div v-else-if="error" class="kkview-preview-error">{{ error }}</div>
    <iframe
      v-else
      :src="previewUrl"
      frameborder="0"
      style="width: 100%; height: 100%; min-height: 500px"
      allowfullscreen
    ></iframe>
  </div>
</template>

<style scoped>
.kkview-preview-container {
  width: 100%;
  height: 100%;
  min-height: 500px;
}
.kkview-preview-loading,
.kkview-preview-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #888;
}
</style>

