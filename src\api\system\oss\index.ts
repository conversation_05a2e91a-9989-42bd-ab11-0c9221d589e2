import type { AxiosPromise } from 'axios'
import type { OssQuery, OssVO } from './types'
import request from '@/utils/request'
import {get} from '@/utils/request'


// 查询OSS对象存储列表
export function listOss(query: OssQuery): AxiosPromise<OssVO[]> {
  return request({
    url: '/resource/oss/list',
    method: 'get',
    params: query,
  })
}

// 查询OSS对象基于id串
export function listByIds(ossId: string | number) {
  return get<OssVO[]>({
    url: `/resource/oss/listByIds/${ossId}`,
  })
}

// 删除OSS对象存储
export function delOss(ossId: string | number | Array<string | number>) {
  return request({
    url: `/resource/oss/${ossId}`,
    method: 'delete',
  })
}
