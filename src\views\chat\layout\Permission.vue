<script setup lang="ts">
import { computed, ref } from 'vue'
import { NButton, NInput, NModal } from 'naive-ui'
import { useUserStore } from '@/store'
import { getCurrentToken, setRequestAgentContext } from '@/utils/token-manager'
import Icon403 from '@/icons/403.vue'
import ms from '@/utils/message'

interface Props {
  visible: boolean
}

defineProps<Props>()

const userStore = useUserStore()

const loading = ref(false)
const token = ref('')

const disabled = computed(() => !token.value.trim() || loading.value)

async function handleVerify() {
  const secretKey = token.value.trim()

  if (!secretKey) return

  try {
    loading.value = true

    // resolved 简单验证token格式（至少10个字符），然后直接设置token
    if (secretKey.length < 10) {
      throw new Error('Token格式不正确，请输入有效的访问密钥')
    }

    userStore.setUserToken(secretKey)
    ms.success('验证成功')
    window.location.reload()
  } catch (error: any) {
    ms.error(error.message ?? '验证失败，请检查访问密钥')
    userStore.setUserToken('')
    token.value = ''
  } finally {
    loading.value = false
  }
}

function handlePress(event: KeyboardEvent) {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    handleVerify()
  }
}
</script>

<template>
  <NModal :show="visible" style="width: 90%; max-width: 640px">
    <div class="p-10 bg-white rounded dark:bg-slate-800">
      <div class="space-y-4">
        <header class="space-y-2">
          <h2 class="text-2xl font-bold text-center text-slate-800 dark:text-neutral-200">403</h2>
          <p class="text-base text-center text-slate-500 dark:text-slate-500">
            {{ $t('common.unauthorizedTips') }}
          </p>
          <Icon403 class="w-[200px] m-auto" />
        </header>
        <NInput
          v-model:value="token"
          type="password"
          placeholder="请输入访问密钥"
          @keypress="handlePress"
        />
        <NButton block type="primary" :disabled="disabled" :loading="loading" @click="handleVerify">
          {{ $t('common.verify') }}
        </NButton>
      </div>
    </div>
  </NModal>
</template>
