import { defineStore } from 'pinia'
import type { DefaultModelState, SettingsState } from './helper'
import { defaultSetting, getLocalState, removeLocalState, setLocalState } from './helper'
import { getBizAiModelDefault } from '@/api'

// 默认缓存过期时间（5分钟）
const CACHE_EXPIRATION_TIME = 5 * 60 * 1000

export const useSettingStore = defineStore('setting-store', {
  state: (): SettingsState => getLocalState(),

  getters: {
    // 检查默认模型数据缓存是否有效
    hasValidDefaultModel(): boolean {
      return (
        this.defaultModel.chatModelId !== null &&
        Date.now() - this.defaultModel.lastFetchTime < CACHE_EXPIRATION_TIME
      )
    },
  },

  actions: {
    updateSetting(settings: Partial<SettingsState>) {
      this.$state = { ...this.$state, ...settings }
      this.recordState()
    },

    resetSetting() {
      this.$state = defaultSetting()
      removeLocalState()
    },

    recordState() {
      setLocalState(this.$state)
    },

    // 获取默认模型配置
    async fetchDefaultModel(forceRefresh = false): Promise<DefaultModelState | null> {
      // 如果强制刷新或者缓存无效，则重新获取
      if (forceRefresh || !this.hasValidDefaultModel) {
        try {
          const response = await getBizAiModelDefault()
          // console.log('getBizAiModelDefault')
          const modelData = response.data

          // 更新默认模型数据
          this.defaultModel = {
            ...this.defaultModel,
            ...modelData,
            lastFetchTime: Date.now(),
          }

          this.recordState()
          return this.defaultModel
        } catch (error) {
          console.error('获取默认模型失败:', error)
          return null
        }
      }

      return this.defaultModel
    },
  },
})
