const path = require('path')
const { app, BrowserWindow, ipcMain, session } = require('electron')
// 使用 app.isPackaged 来判断是否为开发环境
const isDev = !app.isPackaged

// 只在开发环境加载环境变量
if (isDev) {
  const dotenv = require('dotenv')
  const envPath = path.join(__dirname, '../.env')
  const envConfig = dotenv.config({ path: envPath })
  console.log('Environment variables:', {
    isDev,
    envPath,
    envConfig: envConfig.parsed,
  })
}
else {
  // 这里
}

function createWindow() {
  // 创建浏览器窗口
  const mainWindow = new BrowserWindow({
    width: 1600,
    height: 900,
    autoHideMenuBar: true, // 自动隐藏菜单栏
    // menuBarVisible: false // 直接不显示菜单栏
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      webSecurity: false,
    },
    // 设置应用图标
    icon: path.join(__dirname, '../public/electron-desk.png'),
  })

  // todo 这种方式正常吗
  // 设置请求拦截
  if (!isDev) {
    session.defaultSession.webRequest.onBeforeRequest({ urls: ['file:///*/prod-api/*'] },
      (details, callback) => {
        // 使用正则表达式匹配并替换URL
        const newUrl = details.url.replace(/^file:\/\/\/.*?prod-api\//, 'http://************:7070/')
        callback({ redirectURL: newUrl })
      })
  }

  // 加载应用
  if (isDev)
    mainWindow.loadURL('http://localhost:1002') // 修改为与 vite.config.ts 中配置的端口一致
  else
    mainWindow.loadFile(path.join(__dirname, '../dist/index.html'))

  // 开发环境下打开开发者工具
  // if (isDev)
  //   mainWindow.webContents.openDevTools()
}

// 当 Electron 完成初始化时创建窗口
app.whenReady().then(() => {
  createWindow()

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0)
      createWindow()
  })
})

// 当所有窗口关闭时退出应用
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin')
    app.quit()
})

// 在这里可以添加与渲染进程的通信处理
ipcMain.on('message-to-main', (event, message) => {
  console.log('Received message from renderer:', message)
  event.reply('message-from-main', 'Message received!')
})
