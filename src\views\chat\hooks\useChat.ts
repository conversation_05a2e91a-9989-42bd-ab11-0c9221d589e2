import { useChatStore } from '@/store'

export function useChat() {
  const chatStore = useChatStore()

  const getChatByConversationIdAndIndex = (conversationId: string, index: number) => {
    return chatStore.getChatByConversationIdAndIndex(conversationId, index)
  }

  const addChat = (conversationId: string, chat: Chat.Chat, isHistory = false) => {
    chatStore.addChatByConversationId(conversationId, chat, isHistory)
  }

  const prependChat = (conversationId: string, chat: Chat.Chat) => {
    chatStore.prependChatByConversationId(conversationId, chat)
  }

  const updateChat = (conversationId: string, index: number, chat: Chat.Chat) => {
    chatStore.updateChatByConversationId(conversationId, index, chat)
  }

  const updateChatSome = (conversationId: string, index: number, chat: Partial<Chat.Chat>) => {
    chatStore.updateChatSomeByConversationId(conversationId, index, chat)
  }

  return {
    addChat,
    prependChat,
    updateChat,
    updateChatSome,
    getChatByConversationIdAndIndex,
  }
}
