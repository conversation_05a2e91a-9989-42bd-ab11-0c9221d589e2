<script setup lang="ts">
import { globalHeaders } from '@/utils/request/axios'
import { Platform } from '@/api/model-base/types'
import { UploadRawFile, ElMessage, genFileId } from 'element-plus'
import type { FormInstance, FormRules, UploadInstance, UploadProps } from 'element-plus'
import { IMAGE_MIME_TYPES, MAX_IMAGE_SIZE } from '../constant/file'
import { addAiPlatform, updateAiPlatform } from '@/api/model-base'
import PlatformLogo from './PlatformLogo.vue'

defineOptions({
  name: 'ModelPlatformDialog',
})

const emits = defineEmits(['updateInfo'])
const baseUrl = import.meta.env.VITE_APP_BASE_API
const uploadFileUrl = ref(`${baseUrl}/resource/oss/upload`) // 上传文件服务器地址

const headers = globalHeaders()

const dialogVisible = defineModel({ default: false })
const title = ref('添加模型平台')
const platformLogo = ref('')
const ossId = ref<string>('')
const platformCode = ref('openai')
// 图片上传 loading
const uploadLoading = ref(false)
const id = ref('')
const submitLoading = ref(false)
const ruleFormRef = ref<FormInstance>()
const form = reactive({
  platformName: '',
})
interface RuleForm {
  platformName: string
}
const rules = reactive<FormRules<RuleForm>>({
  platformName: [{ required: true, message: '请填写平台名称', trigger: 'blur' }],
})

/**
 * 重置头像
 */
function resetAvatar() {
  platformLogo.value = ''
  ossId.value = ''
}

const upload = ref<UploadInstance>()
const handleExceed: UploadProps['onExceed'] = files => {
  upload.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  upload.value!.handleStart(file)
  upload.value!.submit()
}

/**
 * 文件上传前校验
 * @param rawFile - 待上传的原始文件对象
 * @returns 是否通过校验 (true: 通过, false: 拒绝)
 */
function handleBeforeUpload(rawFile: UploadRawFile): boolean {
  // 1. 基本类型检查
  if (!rawFile || !(rawFile instanceof File)) {
    ElMessage.error('无效的文件对象')
    return false
  }

  // 2. 文件类型双重校验
  if (!IMAGE_MIME_TYPES.includes(rawFile.type as any)) {
    const allowedTypes = IMAGE_MIME_TYPES.map(t => t.split('/')[1]).join(', ')
    ElMessage.error(`仅支持 ${allowedTypes} 格式的图片文件`)
    return false
  }

  // 3. 文件大小校验
  if (rawFile.size > MAX_IMAGE_SIZE) {
    const sizeMB = (MAX_IMAGE_SIZE / 1024 / 1024).toFixed(2)
    ElMessage.error(`文件大小不能超过 ${sizeMB}MB`)
    return false
  }
  uploadLoading.value = true

  return true
}

/**
 * 重置表单数据
 */
function resetForm() {
  if (ruleFormRef.value) {
    ruleFormRef.value.resetFields()
    id.value = ''
    title.value = '添加模型平台'
  }
}

function handleBeforeClose(done: () => void) {
  if (!submitLoading.value) {
    done()
  }
}

function handleClose() {
  resetAvatar()
  resetForm()
}

/**
 * 提交平台
 */
function handleSubmit() {
  if (ruleFormRef.value) {
    ruleFormRef.value.validate(valid => {
      if (valid) {
        interface Params {
          id?: string
          platformCode: string
          platformName: string
          icon: string
          ossId: string
        }
        const params: Params = {
          platformCode: platformCode.value,
          platformName: form.platformName,
          icon: platformLogo.value,
          ossId: ossId.value,
        }
        if (id.value) {
          params.id = id.value
        }
        const submitMethod = id.value ? updateAiPlatform : addAiPlatform
        submitLoading.value = true
        submitMethod(params)
          .then(res => {
            if (res.code === 200) {
              ElMessage.success('保存成功')
              emits('updateInfo', id.value)
              // handleClose()
              dialogVisible.value = false
            } else {
              ElMessage.error(res.msg || '保存失败')
            }
          })
          .finally(() => {
            submitLoading.value = false
          })
      }
    })
  }
}

/**
 * 回填平台信息
 */
function editPlatformInfo(platform: Platform) {
  dialogVisible.value = true
  nextTick(() => {
    form.platformName = platform.platformName
    platformLogo.value = platform.icon
    ossId.value = platform.ossId
    platformCode.value = platform.platformCode
    id.value = platform.id
    title.value = '编辑模型平台'
  })
}

/**
 * 头像修改
 */
function handleUploadSuccess(res: any) {
  if (res.code === 200) {
    platformLogo.value = res.data.url
    ossId.value = res.data.ossId
  } else {
    ElMessage.error('图片上传失败')
  }
  uploadLoading.value = false
}

function handleUploadError() {
  console.log('图片上传失败')
  uploadLoading.value = false
}

defineExpose({
  editPlatformInfo,
})
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    width="600"
    :close-on-click-modal="false"
    :beforeClose="handleBeforeClose"
    @close="handleClose"
  >
    <template #header>
      <span class="text-[24px] g-family-medium">{{ title }}</span>
    </template>
    <div class="text-center">
      <el-dropdown placement="bottom">
        <PlatformLogo
          :loading="uploadLoading"
          :platformName="form.platformName"
          :logoSrc="platformLogo"
        />
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item>
              <el-upload
                ref="upload"
                :action="uploadFileUrl"
                accept=".jpg,.jpeg,.png,.webg"
                :show-file-list="false"
                :limit="1"
                :headers="headers"
                :on-exceed="handleExceed"
                :beforeUpload="handleBeforeUpload"
                :on-success="handleUploadSuccess"
                :on-error="handleUploadError"
              >
                上传头像
              </el-upload>
            </el-dropdown-item>
            <el-dropdown-item @click="resetAvatar">重置头像</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <el-form
      ref="ruleFormRef"
      :model="form"
      :rules="rules"
      label-width="auto"
      hide-required-asterisk
      label-position="top"
    >
      <el-form-item prop="platformName">
        <template #label>
          <div class="g-family-medium text-[16px]">平台名称</div>
        </template>
        <el-input
          placeholder="请输入"
          v-model="form.platformName"
          @keyup.enter.native="handleSubmit"
        />
      </el-form-item>
    </el-form>
    <div class="text-[#969BA4] text-[16px] mt-3 mb-10">
      目前仅支持兼容 OpenAI API 格式的模型平台
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
