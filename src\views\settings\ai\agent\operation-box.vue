<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import Operation from '@/views/agent/my-agent/operation.vue'

const route = useRoute()
const router = useRouter()

// 从路由参数获取模式和ID
// 路由包含/settings/ai则为'review'
const isReview = computed(() => (route.path.includes('/settings/ai') ? 'review' : 'create'))
const agentId = computed(() => route.query.id)

// 处理成功回调
const handleSuccess = () => {
  router.push('/settings/ai/agent')
}
</script>

<template>
  <Operation :is-review="isReview" :agent-id="agentId" @on-success="handleSuccess" />
</template>

<style lang="less" scoped></style>
