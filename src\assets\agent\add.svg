<?xml version="1.0" encoding="UTF-8"?>
<svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>矩形</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="新增助手" transform="translate(-1811.000000, -1004.000000)" fill="#646A73" fill-rule="nonzero">
            <g id="icon_添加" transform="translate(1811.000000, 1004.000000)">
                <rect id="矩形" opacity="0" x="0" y="0" width="20" height="20"></rect>
                <path d="M10,1.625 C14.6252333,1.625 18.375,5.37476667 18.375,10 C18.375,14.6252333 14.6252333,18.375 10,18.375 C5.37476667,18.375 1.625,14.6252333 1.625,10 C1.625,5.37476667 5.37476667,1.625 10,1.625 Z M10,2.74166667 C5.99116667,2.74166667 2.74166667,5.99116667 2.74166667,10 C2.74166667,14.0088333 5.99116667,17.2583333 10,17.2583333 C14.0088333,17.2583333 17.2583333,14.0088333 17.2583333,10 C17.2583333,5.99116667 14.0088333,2.74166667 10,2.74166667 Z M10,4.975 C10.308359,4.975 10.5583333,5.22497435 10.5583333,5.53333333 L10.558,9.441 L14.4666667,9.44166667 C14.7750257,9.44166667 15.025,9.69164101 15.025,10 C15.025,10.308359 14.7750257,10.5583333 14.4666667,10.5583333 L10.558,10.558 L10.5583333,14.4666667 C10.5583333,14.7750257 10.308359,15.025 10,15.025 C9.69164101,15.025 9.44166667,14.7750257 9.44166667,14.4666667 L9.441,10.558 L5.53333333,10.5583333 C5.22497435,10.5583333 4.975,10.308359 4.975,10 C4.975,9.69164101 5.22497435,9.44166667 5.53333333,9.44166667 L9.441,9.441 L9.44166667,5.53333333 C9.44166667,5.22497435 9.69164101,4.975 10,4.975 Z" id="形状结合"></path>
            </g>
        </g>
    </g>
</svg>